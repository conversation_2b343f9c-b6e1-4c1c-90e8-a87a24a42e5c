package com.jlr.ecp.subscription.api.incontrol.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class InControlConsumerVehicleDTO {

    /**
     * 车辆VIN;车辆VIN，唯一索引
     */
    private String carVin;

    /**
     * incontrol账号;incontrol账号
     */
    private String incontrolId;

    /**
     * 车辆型号;车辆型号
     */
    private String seriesCode;

    /**
     * 车辆名称;车辆名称
     */
    private String seriesName;

    /**
     * 品牌CODE;品牌CODE; LALANDJAG
     */
    private String brandCode;

    /**
     * 品牌名;车型品牌
     */
    private String brandName;

    /**
     * House of Brand 英文描述描述;House of Brand 英文描述描述
     */
    private String hobEn;

    /**
     * 产品类型EN;产品类型EN
     */
    private String productionEn;

    /**
     * 配置编码;配置编码
     */
    private String configCode;

    /**
     * 配置名称;配置名称
     */
    private String configName;

    /**
     * 车辆年款;车辆年款
     */
    private String modelYear;

    /**
     * 车机型号;车机型号，PIVI，通过计算获得
     */
    private String carSystemModel;

    /**
     * 车主手机号
     */
    private String incontrolPhone;

    /**
     *  发票时间
     * */
    private LocalDateTime dmsInvoiceDate;

    /**
     * 绑定时间;绑定时间
     */
    private LocalDateTime bindTime;

    /**
     * 用户JLRID
     */
    private String consumerCode;

}
