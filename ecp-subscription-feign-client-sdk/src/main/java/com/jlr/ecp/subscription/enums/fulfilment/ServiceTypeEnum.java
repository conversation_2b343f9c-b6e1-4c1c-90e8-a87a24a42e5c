package com.jlr.ecp.subscription.enums.fulfilment;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 表示服务类型的枚举类。
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum ServiceTypeEnum {

    /**
     * 远程车控Remote Service
     */
    REMOTE(1, "InControl 远程车控"),

    /**
     * PIVI Subscription Service
     */
    PIVI(2, "InControl 在线服务");



    private final Integer code;
    private final String description;

}
