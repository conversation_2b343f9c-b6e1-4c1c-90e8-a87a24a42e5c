package com.jlr.ecp.subscription.api.icrvehicle;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.icrvehicle.dto.ValidateICRDTO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.security.PermitAll;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - icr vehicle 信息")
public interface IcrVehicleApi {
    String PREFIX = ApiConstants.PREFIX + "/icr/vehicle";

    @GetMapping(PREFIX + "/view")
    @Operation(summary = "通过carVin icr 车辆信息 查询接口")
    @Parameter(name = "carVin", description = "车辆vin")
    @PermitAll
    CommonResult<IcrVehicleRespVO> view(@RequestParam(value = "carVin") String carVin);


    @PostMapping(PREFIX + "/vinInfo")
    @Operation(summary = "通过carVin icr 车辆信息 查询接口")
    @Parameter(name = "carVin", description = "车辆vin")
    @PermitAll
    CommonResult<List<IcrVehicleListRespVO>> vinInfo(@RequestBody List<String> carVinList);

    @PostMapping(PREFIX + "/getByJlrIdAndVinList")
    @Operation(summary = "通过jlrId和vinList查询对应的ICR")
    @PermitAll
    CommonResult<Map<String, String>> getByJlrIdAndVinList(@RequestBody ValidateICRDTO validateICRDTO);
}
