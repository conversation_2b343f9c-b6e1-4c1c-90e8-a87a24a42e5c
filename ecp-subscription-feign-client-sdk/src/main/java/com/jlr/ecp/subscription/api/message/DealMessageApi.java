package com.jlr.ecp.subscription.api.message;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC服务 - 处理MQC消息")
public interface DealMessageApi {

    String PREFIX = ApiConstants.PREFIX + "/message";

    @GetMapping(PREFIX + "/getPreDealIdList")
    @Operation(summary = "查询待处理消息id列表")
    CommonResult<List<Long>> getPreDealIdList(@RequestParam(value = "status") Integer status);

    @PostMapping(PREFIX + "/dealMessage")
    @Operation(summary = "处理消息")
    CommonResult<Boolean> dealMessage(@RequestParam(value = "id") Long id);
}
