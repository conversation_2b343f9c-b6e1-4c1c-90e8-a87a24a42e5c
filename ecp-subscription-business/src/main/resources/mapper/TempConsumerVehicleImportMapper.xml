<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jlr.ecp.subscription.dal.mysql.temp.TempConsumerVehicleImportDOMapper">

    <!-- 查询已存在于 t_incontrol_vehicle 中的记录 -->
    <select id="selectExistsInVehicle" resultType="com.jlr.ecp.subscription.dal.dataobject.temp.TempConsumerVehicleImportDO">
        SELECT t.*
        FROM t_temp_consumer_vehicle_import t
        INNER JOIN t_incontrol_vehicle iv ON t.vin = iv.car_vin
        WHERE t.is_deleted = 0
          AND iv.is_deleted = 0
          AND t.import_status = #{importStatus}
    </select>


    <!-- 查询未存在于 t_incontrol_vehicle 中的记录 -->
    <select id="selectNotExistsInVehicle" resultType="com.jlr.ecp.subscription.dal.dataobject.temp.TempConsumerVehicleImportDO">
        SELECT t.*
        FROM t_temp_consumer_vehicle_import t
        LEFT JOIN t_incontrol_vehicle iv ON t.vin = iv.car_vin
        WHERE iv.car_vin IS NULL
          AND t.import_status = #{importStatus}
          AND t.is_deleted = 0
    </select>

</mapper>