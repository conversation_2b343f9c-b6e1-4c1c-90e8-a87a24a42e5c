package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.VehicleBindMessageDO;

import java.util.List;

/**
 * VehicleBindMessageDO Repository接口
 *
 */
public interface VehicleBindMessageRepository extends IService<VehicleBindMessageDO> {

    /**
     * 查询待处理消息数据
     *
     * @param status 状态
     * @return 分页结果
     */
    List<VehicleBindMessageDO> getPreDealIdList(Integer status);
}
