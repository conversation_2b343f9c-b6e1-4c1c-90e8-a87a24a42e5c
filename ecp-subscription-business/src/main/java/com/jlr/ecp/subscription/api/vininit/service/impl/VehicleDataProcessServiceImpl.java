package com.jlr.ecp.subscription.api.vininit.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Sets;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.remoteservice.RemoteOriginalDataParseBO;
import com.jlr.ecp.subscription.api.vininit.constants.VehicleProcessConstants;
import com.jlr.ecp.subscription.api.vininit.model.ProcessResult;
import com.jlr.ecp.subscription.api.vininit.service.VehicleDataPersistService;
import com.jlr.ecp.subscription.api.vininit.service.VehicleDataProcessService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.repository.IncontrolVehicleRepository;
import com.jlr.ecp.subscription.dal.repository.PIVIPackageRepository;
import com.jlr.ecp.subscription.dal.repository.SubscriptionServiceRepository;
import com.jlr.ecp.subscription.dal.repository.VehicleDmsRepository;
import com.jlr.ecp.subscription.enums.remote.RemoteDataErrorType;
import com.jlr.ecp.subscription.service.icrorder.SeriesBrandMappingDataDOService;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.remotepackage.RemotePackageDOService;
import com.jlr.ecp.subscription.service.remoteservice.RemoteOriginalDataService;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import com.jlr.ecp.subscription.util.PIPLDataUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertSet;

/**
 * 车辆数据处理服务实现
 *
 */
@Service
@Slf4j
public class VehicleDataProcessServiceImpl implements VehicleDataProcessService {
    
    @Resource
    private RemotePackageDOService remotePackageDOService;
    
    @Resource
    private RemoteOriginalDataService remoteOriginalDataService;
    
    @Resource
    private PIPLDataUtil piplDataUtil;
    
    @Resource
    private VehicleModelMasterDataService vehicleModelMasterDataService;
    
    @Resource
    private SeriesBrandMappingDataDOService seriesBrandMappingDataDOService;

    
    @Resource
    private IncontrolVehicleRepository incontrolVehicleRepository;
    
    @Resource
    private SubscriptionServiceRepository subscriptionServiceRepository;
    
    @Resource
    private VehicleDataPersistService vehicleDataPersistService;
    
    @Resource(name = "vehicleProcessExecutor")
    private ThreadPoolTaskExecutor vehicleProcessExecutor;

    @Resource
    private Snowflake ecpIdUtil;

    @Resource
    private PIVIPackageRepository piviPackageRepository;

    @Resource
    private VehicleDmsRepository vehicleDmsRepository;

    @Resource
    private VinInitializeService vinInitializeService;
    
    @Override
    public ProcessResult processData(List<RemoteOriginalDataParseBO> parsedDataList) {
        if (CollUtil.isEmpty(parsedDataList)) {
            log.warn("解析后数据为空，跳过处理");
            return ProcessResult.empty();
        }
        log.info("开始处理解析后数据，数据量: {}", parsedDataList.size());

        // 过滤非remote服务包, 并把非remote数据标识为成功
        ProcessResult filterResult = filterNonRemotePackages(parsedDataList);
        log.info("过滤非remote服务包完成, 非remote数据成功标记结果: {}, remote剩余数量: {}", filterResult.getSuccessCount(), CollUtil.size(parsedDataList));

        // 按车vin分组并分批处理
        ProcessResult batchResult = processByBatches(parsedDataList);

        // 合并过滤结果和批次处理结果
        return filterResult.merge(batchResult);
    }
    
    /**
     * 过滤非remote服务包, 并把非remote数据标识为成功
     */
    private ProcessResult filterNonRemotePackages(List<RemoteOriginalDataParseBO> parsedDataList) {
        Set<Long> totalIdSet = convertSet(parsedDataList, RemoteOriginalDataParseBO::getId);
        
        // 获取所有remote服务包代码
        List<String> remotePackages = remotePackageDOService.getAllRemotePackageCode();
        log.info("获取到remote服务包数量: {}", remotePackages.size());
        
        // 过滤非remote服务包数据
        parsedDataList.removeIf(e -> !remotePackages.contains(e.getServicePackage()));
        Set<Long> remoteIdSet = convertSet(parsedDataList, RemoteOriginalDataParseBO::getId);

        // 标记非remote数据为成功
        totalIdSet.removeAll(remoteIdSet); // 剩下的都是非remote数据
        if (CollUtil.isNotEmpty(totalIdSet)) {
            log.info("标记非remote数据为成功，数量: {}", totalIdSet.size());
            remoteOriginalDataService.updateDataSuccess(totalIdSet);
        }
        
        return ProcessResult.of(totalIdSet.size(), 0);
    }
    
    /**
     * 按批次处理数据
     */
    private ProcessResult processByBatches(List<RemoteOriginalDataParseBO> parsedDataList) {
        if (CollUtil.isEmpty(parsedDataList)) {
            log.info("按批次处理数据, 无有效数据处理");
            return ProcessResult.empty();
        }
        // 按vin分组
        Map<String, List<RemoteOriginalDataParseBO>> carServiceMap = parsedDataList.parallelStream()
                .collect(Collectors.groupingBy(RemoteOriginalDataParseBO::getCarVin));
        
        log.info("按车辆分组后，车辆数量: {}", CollUtil.size(carServiceMap));
        
        // 分批处理,每批次最多处理1000
        List<List<Map.Entry<String, List<RemoteOriginalDataParseBO>>>> splitList = 
            CollUtil.split(carServiceMap.entrySet(), VehicleProcessConstants.BatchConfig.DEFAULT_BATCH_SIZE);
        
        log.info("分批处理，批次数量: {}", splitList.size());
        
        // 异步处理各批次
        List<CompletableFuture<ProcessResult>> futureList = new ArrayList<>(splitList.size());
        
        for (List<Map.Entry<String, List<RemoteOriginalDataParseBO>>> entries : splitList) {
            List<RemoteOriginalDataParseBO> batchData = entries.parallelStream()
                .flatMap(entry -> entry.getValue().parallelStream())
                .collect(Collectors.toList());
            
            CompletableFuture<ProcessResult> future = CompletableFuture
                .supplyAsync(() -> processBatchData(batchData), vehicleProcessExecutor)
                .exceptionally(e -> handleBatchException(e, batchData));
            
            futureList.add(future);
        }
        
        // 等待所有批次完成并汇总结果
        return aggregateResults(futureList);
    }
    
    @Override
    public ProcessResult processBatchData(List<RemoteOriginalDataParseBO> batchData) {
        if (CollUtil.isEmpty(batchData)) {
            return ProcessResult.empty();
        }
        log.info("处理批次数据，数据量: {}", batchData.size());
        try {
            // 数据预处理
            BatchProcessContext context = preprocessBatchData(batchData);
            
            // PIPL数据加密
            encryptPIPLData(context);
            
            // 处理车辆数据
            processVehicleData(context);
            
            // 处理订阅服务数据
            processSubscriptionData(context);
            
            // 持久化数据
            return persistBatchData(context);
        } catch (Exception e) {
            log.info("批次数据处理异常", e);
            return handleBatchException(e, batchData);
        }
    }
    
    /**
     * 批次处理上下文
     */
    private static class BatchProcessContext {
        private final List<RemoteOriginalDataParseBO> batchData;
        private final Set<String> vinSet;
        private final Map<String, String> carPhoneMap;
        private final Map<String, String> carIcrMap;
        private final Map<String, Set<Long>> vinIdMap;
        private Map<String, String> encryptedDataMap;
        private final List<IncontrolVehicleDO> insetVehicles;
        private final Set<IncontrolCustomerDO> customerSet;
        private final List<SubscriptionServiceDO> serviceDOList;
        private Set<String> failVinSet;
        private final List<IncontrolVehicleDO> updateVehicles;
        
        public BatchProcessContext(List<RemoteOriginalDataParseBO> batchData) {

            this.batchData = batchData;
            this.vinSet = convertSet(batchData, RemoteOriginalDataParseBO::getCarVin);
            log.info("开始数据预处理, batchData数量:{}, VIN数量: {}", CollUtil.size(batchData), CollUtil.size(vinSet));

            // 构建VIN到手机号的映射，优先选择非空值，如果有冲突则记录警告
            this.carPhoneMap = batchData.stream()
                    .filter(data -> StrUtil.isNotBlank(data.getPhone()) && StrUtil.isNotBlank(data.getCarVin()))
                    .collect(Collectors.toMap(
                            RemoteOriginalDataParseBO::getCarVin,
                            RemoteOriginalDataParseBO::getPhone,
                            (existing, replacement) -> {
                                log.info("VIN {} 存在不同的手机号: {} vs {}, 使用第一个",
                                        existing, replacement, existing);
                                return existing;
                            }
                    ));

            // 构建VIN到IncontrolId的映射，优先选择非空值，如果有冲突则记录警告
            this.carIcrMap = batchData.stream()
                    .filter(data -> StrUtil.isNotBlank(data.getIncontrolId()) && StrUtil.isNotBlank(data.getCarVin()))
                    .collect(Collectors.toMap(
                            RemoteOriginalDataParseBO::getCarVin,
                            RemoteOriginalDataParseBO::getIncontrolId,
                            (existing, replacement) -> {
                                log.info("VIN {} 存在不同的IncontrolId: {} vs {}, 使用第一个",
                                        existing, replacement, existing);
                                return existing;
                            }
                    ));

            this.vinIdMap = batchData.parallelStream()
                .collect(Collectors.groupingBy(
                    RemoteOriginalDataParseBO::getCarVin,
                    Collectors.mapping(RemoteOriginalDataParseBO::getId, Collectors.toSet())));
            this.insetVehicles = new ArrayList<>();
            this.customerSet = Sets.newHashSet();
            this.serviceDOList = new ArrayList<>();
            this.failVinSet = new HashSet<>();
            this.updateVehicles = new ArrayList<>();

            log.info("原始数据数量: {}, 去重后VIN数量: {}, 有效手机号映射数量: {}, 有效IncontrolId映射数量: {}, VIN-ID映射数量: {}",
                    CollUtil.size(batchData), CollUtil.size(vinSet), CollUtil.size(carPhoneMap),
                    CollUtil.size(carIcrMap), CollUtil.size(vinIdMap));
        }

        public List<RemoteOriginalDataParseBO> getBatchData() { return batchData; }
        public Set<String> getVinSet() { return vinSet; }
        public Map<String, String> getCarPhoneMap() { return carPhoneMap; }
        public Map<String, String> getCarIcrMap() { return carIcrMap; }
        public Map<String, Set<Long>> getVinIdMap() { return vinIdMap; }
        public Map<String, String> getEncryptedDataMap() { return encryptedDataMap; }
        public List<IncontrolVehicleDO> getInsetVehicles() { return insetVehicles; }
        public Set<IncontrolCustomerDO> getCustomerSet() { return customerSet; }
        public List<SubscriptionServiceDO> getServiceDOList() { return serviceDOList; }
        public Set<String> getFailVinSet() { return failVinSet; }
        
        public void setEncryptedDataMap(Map<String, String> encryptedDataMap) {
            this.encryptedDataMap = encryptedDataMap;
        }
        
        public void setFailVinSet(Set<String> failVinSet) {
            this.failVinSet = failVinSet;
        }
        public List<IncontrolVehicleDO> getUpdateVehicles() { return updateVehicles; }
    }
    
    /**
     * 数据预处理
     */
    private BatchProcessContext preprocessBatchData(List<RemoteOriginalDataParseBO> batchData) {
        log.info("开始数据预处理, batchData数量:{}", CollUtil.size(batchData));
        return new BatchProcessContext(batchData);
    }
    
    /**
     * PIPL数据加密
     */
    private void encryptPIPLData(BatchProcessContext context) {
        Set<String> piplData = context.getBatchData().parallelStream()
            .flatMap(service -> Stream.of(service.getFirstName(), service.getSurname(), service.getPhone()))
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());
        
        Map<String, String> encryptedDataMap = piplDataUtil.getEncryptListText(piplData);
        context.setEncryptedDataMap(encryptedDataMap);
        
        log.info("PIPL数据加密完成，加密数据量: {}", CollUtil.size(encryptedDataMap));
    }
    
    /**
     * 处理车辆数据
     */
    private void processVehicleData(BatchProcessContext context) {
        Set<String> failVinSet = compareAndGenVehicleData(
                new HashSet<>(context.getVinSet()),
                context.getCarIcrMap(),
                context.getEncryptedDataMap(),
                context.getCarPhoneMap(),
                context.getInsetVehicles(),
                context.getVinIdMap(),
                context.getUpdateVehicles()
        );
        context.setFailVinSet(failVinSet);
        log.info("车辆数据处理完成，待插入车的数量:{}, 待更新车的数量:{}, 失败VIN数量: {}", CollUtil.size(context.getInsetVehicles()),
                CollUtil.size(context.getUpdateVehicles()), CollUtil.size(failVinSet));
    }
    
    /**
     * 处理订阅服务数据
     */
    private void processSubscriptionData(BatchProcessContext context) {
        HashSet<String> serviceVinSet = new HashSet<>(context.getVinSet());
        serviceVinSet.removeAll(context.getFailVinSet());
        
        compareAndGenSubService(
            context.getBatchData(),
            serviceVinSet,
            context.getServiceDOList(),
            context.getEncryptedDataMap(),
            context.getCustomerSet()
        );
        
        log.info("订阅服务数据处理完成，服务数量: {}, 客户数量: {}",
                CollUtil.size(context.getServiceDOList()), CollUtil.size(context.getCustomerSet()));
    }
    
    /**
     * 持久化批次数据
     */
    private ProcessResult persistBatchData(BatchProcessContext context) {
        return vehicleDataPersistService.persistBatchData(
            context.getInsetVehicles(),
            context.getUpdateVehicles(),
            context.getServiceDOList(),
            context.getCustomerSet(),
            context.getVinIdMap(),
            context.getFailVinSet()
        );
    }
    
    /**
     * 处理批次异常
     */
    private ProcessResult handleBatchException(Throwable e, List<RemoteOriginalDataParseBO> batchData) {
        log.info("批次处理异常", e);
        
        Set<Long> errorIds = convertSet(batchData, RemoteOriginalDataParseBO::getId);
        String errorMessage = StrUtil.maxLength(ExceptionUtil.getRootCauseMessage(e), 
            VehicleProcessConstants.ERROR_DESC_MAX_LENGTH);
        
        remoteOriginalDataService.updateDataFail(errorIds, 
            RemoteDataErrorType.OTHER_FAIL.getType(), errorMessage);
        
        return ProcessResult.of(0, errorIds.size());
    }
    
    /**
     * 汇总处理结果
     */
    private ProcessResult aggregateResults(List<CompletableFuture<ProcessResult>> futureList) {
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        
        ProcessResult totalResult = ProcessResult.empty();
        for (CompletableFuture<ProcessResult> future : futureList) {
            ProcessResult result = future.join();
            totalResult = totalResult.merge(result);
        }
        
        log.info("所有批次处理完成，总结果: {}", totalResult);
        return totalResult;
    }
    
    /**
     * 比较并生成车辆数据
     */
    private Set<String> compareAndGenVehicleData(Set<String> vinSet, Map<String, String> carIcrMap,
                                                Map<String, String> encryptListText, Map<String, String> carPhoneMap,
                                                List<IncontrolVehicleDO> insertVehicles, Map<String, Set<Long>> vinIdMap,
                                                 List<IncontrolVehicleDO> updateVehicles ) {
        if (CollUtil.isEmpty(vinSet)) {
            log.debug("VIN集合为空，跳过车辆数据处理");
            return new HashSet<>();
        }
        log.info("比较并生成车辆数据, 开始前, VIN数量: {}, carIcrMap数量: {}, encryptListText数量: {}, carPhoneMap数量: {}, insertVehicles数量：{}, vinIdMap数量：{}, updateVehicles数量:{}",
                CollUtil.size(vinSet), CollUtil.size(carIcrMap), CollUtil.size(encryptListText), CollUtil.size(carPhoneMap),
                CollUtil.size(insertVehicles), CollUtil.size(vinIdMap), CollUtil.size(updateVehicles));

        // 分批查询DP数据
        List<List<String>> splitVin = CollUtil.split(vinSet, VehicleProcessConstants.BatchConfig.DP_QUERY_BATCH_SIZE);
        List<UserDPResultVO> dpList = new ArrayList<>(vinSet.size());

        for (List<String> vinBatch : splitVin) {
            try {
                List<UserDPResultVO> batchResult = vehicleModelMasterDataService.findDpList(vinBatch);
                if (CollUtil.isNotEmpty(batchResult)) {
                    dpList.addAll(batchResult);
                }
            } catch (Exception e) {
                log.info("DP查询异常，VIN批次: {}, 异常：", vinBatch, e);
            }
        }
        log.info("DP查询结果，请求VIN数量: {}, 返回数据量: {}", vinSet.size(), dpList.size());
        // 处理DP查询失败的VIN
        Set<String> dpVinSet = dpList.stream()
            .filter(Objects::nonNull)
            .map(UserDPResultVO::getVin)
            .filter(StrUtil::isNotBlank)
            .collect(Collectors.toSet());

        Set<String> failedVinSet = new HashSet<>(vinSet);
        failedVinSet.removeAll(dpVinSet);

        // 构建车辆数据
        buildVehicleData(dpVinSet, dpList, carIcrMap, encryptListText, carPhoneMap, insertVehicles, updateVehicles);

        log.info("比较并生成车辆数据, 处理后, VIN数量: {}, carIcrMap数量: {}, encryptListText数量: {}, carPhoneMap数量: {}, insertVehicles数量：{}, vinIdMap数量：{}, updateVehicles数量:{}",
                CollUtil.size(vinSet), CollUtil.size(carIcrMap), CollUtil.size(encryptListText), CollUtil.size(carPhoneMap),
                CollUtil.size(insertVehicles), CollUtil.size(vinIdMap), CollUtil.size(updateVehicles));

        return failedVinSet; // 返回失败的VIN集合
    }

    /**
     * 构建车辆数据
     */
    private void buildVehicleData(Set<String> dpVinSet, List<UserDPResultVO> dpList, Map<String, String> carIcrMap,
                                 Map<String, String> encryptListText, Map<String, String> carPhoneMap,
                                 List<IncontrolVehicleDO> insertVehicles, List<IncontrolVehicleDO> updateVehicles) {
        log.info("构建车辆数据, 开始前, dpVinSet数量：{}, dpList数量: {}, carIcrMap数量: {}, encryptListText数量: {}, carPhoneMap数量: {}, insertVehicles数量：{}, updateVehicles数量:{}",
                CollUtil.size(dpVinSet), CollUtil.size(dpList), CollUtil.size(carIcrMap), CollUtil.size(encryptListText),
                CollUtil.size(carPhoneMap), CollUtil.size(insertVehicles), CollUtil.size(updateVehicles));

        // 添加空集合检查，避免无效的数据库查询
        Map<String, PIVIPackageDO> piviPackageDOMap = new HashMap<>();
        Map<String, IncontrolVehicleDO> vehicleDOMap = new HashMap<>();

        if (CollUtil.isNotEmpty(dpVinSet)) {
            piviPackageDOMap = piviPackageRepository.queryPIVIPackageDOMap(dpVinSet);
            vehicleDOMap = incontrolVehicleRepository.queryIncontrolVehicleDOMap(new ArrayList<>(dpVinSet));
        } else {
            log.info("dpVinSet为空，跳过PIVI包和车辆信息查询");
        }
        log.info("构建车辆数据, 批量查询结果, dpVinSet数量：{}, piviPackageDOMap数量:{}, vehicleDOMap数量：{}",
                CollUtil.size(dpVinSet), CollUtil.size(piviPackageDOMap), CollUtil.size(vehicleDOMap));
        for (UserDPResultVO dpResultVO : dpList) {
            String carVin = dpResultVO.getVin();
            String inControlId = carIcrMap.get(carVin);
            inControlId = Objects.nonNull(inControlId) ? inControlId.toLowerCase() : null;
            String phone = carPhoneMap.get(carVin);
            String encryptPhone = encryptListText.getOrDefault(phone, "");
            PIVIPackageDO piviPackageDO = piviPackageDOMap.get(carVin);
            // 根据vin查询绑定关系
            IncontrolVehicleDO vehicleDO = vehicleDOMap.get(carVin);
            if (vehicleDO == null) {
                vehicleDO = vinInitializeService.getInsertVehicleDO(carVin, inControlId, piviPackageDO, encryptPhone, dpResultVO);
                insertVehicles.add(vehicleDO);
            } else {
                vinInitializeService.getUpdateVehicleDO(carVin, vehicleDO, inControlId, encryptPhone, piviPackageDO);
                updateVehicles.add(vehicleDO);
            }
        }
        log.info("构建车辆数据, 处理后, dpList数量: {}, carIcrMap数量: {}, encryptListText数量: {}, carPhoneMap数量: {}, insertVehicles数量：{}, updateVehicles数量:{}",
                CollUtil.size(dpList), CollUtil.size(carIcrMap), CollUtil.size(encryptListText), CollUtil.size(carPhoneMap),
                CollUtil.size(insertVehicles), CollUtil.size(updateVehicles));
    }

    /**
     * 比较并生成订阅服务数据
     */
    private void compareAndGenSubService(List<RemoteOriginalDataParseBO> batchData, Set<String> vinSet,
                                       List<SubscriptionServiceDO> saveOrUpdateList, Map<String, String> encryptListText,
                                       Set<IncontrolCustomerDO> customerSet) {
        if (CollUtil.isEmpty(vinSet)) {
            log.info("VIN集合为空，跳过订阅服务处理");
            return;
        }

        // 查询已有服务数据
        List<SubscriptionServiceDO> oldServiceList = subscriptionServiceRepository
            .findByVinSetAndServiceType(vinSet, Constants.SERVICE_TYPE.REMOTE);

        Map<String, SubscriptionServiceDO> oldServiceMap = oldServiceList.stream()
            .collect(Collectors.toMap(
                k -> buildServiceKey(k.getCarVin(), k.getServicePackage(), k.getServiceName()),
                v -> v,
                (v1, v2) -> v1));

        log.info("查询到已有服务数据量: {}", CollUtil.size(oldServiceList));

        // 处理每个服务数据
        for (RemoteOriginalDataParseBO serviceBO : batchData) {
            if (!vinSet.contains(serviceBO.getCarVin())) {
                continue; // 跳过失败的VIN
            }
            processServiceData(serviceBO, oldServiceMap, saveOrUpdateList, encryptListText, customerSet);
        }
        log.info("生成订阅服务数据量: {}, customer数量: {}", CollUtil.size(saveOrUpdateList), CollUtil.size(customerSet));
    }

    /**
     * 处理单个服务数据
     */
    private void processServiceData(RemoteOriginalDataParseBO serviceBO,
                                   Map<String, SubscriptionServiceDO> oldServiceMap,
                                   List<SubscriptionServiceDO> saveOrUpdateList,
                                   Map<String, String> encryptListText,
                                   Set<IncontrolCustomerDO> customerSet) {
        // 验证必要字段
        if (serviceBO == null || StrUtil.isBlank(serviceBO.getCarVin()) ||
            StrUtil.isBlank(serviceBO.getServicePackage()) || StrUtil.isBlank(serviceBO.getServiceName())) {
            log.warn("服务数据缺少必要字段，跳过处理: {}", serviceBO);
            return;
        }

        String serviceKey = buildServiceKey(serviceBO.getCarVin(), serviceBO.getServicePackage(), serviceBO.getServiceName());
        SubscriptionServiceDO oldService = oldServiceMap.get(serviceKey);
        String inControlId = serviceBO.getIncontrolId();

        try {
            if (oldService == null) {
                // 创建新服务
                createNewService(serviceBO, saveOrUpdateList);
            } else {
                // 更新已有服务
                updateExistingService(oldService, serviceBO, saveOrUpdateList);
            }
            // 构建客户数据
            if (StrUtil.isNotBlank(inControlId)) {
                buildCustomerData(serviceBO, encryptListText, customerSet);
            }
        } catch (Exception e) {
            log.info("处理服务数据异常, serviceKey: {}", serviceKey, e);
        }
    }

    /**
     * 更新已有服务
     */
    private void updateExistingService(SubscriptionServiceDO oldService, RemoteOriginalDataParseBO serviceBO,
                                     List<SubscriptionServiceDO> saveOrUpdateList) {
        boolean needUpdate = !Objects.equals(serviceBO.getExpiryDateUTC0(), oldService.getExpireDateUtc0());

        if (needUpdate) {
            oldService.setExpireDateUtc0(serviceBO.getExpiryDateUTC0());
            if (serviceBO.getExpiryDateUTC0() != null) {
                oldService.setExpiryDate(serviceBO.getExpiryDateUTC0().plusHours(8));
            }
            oldService.setUpdatedTime(LocalDateTime.now());
            saveOrUpdateList.add(oldService);
        }
    }

    /**
     * 创建新服务
     */
    private void createNewService(RemoteOriginalDataParseBO serviceBO, List<SubscriptionServiceDO> saveOrUpdateList) {
        SubscriptionServiceDO newService = new SubscriptionServiceDO();
        // 复制属性
        newService.setCarVin(serviceBO.getCarVin());
        newService.setServiceName(serviceBO.getServiceName());
        newService.setServicePackage(serviceBO.getServicePackage());
        newService.setExpireDateUtc0(serviceBO.getExpiryDateUTC0());
        if (serviceBO.getExpiryDateUTC0() != null) {
            newService.setExpiryDate(serviceBO.getExpiryDateUTC0().plusHours(8));
        }
        newService.setServiceType(serviceBO.getServiceType());
        // 生成订阅ID
        newService.setSubscriptionId(generateSubscriptionId());

        saveOrUpdateList.add(newService);
    }

    /**
     * 构建客户数据
     */
    private void buildCustomerData(RemoteOriginalDataParseBO serviceBO, Map<String, String> encryptListText,
                                  Set<IncontrolCustomerDO> customerSet) {
        IncontrolCustomerDO customer = new IncontrolCustomerDO();
        customer.setIncontrolId(serviceBO.getIncontrolId());
        customer.setUserid(serviceBO.getUserid());

        // 安全获取加密数据
        String firstName = serviceBO.getFirstName();
        String surname = serviceBO.getSurname();
        String phone = serviceBO.getPhone();

        customer.setFirstName(firstName != null ? encryptListText.get(firstName) : null);
        customer.setSurname(surname != null ? encryptListText.get(surname) : null);
        customer.setPhoneEncrypt(phone != null ? encryptListText.get(phone) : null);

        customerSet.add(customer);
    }

    /**
     * 构建服务键
     */
    private String buildServiceKey(String carVin, String servicePackage, String serviceName) {
        // 安全处理空值
        String safeCarVin = StrUtil.isNotBlank(carVin) ? carVin : "";
        String safeServicePackage = StrUtil.isNotBlank(servicePackage) ? servicePackage : "";
        String safeServiceName = StrUtil.isNotBlank(serviceName) ? serviceName : "";

        return safeCarVin + Constants.DEFAULT_CONCAT_STR + safeServicePackage + Constants.DEFAULT_CONCAT_STR + safeServiceName;
    }

    /**
     * 生成订阅ID
     */
    private String generateSubscriptionId() {
        return ecpIdUtil.nextIdStr();
    }
}
