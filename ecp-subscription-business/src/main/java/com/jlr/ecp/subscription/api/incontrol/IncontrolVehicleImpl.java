package com.jlr.ecp.subscription.api.incontrol;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.notification.api.dto.MsgBlackListDTO;
import com.jlr.ecp.notification.api.notification.NotificationServiceApi;
import com.jlr.ecp.subscription.api.incontrol.dto.InControlConsumerVehicleDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.VehicleSubscriptionDTO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.dal.repository.ConsumerVehicleRepository;
import com.jlr.ecp.subscription.dal.repository.IncontrolVehicleRepository;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.service.vehicle.IncontrolVehicleService;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertList;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
@Slf4j
public class IncontrolVehicleImpl implements IncontrolVehicleAPI {

    @Resource
    IncontrolVehicleService incontrolVehicleService;

    @Resource
    RedissonClient redissonClient;

    @Resource
    IncontrolVehicleDOMapper incontrolVehicleMapper;

    @Resource
    private NotificationServiceApi notificationServiceApi;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    VinInitializeService vinInitializeService;

    @Resource
    private IncontrolVehicleRepository incontrolVehicleRepository;

    @Resource
    private ConsumerVehicleRepository consumerVehicleRepository;


    @Override
    public CommonResult<List<IncontrolVehicleDTO>> getIncontrolVehicleByConsumerCode(String consumerCode,String brandCode) {

        RLock lock = redissonClient.getLock(StrUtil.format(Constants.BUSINESS_LOCK_KEY.INCONTROL_KEY, consumerCode));
        if (lock.isLocked()) {
            throw new RuntimeException("您的车辆信息正在同步中，请稍后再试");
        }
        List<IncontrolVehicleDTO> incontrolVehicles = incontrolVehicleRepository.getIncontrolVehicleByConsumerCode(consumerCode,brandCode);
        return CommonResult.success(incontrolVehicles);
    }

    @Override
    public CommonResult<List<IncontrolVehicleByCarDTO>> getIncontrolVehicleByCarVin(List<String> carVinList) {
        List<IncontrolVehicleByCarDTO> incontrolVehicles = incontrolVehicleService.getIncontrolVehicleByCarVinList(carVinList);

        return CommonResult.success(incontrolVehicles);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> saveVehicleForICRConsumer(List<IncontrolVehicleDTO> vehicleList) {
        if (CollUtil.isEmpty(vehicleList)){
            return CommonResult.success(true);
        }
        // 先删除该ICR账号的记录
        incontrolVehicleMapper.deleteVehicleByIncontrol(vehicleList.get(0).getIncontrolId());
        incontrolVehicleMapper.insertBatch(convertList(vehicleList,e-> BeanUtil.copyProperties(e, IncontrolVehicleDO.class)));
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<Boolean> getAndStoreCarInfo(String inControlId, String consumerCode) {
        //incontrolVehicleService.getAndStoreCarInfo(inControlId);
        vinInitializeService.vinInitializeByLogin(inControlId, consumerCode);
        return CommonResult.success(true);
    }

    /**
     * 根据jlrId列表获取车辆VIN信息
     *
     * @param jlrIdList JLR ID列表，包含了一个或多个JLR ID，用于查询对应的车辆VIN信息
     * @return 返回一个CommonResult对象，包含查询结果由于方法体未实现，目前返回null
     */
    @Override
    public CommonResult<Map<String, List<VehicleSubscriptionDTO>>> getMpExpireCarVinInfo(List<String> jlrIdList) {
        log.info("根据jlrId列表获取车辆VIN信息, jlrIdList的数量：{}", CollUtil.size(jlrIdList));
        Map<String, Set<String>> vinToJlrIdMap = getVinToJlrIdMap(jlrIdList);
        if (CollUtil.isEmpty(vinToJlrIdMap)) {
            log.info("根据jlrId列表获取车辆VIN信息，vinToJlrIdMap为空");
            return CommonResult.success(new HashMap<>());
        }
        List<String> carVinList = new ArrayList<>(vinToJlrIdMap.keySet());
        log.info("根据jlrId列表获取车辆VIN信息, carVinList的数量：{}", CollUtil.size(carVinList));
        List<VehicleSubscriptionDTO> allVehicleDTOList = new ArrayList<>();
        List<VehicleSubscriptionDTO> remoteVehicleDTOList = incontrolVehicleMapper
                .findSubscriptionsByVinListAndDays(carVinList, LocalDateTime.now(), 3, 4, Constants.SERVICE_TYPE.REMOTE);
        log.info("根据jlrId列表获取车辆VIN信息，查询到的Remote车辆数量：{}, vehicleDTOList:{}", remoteVehicleDTOList.size(),
                remoteVehicleDTOList);
        List<VehicleSubscriptionDTO> piviVehicleDTOList = incontrolVehicleMapper
                .findSubscriptionsByVinListAndDays(carVinList, LocalDateTime.now(), 3, 4, Constants.SERVICE_TYPE.PIVI);
        log.info("根据jlrId列表获取车辆VIN信息，查询到的PIVI车辆数量：{}, vehicleDTOList:{}", piviVehicleDTOList.size(),
                piviVehicleDTOList);
        List<VehicleSubscriptionDTO> piviValidVehicleList = mpFilterInvalidVehicle(piviVehicleDTOList);
        allVehicleDTOList.addAll(remoteVehicleDTOList);
        allVehicleDTOList.addAll(piviValidVehicleList);
        List<VehicleSubscriptionDTO> vehicleListRes = new ArrayList<>();
        for (VehicleSubscriptionDTO vehicleDTO : allVehicleDTOList) {
            Set<String> jlrIdSet = vinToJlrIdMap.get(vehicleDTO.getCarVin());
            for (String jlrId : jlrIdSet) {
                vehicleDTO.setJlrId(jlrId);
                vehicleListRes.add(vehicleDTO);
            }
        }
        log.info("根据jlrId列表获取车辆VIN信息，allVehicleDTOList数量:{}， vehicleListRes数量：{}",
                CollUtil.size(allVehicleDTOList), CollUtil.size(vehicleListRes));
        Map<String, List<VehicleSubscriptionDTO>> resp = new HashMap<>();
        for (VehicleSubscriptionDTO vehicleDTO : vehicleListRes) {
            List<VehicleSubscriptionDTO> dtoList = resp.getOrDefault(vehicleDTO.getJlrId(), new ArrayList<>());
            dtoList.add(vehicleDTO);
            resp.put(vehicleDTO.getJlrId(), dtoList);
        }
        log.info("根据jlrId列表获取车辆VIN信息，返回结果数量：{}", CollUtil.size(resp));
        return CommonResult.success(resp);
    }

    /**
     * 根据JlrId列表获取VIN码与JlrId的映射关系
     *
     * @param jlrIdList JLR ID列表
     * @return Map<String, List<String>> VIN码作为key，对应的JLR ID列表作为value的映射关系
     */
    public Map<String, Set<String>> getVinToJlrIdMap(List<String> jlrIdList) {
        log.info("根据JlrId列表获取VIN码与JlrId的映射关系, jlrIdList数量:{}", CollUtil.size(jlrIdList));
        if (CollUtil.isEmpty(jlrIdList)) {
            return new HashMap<>();
        }
        Map<String, Set<String>> map = new HashMap<>();
        List<InControlConsumerVehicleDTO> inControlVehicleDOS = incontrolVehicleMapper.getBindIncontrolVehicleByConsumerCodes(jlrIdList, null);
        for (InControlConsumerVehicleDTO inControlVehicleDO : inControlVehicleDOS) {
            if (Objects.isNull(inControlVehicleDO) || StringUtils.isBlank(inControlVehicleDO.getCarVin())) {
                log.info("据JlrId列表获取VIN码与JlrId的映射关系，vin为空，inControlVehicleDO:{}", inControlVehicleDO);
                continue;
            }
            Set<String> consumerCodeSet = map.getOrDefault(inControlVehicleDO.getCarVin(), new HashSet<>());
            consumerCodeSet.add(inControlVehicleDO.getConsumerCode());
            map.put(inControlVehicleDO.getCarVin(), consumerCodeSet);
        }

        List<ConsumerVehicleDO> consumerVehicleDOS = consumerVehicleRepository.getListByJlrIds(jlrIdList);
        for (ConsumerVehicleDO consumerVehicleDO : consumerVehicleDOS) {
            if (Objects.isNull(consumerVehicleDO) || StringUtils.isBlank(consumerVehicleDO.getCarVin())) {
                log.info("据JlrId列表获取VIN码与JlrId的映射关系，vin为空，consumerVehicleDO:{}", consumerVehicleDO);
                continue;
            }
            Set<String> consumerCodeSet = map.getOrDefault(consumerVehicleDO.getCarVin(), new HashSet<>());
            consumerCodeSet.add(consumerVehicleDO.getConsumerCode());
            map.put(consumerVehicleDO.getCarVin(), consumerCodeSet);
        }
        return map;
    }

    /**
     * 过滤无效车辆订阅信息
     *
     * @param vehicleList 待过滤的车辆订阅信息列表
     * @return List<VehicleSubscriptionDTO> 经过双重过滤后的有效车辆订阅信息列表，
     *
     */
    private List<VehicleSubscriptionDTO> mpFilterInvalidVehicle(List<VehicleSubscriptionDTO> vehicleList) {
        log.info("mp过滤无效车辆信息, 待过滤总数量:{}", vehicleList.size());
        List<VehicleSubscriptionDTO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(vehicleList)) {
            return resp;
        }
        List<VehicleSubscriptionDTO> filterBlackList = mpFilterBlackList(vehicleList);
        resp = mpFilterAmaPExpireDateList(filterBlackList);
        log.info("mp过滤无效车辆信息, 过滤后总数量:{}", resp.size());
        return resp;
    }

    /**
     * 过滤车辆黑名单数据
     *
     * @param vehicleList 待过滤的车辆订阅信息列表，包含需要校验的车辆VIN信息
     * @return 过滤后的合法车辆信息列表（不包含黑名单中的车辆）
     */
    private List<VehicleSubscriptionDTO> mpFilterBlackList(List<VehicleSubscriptionDTO> vehicleList) {
        log.info("mp过滤黑名单中的车辆信息, 待过滤总数量:{}", vehicleList.size());
        List<VehicleSubscriptionDTO> resp = new ArrayList<>();
        if (CollUtil.isEmpty(vehicleList)) {
            return resp;
        }
        List<String> carVinList = vehicleList.stream()
                .map(VehicleSubscriptionDTO::getCarVin).collect(Collectors.toList());
        CommonResult<List<MsgBlackListDTO>> msgBlackListDTOS;
        try {
            msgBlackListDTOS = notificationServiceApi.checkCarVinBlackList(carVinList);
            log.info("mp过滤黑名单中的车辆信息结果, code:{}, msg:{}", msgBlackListDTOS.getCode(), msgBlackListDTOS.getMsg());
        } catch (Exception e) {
            log.info("mp过滤黑名单中的车辆信息, 获取黑名单异常:{}", e.getMessage());
            return vehicleList;
        }
        if (!msgBlackListDTOS.isSuccess() || CollUtil.isEmpty(msgBlackListDTOS.getData())) {
            log.info("mp过滤黑名单中的车辆信息,获取黑名单请求结果错误或数据为空");
            return vehicleList;
        }
        log.info("mp过滤黑名单中的车辆信息, 黑名单总数量：{}", msgBlackListDTOS.getData().size());
        Map<String, MsgBlackListDTO> msgBlackListDTOMap = msgBlackListDTOS.getData().stream()
                .collect(Collectors.toMap(
                        MsgBlackListDTO::getCarVin,
                        Function.identity(),
                        (v1, v2) -> v1
                ));
        for (VehicleSubscriptionDTO vehicle : vehicleList) {
            if (msgBlackListDTOMap.containsKey(vehicle.getCarVin())) {
                log.info("mp过滤不合法的carVin, 当前数据在黑名单中直接过滤，vehicle:{}", vehicle);
                continue;
            }
            resp.add(vehicle);
        }
        log.info("mp过滤黑名单中的车辆信息, 过滤后合法的总数量:{}", resp.size());
        return resp;
    }

    /**
     * 根据车辆VIN列表获取订阅服务信息的映射表
     *
     * @param vehicleList 车辆VIN编号列表
     * @return List<VehicleSubscriptionDTO>
     */
    private List<VehicleSubscriptionDTO> mpFilterAmaPExpireDateList(List<VehicleSubscriptionDTO> vehicleList) {
        log.info("mp过滤出高德服务到期时间晚于ECP服务到期时间的车辆信息列表, 待过滤总数量:{}", vehicleList.size());
        if (CollUtil.isEmpty(vehicleList)) {
            return new ArrayList<>();
        }
        List<String> allCarVinList = vehicleList.stream()
                .map(VehicleSubscriptionDTO::getCarVin)
                .collect(Collectors.toList());
        Map<String, SubscriptionServiceDO> amapServiceDOMap = getMpSubscriptionServiceDOMap(allCarVinList);
        List<VehicleSubscriptionDTO> resp = mpFilterAmaPExpireDateSubscription(vehicleList, amapServiceDOMap);
        log.info("mp过滤出高德服务到期时间晚于ECP服务到期时间的车辆信息列表, 过滤后合法总数量:{}", resp.size());
        return resp;
    }

    /**
     * 过滤高德地图服务到期时间早于ECP服务到期时间的订阅车辆
     *
     * @param vehicleList 待过滤的车辆订阅列表
     * @param amapServiceDOMap 高德服务数据映射表
     * @return 过滤后的合法车辆订阅列表
     */
    private List<VehicleSubscriptionDTO> mpFilterAmaPExpireDateSubscription(List<VehicleSubscriptionDTO> vehicleList,
                                                                            Map<String, SubscriptionServiceDO> amapServiceDOMap) {
        log.info("mp的subscription过滤掉高德地图服务到期时间早于ECP服务到期时间, 待过滤数据量:{}", vehicleList.size());
        List<VehicleSubscriptionDTO> resp = new ArrayList<>();
        for (VehicleSubscriptionDTO vehicleDTO : vehicleList) {
            SubscriptionServiceDO amaPServiceDO = amapServiceDOMap.get(vehicleDTO.getCarVin());
            if (Objects.nonNull(amaPServiceDO) &&  Objects.nonNull(amaPServiceDO.getExpiryDate()) &&
                    amaPServiceDO.getExpiryDate().toLocalDate().isBefore(vehicleDTO.getExpiryDate().toLocalDate())
                    && LocalDateTime.now().toLocalDate().isBefore(vehicleDTO.getExpiryDate().toLocalDate())) {
                log.info("mp该carVin在subscription, 且高德到期时间小于ECP过期时间，进行过滤，amaPServiceDO:{}, vehicleDTO:{}",
                        amaPServiceDO, vehicleDTO);
                continue;
            }
            resp.add(vehicleDTO);
        }
        log.info("mp的subscription过滤掉高德地图服务到期时间早于ECP服务到期时间, 过滤后合法滤数据量:{}", resp.size());
        return resp;
    }

    /**
     * 根据车辆VIN列表获取订阅服务信息的映射表
     *
     * @param carVinList 车辆VIN码列表，作为查询条件。若为空列表或null，直接返回空映射表
     * @return Map<String, SubscriptionServiceDO> 以车辆VIN为key，对应订阅服务实体为value的映射表。
     *
     */
    private Map<String, SubscriptionServiceDO> getMpSubscriptionServiceDOMap(List<String> carVinList) {
        if (CollUtil.isEmpty(carVinList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SubscriptionServiceDO::getCarVin, carVinList)
                .eq(SubscriptionServiceDO::getIsDeleted, false)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                .eq(SubscriptionServiceDO::getServicePackage, ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName());
        List<SubscriptionServiceDO> serviceDOList = new ArrayList<>();
        try {
            serviceDOList = subscriptionServiceMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.info("mp根据车辆VIN列表获取AMAP订阅服务信息的映射表异常:{}", e.getMessage());
        }
        if (CollUtil.isEmpty(serviceDOList)) {
            return new HashMap<>();
        }
        return serviceDOList.stream().collect(Collectors.toMap(SubscriptionServiceDO::getCarVin,
                Function.identity(), (v1,v2) -> v1));
    }
}
