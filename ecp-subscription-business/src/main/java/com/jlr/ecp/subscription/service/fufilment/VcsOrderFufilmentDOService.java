package com.jlr.ecp.subscription.service.fufilment;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.SubBrandListDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.VinsAndServiceDateDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.OrderSubscriptionRespVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.VcsOrderFulfilmentRespVO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【t_vcs_order_fufilment(t_vcs_order_fufilment)】的数据库操作Service
* @createDate 2024-01-19 14:15:09
*/
public interface VcsOrderFufilmentDOService extends IService<VcsOrderFufilmentDO> {

    /**
     * 根据订单item编码查询履约订单详情
     * @param orderItemCode
     * @return
     */
    VcsOrderFulfilmentRespVO getOneByOrderItemCode(String orderItemCode);


    /**
     * 根据订单item编码列表查询履约订单详情
     * @param orderItemCodeList
     * @return
     */
    List<VcsOrderFulfilmentRespVO> getHistoryByOrderItemCodeList(String orderItemCodeList);

    /**
     * 我的订阅 tab列表下 1.揽胜 2.卫士 3.发现
     *
     * 一个人买 car1 car2 属于揽胜，订阅列表只查 所有car s下订阅过的商品，哪怕未激活（支付后才会去订阅）
     *
     * @param subBrandListDTO
     * @return
     */
    List<OrderSubscriptionRespVO> getSubscriptionList(SubBrandListDTO subBrandListDTO);

    /**
     * 我的订阅列表
     *
     * @param jlrId jlrId
     * @param clientId clientId
     * @return Map<String, List<OrderSubscriptionRespVO>>
     */
    Map<String, List<OrderSubscriptionRespVO>> getSubscriptionList(String jlrId, String clientId);

    /**
     * 履约订单详情API 获取每个orderItemCode根据MaxId对应的最新履约记录
     *
     * @param orderItemCodeList 订单item编码
     * @return CommonResult<List < VcsOrderFulfilmentRespVO>>
     */
    List<VcsOrderFulfilmentRespVO> getLatestByOrderItemCodeList(List<String> orderItemCodeList);

    /**
     * 通过carvinList查出t_subscription_service t_vcs_order_fufilment t_vcs_order_rollback表中的数据
     * 1.服务过期时间大于当前时间+一年 2.正在激活中 激活关闭中的服务不可购买
     *
     * @param carVinAndServiceTypeList
     * @return
     */
    Boolean checkCanBuyService(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

    /**
     * 通过carvinList查出t_subscription_service t_vcs_order_fufilment t_vcs_order_rollback表中的数据
     * 1.服务过期时间大于当前时间+一年 2.正在激活中 激活关闭中的服务不可购买
     *
     * @param carVinAndServiceTypeList
     * @return
     */
    Boolean checkCanBuyServiceV2(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

    /**
     * 根据CarVin去查询正在激活中的履约
     * @param carvinList
     * @return carvinList
     */
    List<String> getFulfilmentListByCarVinList(List<String> carvinList);

    /**
     * 查询车辆对应的履约服务时间
     * @param carvinList vin
     * @return VinsAndServiceDateDTO
     */
    List<VinsAndServiceDateDTO> findVinAndServiceDate(List<String> carvinList);


    /**
     * 服务激活后同步订单状态
     * @param orderCode
     * @param vcsOrderCode
     */
    void executeOrderStatusSync(String orderCode,String vcsOrderCode);

    /**
     * 同步执行订单状态
     *
     * @param vcsOrderFufilmentDOList  vcs履约订单列表
     * */
    void executeOrderStatusSync(List<VcsOrderFufilmentDO> vcsOrderFufilmentDOList);

    /**
     * 服务关闭后同步订单状态
     * @param orderCode
     * @param refundOrderCode
     */
    void executeRefundOrderStatusSync(String orderCode,String refundOrderCode,String fufilmentId);

    /**
     * 处理TSDP履约失败订单的补偿任务
     * @param upcastDays 上溯天数
     * @return 成功处理掉的任务数量
     */
    Integer processTsdpFailedTasks(@RequestParam(value = "upcastDays") Integer upcastDays);


    /**
     * 代客下单服务购买权限校验
     * @param carVinAndServiceTypeList 入参
     * @return true
     */
    CommonResult<Boolean> checkCanBuyServiceForPC(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList);

    /**
     * 检查订单包含的履约服务是否有激活失败状态
     * @param orderCode 入参
     * @return true
     */
    Boolean checkStatus(String orderCode);

    /**
     * 更新服务状态
     */
    void updateServiceStatusFail(String fufilmentId);

    /**
     * 检查是否有激活失败的订单
     * @return true
     */
    Set<String> getFailOrders();
}
