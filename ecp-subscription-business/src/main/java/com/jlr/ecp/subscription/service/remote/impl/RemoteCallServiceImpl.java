package com.jlr.ecp.subscription.service.remote.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.jlr.ecp.subscription.annotation.ApiLimitation;
import com.jlr.ecp.subscription.api.subscripiton.dto.ServicePackageDTO;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteModifyRespDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteQueryByVinRespDTO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFulfilmentCallMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.fufil.CallActivationStatusEnum;
import com.jlr.ecp.subscription.enums.oss.RecordResultEnum;
import com.jlr.ecp.subscription.enums.redis.RedisDelayQueueEnum;
import com.jlr.ecp.subscription.enums.remote.RemoteModifyErrorEnum;
import com.jlr.ecp.subscription.enums.subscribeservice.notify.ExpirationServiceEnum;
import com.jlr.ecp.subscription.exception.TooManyRequestException;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;
import com.jlr.ecp.subscription.kafka.listener.dto.TSDPSubscriptionDTO;
import com.jlr.ecp.subscription.kafka.message.BaseMessage;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.util.RLockUtil;
import com.jlr.ecp.subscription.util.redis.RedisDelayQueueUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RateIntervalUnit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.*;

@Service
@Slf4j
public class RemoteCallServiceImpl implements RemoteCallService {

    @Resource
    RestTemplate restTemplate;

    @Value("${tsdp.subscriptionRenewUrl}")
    private String subscriptionRenewUrl;

    @Value("${tsdp.ifEcomToken}")
    private String ifEcomToken;

    @Value("${tsdp.getByVinUrl}")
    private String getByVinUrl;

    @Value("${tsdp.icrSubscriptionUrl}")
    private String icrSubscriptionUrl;

    @Resource
    private Redisson redisson;

    @Resource
    private VcsOrderFulfilmentCallMapper callMapper;

    @Resource
    private RedisService redisService;

    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;

    @Value("${busyErrorLimit:5}")
    private Integer busyErrorLimit;

    @Value("${busyDelaySecond:10}")
    private Integer busyDelaySecond;


    public RemoteModifyRespDTO callTSDPRenewService(OrdFufilmentBusiDTO param) {
        Instant start = Instant.now();
        RLock lock = redisson.getLock(Constants.REDIS_KEY.CONCURRENT_REMOTE_RENEWAL_KEY);
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.ACCEPT, ContentType.JSON.getValue());
        headers.set("Content-Type", ContentType.JSON.getValue());
        headers.set(HttpHeaders.AUTHORIZATION, ifEcomToken);
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(param), headers);
        ResponseEntity<JSONObject> response;
        RemoteModifyRespDTO respDTO = new RemoteModifyRespDTO();
        boolean success = false;
        try {
            if (RLockUtil.tryLock(lock, 2500, 5000, TimeUnit.MILLISECONDS)) {
                log.info("调用TSDP续期获取锁成功, {}", param.getVin());
                response = restTemplate.postForEntity(subscriptionRenewUrl, requestEntity, JSONObject.class);
                respDTO.setResponse(JSON.toJSONString(response.getBody()));
                if (!HttpStatus.OK.equals(response.getStatusCode()) && !HttpStatus.CREATED.equals(response.getStatusCode())) {
                    log.warn("手动续费调用TSDP续期服务响应码非200：{}", response);
                    respDTO.setErrorMsg(RemoteModifyErrorEnum.REQUEST_ERROR.getDesc());
                } else {
                    log.info("手动续费调用TSDP续期服务成功, {}", response);
                    success = true;
                }
            } else {
                log.error("调用TSDP续费获取锁失败：param={}", param);
                respDTO.setErrorMsg(String.format(RecordResultEnum.CALL_ERROR.getDesc(), "调用TSDP续费获取锁失败"));
            }
        } catch (RestClientException e) {
            log.warn("手动续费调用TSDP续期服务出错：{}", e.getMessage());
            if (e instanceof HttpClientErrorException.TooManyRequests) {
                log.error("429错误：param={}", param);
                respDTO.setTooManyRequests(true);
            }
            String errorMsg = RemoteModifyErrorEnum.SYSTEM_ERROR.getDesc();
            if (Objects.requireNonNull(e.getMessage()).startsWith("40")) {
                errorMsg =  RemoteModifyErrorEnum.REQUEST_ERROR.getDesc();
            }
            respDTO.setErrorMsg(errorMsg);
            respDTO.setResponse(e.getMessage());
        } finally {
            // 计算并打印处理耗时
            Duration elapsed = Duration.between(start, Instant.now());
            log.info("call TSDP duration: " + elapsed.toMillis() + " ms");
            if (elapsed.toMillis() < 2000L) {
                try {
                    // sleep 2 second - elapsed.toMillis()
                    Thread.sleep(2000L - elapsed.toMillis());
                } catch (InterruptedException e) {
                    log.info("call TSDP sleep error：{}", e.getMessage());
                    Thread.currentThread().interrupt();
                }
            }
            RLockUtil.unlock(lock, 3);
        }
        respDTO.setSuccess(success);
        return respDTO;
    }

    @Override
    public RemoteModifyRespDTO concurrentCallTSDPRenew(OrdFufilmentBusiDTO param) {
        RemoteModifyRespDTO respDTO = new RemoteModifyRespDTO();
        respDTO.setSuccess(false);

        // sprint47:并发控制
        RLock lock = redisson.getLock(Constants.REDIS_KEY.CONCURRENT_REMOTE_RENEWAL_KEY + param.getVin());
        // 尝试获取锁，不等待
        if (!lock.tryLock()) {
            log.warn("获取remote续费锁失败, carVin:{}", param.getVin());
            // 未获取到锁
            respDTO.setErrorMsg(ORDER_IN_TRANSIT_ERROR.getMsg());
            return respDTO;
        }
        try {
            return callTSDPRenewService(param);
        } catch (Exception e) {
            log.warn("remote续约请求异常:{}", e.getMessage());
            respDTO.setErrorMsg(CALL_REMOTE_RENEWAL_ERROR.getMsg());
            return respDTO;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock(); // 仅在当前线程持有锁时释放锁
            }
        }
    }

    @Override
    public RemoteModifyRespDTO listenerCallTSDPRenew(List<SubscriptionServiceDO> services, String fulfilmentId,
                                                     BaseMessage baseMessage, String incontrolId) {
        OrdFufilmentBusiDTO param = buildCallApiParam(services, baseMessage, incontrolId);
        RemoteModifyRespDTO remoteModifyRespDTO = concurrentCallTSDPRenew(param);
        addRemoteFulfilmentCall(baseMessage, fulfilmentId, remoteModifyRespDTO, param);
        return remoteModifyRespDTO;
    }

    @Override
    @ApiLimitation(tokenBucketName="getTSDPByVin", time = 2500, timeUnit = RateIntervalUnit.MILLISECONDS, limitCount = 1)
    public RemoteQueryByVinRespDTO getByVin(String vin) {
        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.ACCEPT, ContentType.JSON.getValue());
        headers.set(HttpHeaders.AUTHORIZATION, ifEcomToken);
        HttpEntity<String> requestEntity = new HttpEntity<>(headers);
        ResponseEntity<RemoteQueryByVinRespDTO> response;
        try {
            log.info("根据vin:{}查询TSDP续期服务开始", vin);
            response = restTemplate.exchange(getByVinUrl + vin, HttpMethod.GET, requestEntity, RemoteQueryByVinRespDTO.class);
            if (!HttpStatus.OK.equals(response.getStatusCode())) {
                log.warn("根据vin:{}查询TSDP续期服务响应码非200：{}", vin, response);
                throw exception(ErrorCodeConstants.GET_EXPIRE_DATE_ERROR);
            } else {
                log.info("根据vin:{}查询TSDP续期服务成功, {}", vin, response);
                return response.getBody();
            }
        } catch (RestClientException e) {
            log.error("根据vin:{}查询TSDP续期服务异常：{}", vin, e.getMessage());
            if (e instanceof HttpClientErrorException.NotFound) {
                throw exception(ErrorCodeConstants.VIN_NOT_FOUND_ERROR);
            }
            if (e instanceof HttpClientErrorException.TooManyRequests) {
                throw new TooManyRequestException();
            }
            throw exception(ErrorCodeConstants.GET_EXPIRE_DATE_ERROR);
        }
    }

    @Override
    public List<VinsAndServiceDTO> getByICR(String inControlId) {
        String url = icrSubscriptionUrl;
        // 设置header数据
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.ACCEPT, "application/json");
        headers.add(HttpHeaders.AUTHORIZATION, ifEcomToken);
        // 创建HttpEntity对象，包含header和body数据
        HttpEntity<String> entity = new HttpEntity<>(headers);
        url = url + inControlId;
        // 发送GET请求
        ResponseEntity<JSONObject> response;
        try {
            log.info("TSDP订阅查询URL:{}", url);
            response = restTemplate.exchange(url, HttpMethod.GET, entity, JSONObject.class);
            if (!HttpStatus.OK.equals(response.getStatusCode())) {
                log.warn("根据inControlId查询TSDP续期服务响应码非200：{}", response);
                throw exception(ErrorCodeConstants.GET_EXPIRE_DATE_ERROR);
            }
            JSONObject responseBody = response.getBody();
            log.info("根据inControlId查询TSDP续期服务成功, {}", responseBody);
            log.info("Current Server TimeZone: " + TimeZone.getDefault().getID() + "name " + TimeZone.getDefault().getDisplayName());

            return parseResult(inControlId, responseBody);
        } catch (RestClientException e) {
            log.info("TSDP查询账号下订阅失败：", e);
            if (e instanceof HttpClientErrorException.Forbidden) {
                log.error("403错误", e);
                throw exception(TSDP_REQUEST_FORBIDDEN);
            }
            if (e instanceof HttpClientErrorException.TooManyRequests) {
                log.error("429错误", e);
                throw exception(TSDP_REQUEST_RATE_TOO_FAST);
            }
            throw e;
        }
    }

    /**
     * 添加履行记录。
     * 该方法用于创建并插入一个应用部署履行记录到数据库中。它详细记录了部署过程中的关键信息，
     *
     * @param baseMessage baseMessage
     * @param fulfilmentId 履行ID
     * @param remoteModifyRespDTO         remoteModifyRespDTO
     * @param param     param
     */
    private void addRemoteFulfilmentCall(BaseMessage baseMessage, String fulfilmentId,
                                       RemoteModifyRespDTO remoteModifyRespDTO, OrdFufilmentBusiDTO param) {
        VcsOrderFufilmentCall fulfilmentCall = VcsOrderFufilmentCall.builder()
                .fufilmentId(fulfilmentId)
                .servicePackage(ExpirationServiceEnum.REMOTE.getType())
                .serviceName(ExpirationServiceEnum.REMOTE.getType())
                .requestParam(JSON.toJSONString(param))
                .requestResult(JSON.toJSONString(remoteModifyRespDTO))
                .activationStatus(remoteModifyRespDTO.isSuccess() ? CallActivationStatusEnum.SUCCESS.getStatus() : CallActivationStatusEnum.FAILED.getStatus())
                .activationFailedMsg(remoteModifyRespDTO.getErrorMsg())
                .tenantId(baseMessage.getTenantId().intValue())
                .build();
        callMapper.insert(fulfilmentCall);
    }

    /**
     * 构建调用API所需的参数对象
     * 此方法根据订阅的服务信息和基础消息构建一个订单履行业务DTO对象，用于后续的API调用
     *
     * @param services 订阅的服务列表，包含服务相关信息，如inControl ID、服务包、服务名称等
     * @param baseMessage 基础消息对象，包含VIN和消息ID等信息
     * @param incontrolId incontrolId
     * @return 返回构建好的OrdFufilmentBusiDTO对象，包含用户ID、VIN、交易ID和修改的订阅信息列表
     */
    private OrdFufilmentBusiDTO buildCallApiParam(List<SubscriptionServiceDO> services, BaseMessage baseMessage, String incontrolId) {
        OrdFufilmentBusiDTO busiDTO = new OrdFufilmentBusiDTO();
        busiDTO.setUser(incontrolId);
        busiDTO.setVin(baseMessage.getVin());
        // 当user为空不传递transactionId
        busiDTO.setTransactionId(Objects.isNull(busiDTO.getUser()) ? null : baseMessage.getMessageId());
        ArrayList<TSDPSubscriptionDTO> list = new ArrayList<>();
        // 校验时间不能为过去
        for (SubscriptionServiceDO service : services) {
            TSDPSubscriptionDTO subscriptionDTO = new TSDPSubscriptionDTO();
            subscriptionDTO.setServicePackage(service.getServicePackage());
            subscriptionDTO.setServiceName(service.getServiceName());
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
            String expiryDate = baseMessage.getServiceEndDate().minusHours(8).format(formatter);
            subscriptionDTO.setExpiryDate(expiryDate);
            list.add(subscriptionDTO);
        }
        busiDTO.setNewOrModifiedSubscriptions(list);
        return busiDTO;
    }

    /**
     * 将指定的控制ID保存到延迟队列中以进行重试
     * 此方法主要用于处理查询失败的场景，通过将查询任务加入延迟队列来实现重试机制
     * 它首先检查当前查询失败的次数是否超过了设定的阈值，如果没有，则将失败次数递增
     * 并在延迟队列中重新安排该查询任务，以便在指定的延迟时间后再次尝试查询
     *
     * @param inControlIdAndJlrId 查询任务的唯一标识符，用于在延迟队列中跟踪和管理查询任务
     */
    @Override
    public void save2DelayQue(String inControlIdAndJlrId) {
        Integer increment = redisService.getCacheObject(StrUtil.format(Constants.BUSINESS_LOCK_KEY.ICR_TSDP_QRY_FAIL, inControlIdAndJlrId));
        if (increment == null || increment <= busyErrorLimit) {
            redisService.increment(StrUtil.format(Constants.BUSINESS_LOCK_KEY.ICR_TSDP_QRY_FAIL, inControlIdAndJlrId));
            redisDelayQueueUtil.remove(inControlIdAndJlrId, RedisDelayQueueEnum.ECP_ICR_TSDP_QRY_RETRY_TASK.getCode());
            redisDelayQueueUtil.addDelayQueue(inControlIdAndJlrId, busyDelaySecond, TimeUnit.SECONDS, RedisDelayQueueEnum.ECP_ICR_TSDP_QRY_RETRY_TASK.getCode());
        }
    }

    /**
     * 清除延迟队列中的特定任务
     * 该方法旨在清除Redis延迟队列中与给定控制ID相关的任务它首先尝试删除与控制ID相关的业务锁键，
     * 然后从指定的延迟任务队列中移除相应的任务此操作通常在任务处理成功或需要取消特定任务时进行
     *
     * @param inControlId 控制ID，用于标识特定的任务
     */
    @Override
    public void clearDelayQue(String inControlId) {
        redisService.deleteObject(StrUtil.format(Constants.BUSINESS_LOCK_KEY.ICR_TSDP_QRY_FAIL, inControlId));
        redisDelayQueueUtil.remove(inControlId, RedisDelayQueueEnum.ECP_ICR_TSDP_QRY_RETRY_TASK.getCode());
    }

    /**
     * 解析给定的JSON结果并返回一个包含VIN和服务信息的列表
     *
     * @param inControlId 控制标识符，用于日志信息
     * @param body 包含订阅信息的JSON对象
     * @return 一个VinsAndServiceDTO对象的列表，每个对象包含VIN和相关服务包信息
     */
    private List<VinsAndServiceDTO> parseResult(String inControlId, JSONObject body) {
        if(Objects.isNull(body)){
            return Collections.emptyList();
        }
        JSONArray subscriptions = body.getJSONArray("subscriptions");
        List<VinsAndServiceDTO> list = Lists.newArrayList();

        if (CollUtil.isNotEmpty(subscriptions)) {
            for (Object subscription : subscriptions) {
                JSONObject subscriptionJson = new JSONObject((Map) subscription);
                VinsAndServiceDTO serviceDTO = parseSubscription(subscriptionJson);
                list.add(serviceDTO);
            }

            log.info("{}账号下TSDP查询结果组装list===>\r\n{}", inControlId, JSON.toJSON(list));
        }

        return list;
    }

    /**
     * 解析订阅信息JSON对象为VinsAndServiceDTO对象
     * 该方法主要用于将从API或其他来源获取的订阅信息JSON数据转换为VinsAndServiceDTO对象，以便于在应用程序中进一步处理
     *
     * @param subscriptionJson 包含订阅信息的JSON对象，包括车辆识别号（VIN）和相关的服务包信息
     * @return 返回一个填充了订阅信息的VinsAndServiceDTO对象如果输入JSON不包含有效的数据，则返回一个空的VinsAndServiceDTO对象
     */
    private VinsAndServiceDTO parseSubscription(JSONObject subscriptionJson) {
        VinsAndServiceDTO serviceDTO = new VinsAndServiceDTO();
        serviceDTO.setVin(subscriptionJson.getString("vin"));

        JSONArray packages = subscriptionJson.getJSONArray("packages");
        if (CollUtil.isNotEmpty(packages)) {
            List<ServicePackageDTO> packageList = new ArrayList<>();
            for (Object aPackage : packages) {
                JSONObject packageJson = new JSONObject((Map) aPackage);
                JSONArray servicesJson = packageJson.getJSONArray("services");

                if (CollUtil.isNotEmpty(servicesJson)) {
                    for (Object service : servicesJson) {
                        ServicePackageDTO packageDTO = new ServicePackageDTO();
                        packageDTO.setServicePackageName(packageJson.getString("packageName"));
                        Date expiryDate = packageJson.getDate("expiryDate");

                        if (expiryDate != null) {
                            packageDTO.setExpireDate(LocalDateTime.ofInstant(expiryDate.toInstant(), ZoneId.of("UTC")).plusHours(8));
                            packageDTO.setExpireDateUTC0(LocalDateTime.ofInstant(expiryDate.toInstant(), ZoneId.of("UTC")));
                            packageDTO.setServiceName(String.valueOf(service));
                            packageList.add(packageDTO);
                        }
                    }
                }
            }
            serviceDTO.setServicePackage(packageList);
        }

        return serviceDTO;
    }

}
