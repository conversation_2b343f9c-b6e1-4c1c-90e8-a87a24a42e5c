package com.jlr.ecp.subscription.exception;

import lombok.NoArgsConstructor;

@NoArgsConstructor
public class TooManyRequestException extends RuntimeException {

    public TooManyRequestException(String message) {
        super(message);
    }

    public TooManyRequestException(String message, Throwable cause) {
        super(message, cause);
    }

    public TooManyRequestException(Throwable cause) {
        super(cause);
    }
}
