package com.jlr.ecp.subscription.service.vehicle;

import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.VehicleBindMessageDO;
import com.jlr.ecp.subscription.service.listener.dto.VinBindNotifyDto;
import software.amazon.awssdk.services.sqs.model.Message;

import java.util.List;

/**
 * VIN初始化服务接口
 *
 */
public interface VinInitializeService {

    /**
     * 初始化车辆信息（用户主动登录）
     * @param inControlId inControlId
     */
    void vinInitializeByLogin(String inControlId, String consumerCode);

    /**
     * 初始化车辆信息（代客下单by ICR查询）
     * @param inControlId inControlId
     * @param infoListFromTSDP infoListFromTSDP
     */
    void vinInitializeByQueryICR(String inControlId, List<VinsAndServiceDTO> infoListFromTSDP);

    /**
     * 初始化车辆信息（代客下单by VIN查询）
     * @param carVin carVin
     */
    String vinInitializeByQueryVin(String carVin);

    /**
     * 处理MQC消息
     * @param message message
     * @param vinBindNotifyDto vinBindNotifyDto
     */
    boolean dealWithMQCMessage(Message message, VinBindNotifyDto vinBindNotifyDto);

    /**
     * 更新车辆信息对象(IncontrolVehicleDO)的属性值
     *
     * @param carVin 车辆VIN码
     * @param vehicleDO 车辆信息对象，需要被更新的实体对象
     * @param inControlId InControl系统ID
     * @param encryptPhone 加密后的手机号码
     * @param piviPackageDO PIVI包信息对象，用于获取发票日期等信息
     */
    void getUpdateVehicleDO(String carVin, IncontrolVehicleDO vehicleDO, String inControlId, String encryptPhone, PIVIPackageDO piviPackageDO);

    /**
     * 插入车辆信息数据对象
     *
     * @param carVin 车辆识别码
     * @param inControlId 控制ID
     * @param piviPackageDO PIVI包数据对象
     * @param encryptPhone 加密手机号
     * @param dpResultVO 用户DP结果数据对象
     * @return 返回构建好的车辆信息数据对象
     */
    IncontrolVehicleDO getInsertVehicleDO(String carVin, String inControlId, PIVIPackageDO piviPackageDO, String encryptPhone, UserDPResultVO dpResultVO);

    /**
     * 根据inControlId查询TSDP返回为空时, 将车辆绑定表中inControlId字段置为空
     * @param inControlId inControlId
     */
    void updateInControlIdNull(String inControlId);

    /**
     * 根据车辆VIN码保存或更新在线服务信息
     *
     * @param carVin 车辆VIN码
     * @return 保存或更新成功返回true，失败返回false
     */
    Boolean saveOrUpdateOnlineServiceByVin(String carVin);

    /**
     * 处理本地保存的MQC消息
     * @param messageDO messageDO
     */
    void dealWithLocalMessage(VehicleBindMessageDO messageDO);
}
