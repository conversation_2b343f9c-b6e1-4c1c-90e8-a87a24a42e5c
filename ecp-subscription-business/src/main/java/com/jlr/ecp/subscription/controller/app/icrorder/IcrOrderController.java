package com.jlr.ecp.subscription.controller.app.icrorder;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.icrvehicle.dto.ValidateICRDTO;
import com.jlr.ecp.subscription.service.icrorder.IncontrolVehicleDOService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 与订单相关的接口
 *
 * <AUTHOR>
 */
@Tag(name = "app端 - 与订单相关的接口")
@RestController
@RequestMapping("v1/icr")
@Validated
@Slf4j
public class IcrOrderController {

    @Resource
    private IncontrolVehicleDOService incontrolVehicleDOService;

    /**
     * 根据vin检查ICR账号
     *
     * @param validateICRDTO validateICRDTO
     */
    @PostMapping("/validateICR")
    @Operation(summary = "校验ICR账号")
    CommonResult<Boolean> validateICR(@RequestBody ValidateICRDTO validateICRDTO) {
        log.info("校验ICR账号, validateICRDTO={}", validateICRDTO);

        return CommonResult.success(incontrolVehicleDOService.validateICR(validateICRDTO));
    }

}
