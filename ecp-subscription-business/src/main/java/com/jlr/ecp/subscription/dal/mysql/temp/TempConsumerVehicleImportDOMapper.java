package com.jlr.ecp.subscription.dal.mysql.temp;

import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.subscription.dal.dataobject.temp.TempConsumerVehicleImportDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface TempConsumerVehicleImportDOMapper extends BaseMapperX<TempConsumerVehicleImportDO> {

    /**
     * 查询已存在于 incontrol_vehicle 表中的记录（用于去重或更新等）
     */
    List<TempConsumerVehicleImportDO> selectExistsInVehicle(@Param("importStatus") Integer importStatus);

    /**
     * 查询未存在于 incontrol_vehicle 表中的记录（用于新增绑定）
     */
    List<TempConsumerVehicleImportDO> selectNotExistsInVehicle(@Param("importStatus") Integer importStatus);
}
