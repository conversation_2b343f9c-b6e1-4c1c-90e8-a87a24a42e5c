package com.jlr.ecp.subscription.kafka.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteModifyRespDTO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentRecords;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderRollbackDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderRollbackRecords;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceLogDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentDOMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderRollbackMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderRollbackRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceLogMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.fufil.*;
import com.jlr.ecp.subscription.enums.subscribeservice.notify.ExpirationServiceEnum;
import com.jlr.ecp.subscription.kafka.message.cancel.CancelMessage;
import com.jlr.ecp.subscription.service.fufilment.VcsOrderFufilmentDOService;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OrderCancelListener {

    @Autowired
    private VcsOrderFufilmentDOMapper vcsOrderFufilmentDOMapper;

    @Autowired
    private VcsOrderRollbackMapper vcsOrderRollbackMapper;

    @Autowired
    private IncontrolVehicleDOMapper incontrolVehicleDOMapper;

    @Resource
    SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private PIVIAppDService piviAppDService;

    @Resource(name = "subscribeAsyncThreadPool")
    private ThreadPoolTaskExecutor subscribeAsyncThreadPool;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceDOMapper;

    @Resource
    private SubscriptionServiceLogMapper subscriptionServiceLogMapper;

    @Resource
    private VcsOrderFufilmentDOService vcsOrderFufilmentDOService;

    @Resource
    private VcsOrderFufilmentRecordsMapper vcsOrderFufilmentRecordsMapper;

    @Resource
    private PIVIUnicomService piviUnicomService;


    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private VcsOrderRollbackRecordsMapper vcsOrderRollbackRecordsMapper;

    @Resource
    private RemoteCallService remoteCallService;

    @Resource
    private Environment environment;


    @KafkaListener(topics="order-refund-success-topic", groupId = "order-cancel-group", properties = "max.poll.records:1")
    public void onMessage(String message) {
        log.info("order cancel 消费消息, message:{}", message);
        /**
         *  1.根据  vcsOrderCode查询t_vcs_order_fufilment表是否有数据，若无则结束
         *  2. 根据查询出的履约记录表，组装履约回退记录到t_vcs_order_rollback表中，持久化数据库，初始状态为：激活关闭中
         *  3. 调用TSDP取消接口，对服务结束时间进行更新
         */
        try {
            CancelMessage cancelMessage = JSON.parseObject(message, CancelMessage.class);
            boolean checkRes = redisTemplate.opsForValue().setIfAbsent(cancelMessage.getVcsOrderCode(), "1", 1, TimeUnit.MINUTES);
            if (!checkRes) {
                log.info("取消履约，重复消费，cancelMessage:{}", cancelMessage);
                return ;
            }
            TenantContextHolder.setTenantId(cancelMessage.getTenantId());


            VcsOrderFufilmentDO orderFufilment = vcsOrderFufilmentDOMapper.selectOne(
                    new LambdaQueryWrapperX<VcsOrderFufilmentDO>()
                    // .select(VcsOrderFufilmentDO::getFufilmentId)
                    .eq(VcsOrderFufilmentDO::getVcsOrderCode, cancelMessage.getVcsOrderCode())
                    // .eq(VcsOrderFufilmentDO::getOrderCode, cancelMessage.getOrderCode())
            );
            if (orderFufilment == null){
                return;
            }
            cancelMessage.setFufilmentId(orderFufilment.getFufilmentId());
            cancelMessage.setVin(orderFufilment.getCarVin());

            // 校验vin码是否存在，以及是否拥有车辆完整配置信息和订阅服务
            IncontrolVehicleDO incontrolVehicleDO = incontrolVehicleDOMapper.selectOne(
                    new LambdaQueryWrapperX<IncontrolVehicleDO>()
                            .eq(IncontrolVehicleDO::getCarVin, orderFufilment.getCarVin())
                            .eq(BaseDO::getIsDeleted, false)
                            .orderByDesc(IncontrolVehicleDO::getId)
                            .last("limit 1"));
            if (incontrolVehicleDO == null) {
                log.warn("取消消费时车辆：{}未查询到", orderFufilment.getCarVin());
                return;
            }

            Integer serviceType = orderFufilment.getServiceType();
            // REMOTE==1, PIVI==3
            if (Constants.SERVICE_TYPE.NOT_REMOTE.equals(serviceType)) {
                serviceType = Constants.SERVICE_TYPE.PIVI;
            }

            // 查询对应订阅服务，筛选remote类型得到要取消的的服务
            List<SubscriptionServiceDO> services = subscriptionServiceMapper.selectList(
                    new LambdaQueryWrapperX<SubscriptionServiceDO>()
                            .eq(SubscriptionServiceDO::getCarVin, orderFufilment.getCarVin())
                            .eq(SubscriptionServiceDO::getServiceType, serviceType)
            );
            if (CollUtil.isEmpty(services)) {
                log.warn("取消消费时车辆：{}未查询到可供取消的服务", orderFufilment.getCarVin());
                return;
            }

            if(Constants.SERVICE_TYPE.REMOTE.equals(serviceType)) {
                // 对于remote，VcsOrderFufilmentDO为激活失败，则一定不需要回滚
                log.info("进行remote退单履约消费处理");
                handleRemoteRollBackFulfilment(cancelMessage, orderFufilment, services, incontrolVehicleDO.getIncontrolId());
            } else if (Constants.SERVICE_TYPE.PIVI.equals(serviceType)) {
                // 对于PIVI，VcsOrderFufilmentDO为激活失败，还需要查询records表，只有已激活的才需要回滚
                log.info("进行PIVI退单履约消费处理");
                handlePiviRollbackFulfilment(cancelMessage, orderFufilment, services);
            }
        }  finally {
            TenantContextHolder.clear();
        }
    }

    /**
     * 添加一条cancel记录
     * @param cancelMessage cancel消息
     * */
    private Pair<Boolean, VcsOrderRollbackDO> insertOrderRollback(CancelMessage cancelMessage, Integer status) {
        VcsOrderRollbackDO exist = vcsOrderRollbackMapper.selectOne(new LambdaQueryWrapperX<VcsOrderRollbackDO>()
                .eq(VcsOrderRollbackDO::getRefundOrderCode, cancelMessage.getRefundOrderCode())
                //退单号+vcs_code确定唯一单
                .eq(VcsOrderRollbackDO::getVcsOrderCode, cancelMessage.getVcsOrderCode())
                .eq(BaseDO::getIsDeleted, false)
        );
        if (exist == null) {
            exist = VcsOrderRollbackDO.builder()
                    .fufilmentId(cancelMessage.getFufilmentId())
                    .rollbackFufilmentId(UUID.randomUUID().toString().replace("-", ""))
                    // .orderCode(cancelMessage.getOrderCode())
                    .vcsOrderCode(cancelMessage.getVcsOrderCode())
                    .refundOrderCode(cancelMessage.getRefundOrderCode())
                    .serviceEndDate(cancelMessage.getServiceEndDate())
                    .serviceStatus(status)
                    .build();
            vcsOrderRollbackMapper.insert(exist);
            return new ImmutablePair<>(true, exist);
        } else {
            exist.setServiceStatus(status);
            vcsOrderRollbackMapper.updateById(exist);
            log.info("回滚履约记录已经存在, rollbackFufilmentId:{}", exist.getRollbackFufilmentId());
            return new ImmutablePair<>(false, exist);
        }
    }

    /**
     * 处理Pivi履行订单
     * @param cancelMessage 履行消息，包含订单信息。
     */
    private void handlePiviRollbackFulfilment(CancelMessage cancelMessage, VcsOrderFufilmentDO fulfilmentDO, List<SubscriptionServiceDO> services) {
        // 只有records表中有已激活的APPD或CU才需要回滚
        List<String> successPackages = vcsOrderFufilmentRecordsMapper.selectList(
                new LambdaQueryWrapperX<VcsOrderFufilmentRecords>()
                        .eq(VcsOrderFufilmentRecords::getFufilmentId, cancelMessage.getFufilmentId())
                        .eq(VcsOrderFufilmentRecords::getActivationStatus, ServiceActivationStatusEnum.ACTIVATION_SUCCESS.getStatus())
                        .in(VcsOrderFufilmentRecords::getServicePackage, ServicePackageEnum.ONLINE_PACK.getPackageName(), ServicePackageEnum.DATA_PLAN.getPackageName())
                        .eq(VcsOrderFufilmentRecords::getIsDeleted, false))
                .stream().map(VcsOrderFufilmentRecords::getServicePackage)
                .collect(Collectors.toList());
        boolean activateFailure = FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getStatus().equals(fulfilmentDO.getServiceStatus());
        // 履约激活失败且records表中没有激活成功的服务，不需要做回滚
        if (activateFailure && CollUtil.isEmpty(successPackages)) {
            notNeedRollback(cancelMessage,fulfilmentDO);
            return;
        }

        Long jlrSubscriptionId = getJlrSubscriptionId(services, successPackages);
        boolean needRollbackAppD = Objects.nonNull(jlrSubscriptionId);
        boolean needRollbackCu = successPackages.contains(ServicePackageEnum.DATA_PLAN.getPackageName());

        // appd和cu均不需要回滚
        if (!needRollbackAppD && !needRollbackCu) {
            notNeedRollback(cancelMessage, fulfilmentDO);
            return;
        }

        //插入履约表信息
        OrderCancelListener cancelListener = applicationContext.getBean(getClass());
        Pair<Boolean, VcsOrderRollbackDO> pair = cancelListener.insertPiviOrderRollback(cancelMessage, needRollbackAppD, needRollbackCu);
        // 判断回滚履约记录是否已经存在, 证明为重复消费
        if (!pair.getLeft()) {
            return;
        }
        VcsOrderRollbackDO rollbackDO = pair.getRight();

        // APPD
        CompletableFuture<Boolean> appDFuture = CompletableFuture.supplyAsync(
                        () -> !needRollbackAppD || piviAppDService.callAppDService(cancelMessage, cancelMessage.getFufilmentId(), jlrSubscriptionId), subscribeAsyncThreadPool)
                .exceptionally(ex -> {
                    log.info("调用APPD接口失败", ex);
                    return false;
                });

        // 对于激活失败的情况，log表没有记录，联通回滚的刷新前到期日取当前subscription_service表的日期
        if (activateFailure) {
            LocalDateTime refreshBeforeDate = services.stream().filter(serviceDO -> ServicePackageEnum.DATA_PLAN.getPackageName().equals(serviceDO.getServicePackage()))
                    .map(SubscriptionServiceDO::getExpiryDate)
                    .findFirst()
                    .orElse(null);
            cancelMessage.setRefreshBeforeDate(refreshBeforeDate);
        }

        CompletableFuture<Boolean> unionFuture = callUnicomService(cancelMessage, needRollbackCu);
        try {
            log.info("APPD的续约:{},联通的续约:{}",appDFuture.get(),unionFuture.get());
            // 如果两个响应都true，修改服务状态
            if(appDFuture.get() && unionFuture.get()){
                log.info("APPD和联通取消订单成功, cancelMessage={}", cancelMessage);
                cancelSuccessUpdateStatus(rollbackDO, fulfilmentDO, needRollbackAppD, needRollbackCu);
                // 执行退单状态同步
                vcsOrderFufilmentDOService.executeRefundOrderStatusSync(fulfilmentDO.getOrderCode(),rollbackDO.getRefundOrderCode(),fulfilmentDO.getFufilmentId());
            }else {
                cancelListener.updateFulfilmentRecord(appDFuture.get(), unionFuture.get(), rollbackDO, needRollbackAppD, needRollbackCu);
            }
        } catch (InterruptedException e) {
            log.error("获取APPD和联通履约结果失败", e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("获取APPD和联通履约结果失败", e);
        }
    }

    /**
     * 处理不需要回滚的取消消息
     *
     * @param cancelMessage 取消消息对象，包含退款订单相关信息
     * @param fulfilmentDO 履约订单信息对象，包含订单履行相关数据
     */
    private void  notNeedRollback(CancelMessage cancelMessage, VcsOrderFufilmentDO fulfilmentDO) {
        //插入履约表信息
        Pair<Boolean, VcsOrderRollbackDO> pair = insertOrderRollback(cancelMessage, CancelServiceStatusEnum.ACTIVATE_OFF.getStatus());
        // 只有第一次插入成功时才执行退单状态同步
        if (pair.getLeft()) {
            // 执行退单状态同步
            vcsOrderFufilmentDOService.executeRefundOrderStatusSync(fulfilmentDO.getOrderCode(), cancelMessage.getRefundOrderCode(), fulfilmentDO.getFufilmentId());
        }
    }

    /**
     * 异步调用联通服务进行取消操作
     *
     * @param cancelMessage 取消消息对象，包含取消操作所需的信息
     * @param needRollbackCu 是否需要回滚联通服务的取消操作
     * @return 返回一个CompletableFuture，表示异步操作的结果
     */
    private CompletableFuture<Boolean> callUnicomService(CancelMessage cancelMessage, boolean needRollbackCu) {
        CompletableFuture<Boolean> unionFuture;
        // 根据环境变量调用联通接口
        String env = environment.getProperty(Constants.PROFILE_ACTIVE);
        if ("dev".equals(env) || "test".equals(env)) {
            // 联通mock返回成功
            unionFuture = CompletableFuture.supplyAsync(() -> true, subscribeAsyncThreadPool)
                    .exceptionally(ex -> {
                        log.info("调用联通接口失败", ex);
                        return false;
                    });
        } else {
            //uat 和prd实际直接调用
            unionFuture = CompletableFuture.supplyAsync(
                            () -> !needRollbackCu || piviUnicomService.newCancelUnicomService(cancelMessage, cancelMessage.getFufilmentId()), subscribeAsyncThreadPool)
                    .exceptionally(ex -> {
                        log.info("调用联通接口失败", ex);
                        return false;
                    });
        }
        return unionFuture;
    }

    /**
     * 获取JLR订阅ID
     * 本方法旨在处理特定的订阅服务列表和包列表，以找到并返回JLR订阅ID如果提供了在线包服务，则返回相应的JLR订阅ID；
     * 否则，返回null
     *
     * @param services 订阅服务列表，包含多个订阅服务的详细信息
     * @param packages 包列表，包含用户订阅的包名称
     * @return 如果找到JLR订阅ID，则返回之；否则返回null
     */
    private static Long getJlrSubscriptionId(List<SubscriptionServiceDO> services, List<String> packages) {
        if (packages.contains(ServicePackageEnum.ONLINE_PACK.getPackageName())) {
            return services.stream().filter(serviceDO -> ServicePackageEnum.ONLINE_PACK.getPackageName().equals(serviceDO.getServicePackage()))
                    .map(SubscriptionServiceDO::getJlrSubscriptionId)
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    /**
     * 更新履约记录状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFulfilmentRecord(boolean appd, boolean unicom, VcsOrderRollbackDO rollbackDO,
                                       boolean needRollbackAppD, boolean needRollbackCu) {
        // 需要回滚的数据才需要更新
        if (needRollbackAppD) {
            // 记录APPD关闭激活状态
            VcsOrderRollbackRecords appDRecord = new VcsOrderRollbackRecords();
            appDRecord.setActivationStatus(appd ? CancelServiceStatusEnum.ACTIVATE_OFF.getStatus()
                    : CancelServiceStatusEnum.ACTIVATE_OFF_FAILURE.getStatus());
            vcsOrderRollbackRecordsMapper.update(appDRecord, new LambdaUpdateWrapper<VcsOrderRollbackRecords>()
                    .eq(VcsOrderRollbackRecords::getRollbackFufilmentId, rollbackDO.getRollbackFufilmentId())
                    .eq(VcsOrderRollbackRecords::getServicePackage, ServicePackageEnum.ONLINE_PACK.getPackageName())
                    .eq(BaseDO::getIsDeleted, false));
        }

        // 记录UNICOM关闭激活状态
        if (needRollbackCu) {
            VcsOrderRollbackRecords unicomRecord = new VcsOrderRollbackRecords();
            unicomRecord.setActivationStatus(unicom ? CancelServiceStatusEnum.ACTIVATE_OFF.getStatus()
                    : CancelServiceStatusEnum.ACTIVATE_OFF_FAILURE.getStatus());
            vcsOrderRollbackRecordsMapper.update(unicomRecord, new LambdaUpdateWrapper<VcsOrderRollbackRecords>()
                    .eq(VcsOrderRollbackRecords::getRollbackFufilmentId, rollbackDO.getRollbackFufilmentId())
                    .eq(VcsOrderRollbackRecords::getServicePackage, ServicePackageEnum.DATA_PLAN.getPackageName())
                    .eq(BaseDO::getIsDeleted, false));
        }
        // 均成功才更新逆向履约表状态
        if (appd && unicom) {
            // 更新逆向履约表状态为激活关闭
            rollbackDO.setServiceStatus(CancelServiceStatusEnum.ACTIVATE_OFF.getStatus());
            vcsOrderRollbackMapper.updateById(rollbackDO);
        }
    }


    private void cancelSuccessUpdateStatus(VcsOrderRollbackDO rollbackDO, VcsOrderFufilmentDO fulfilmentDO,
                                          boolean needRollbackAppD, boolean needRollbackCu) {
        log.info("取消订单成功,修改服务状态, rollbackDO={}, fulfilmentDO={}", rollbackDO, fulfilmentDO);
        // 更新回滚record状态
        OrderCancelListener cancelListener = applicationContext.getBean(getClass());
        cancelListener.updateFulfilmentRecord(true, true, rollbackDO, needRollbackAppD, needRollbackCu);
        // 查询车辆订阅服务信息
        List<SubscriptionServiceDO> subscriptionServiceDOList = getSubscriptionServiceDO(fulfilmentDO.getCarVin());
        if (CollUtil.isNotEmpty(subscriptionServiceDOList)) {
            cancelListener.insertSubscriptionServiceLog(rollbackDO, subscriptionServiceDOList);
        }
    }

    /**
     * 根据车辆识别码和服务套餐获取订阅服务信息。
     *
     * @param carVin 车辆识别码，用于精确查询特定车辆的订阅服务信息。
     * @return 返回匹配条件的订阅服务数据对象列表。
     */
    private List<SubscriptionServiceDO> getSubscriptionServiceDO(String carVin) {
        LambdaQueryWrapperX<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(SubscriptionServiceDO::getCarVin, carVin)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                .eq(SubscriptionServiceDO::getIsDeleted, false);
        return subscriptionServiceDOMapper.selectList(queryWrapper);
    }

    /**
     * 插入订阅服务日志。
     *
     * @param rollbackDO 订阅服务的回滚履行订单数据对象，包含订单的相关信息。
     * @param serviceDOList serviceDOList
     */
    @Transactional(rollbackFor = Exception.class)
    public void insertSubscriptionServiceLog(VcsOrderRollbackDO rollbackDO, List<SubscriptionServiceDO> serviceDOList) {
        List<SubscriptionServiceLogDO> logDOList = new ArrayList<>();
        for (SubscriptionServiceDO serviceDO : serviceDOList) {
            // 高德的时间不做回退
            if (ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage())) {
                continue;
            }
            LocalDateTime refreshBeforeDate = serviceDO.getExpiryDate();
            LocalDateTime refreshAfterDate = rollbackDO.getServiceEndDate();
            SubscriptionServiceLogDO logDO = SubscriptionServiceLogDO.builder()
                    .subscriptionId(serviceDO.getSubscriptionId())
                    .fufilmentId(rollbackDO.getFufilmentId())
                    .refreshBeforeDate(refreshBeforeDate)
                    .refreshAfterDate(refreshAfterDate)
                    .build();
            serviceDO.setExpiryDate(refreshAfterDate);
            serviceDO.setExpireDateUtc0(refreshAfterDate.minusHours(8));
            serviceDO.setUpdatedTime(LocalDateTime.now());
            logDOList.add(logDO);
        }
        subscriptionServiceLogMapper.insertBatch(logDOList);
        log.info("插入SubscriptionServiceLogDO表成功, size={}", logDOList.size());
        subscriptionServiceDOMapper.updateBatch(serviceDOList);
        log.info("更新SubscriptionServiceDO表成功, size={}", serviceDOList.size());
    }

    /**
     * 组装VcsOrderRollbackRecords对象
     *
     */
    private VcsOrderRollbackRecords createRollbackRecord(LocalDateTime serviceEndDate, String fulfilmentId, String rollbackFulfilmentId, String servicePackage, String serviceName) {
        VcsOrderRollbackRecords rollbackRecords = new VcsOrderRollbackRecords();
        rollbackRecords.setExpireDate(serviceEndDate);
        rollbackRecords.setFufilmentId(fulfilmentId);
        rollbackRecords.setRollbackFufilmentId(rollbackFulfilmentId);
        rollbackRecords.setServicePackage(servicePackage);
        rollbackRecords.setServiceName(serviceName);
        rollbackRecords.setActivationStatus(CancelServiceStatusEnum.UNACTIVATED.getStatus());
        rollbackRecords.setActivationMethod(ActivationMethod.MULTIPLE_ACTIVATION.getMethod());
        return rollbackRecords;
    }

    /**
     * 处理Remote履行订单
     * @param cancelMessage 履行消息，包含订单信息。
     * @param services 订阅的服务，用于处理服务的激活和回滚。
     */
    private void handleRemoteRollBackFulfilment(CancelMessage cancelMessage, VcsOrderFufilmentDO fulfilmentDO,
                                        List<SubscriptionServiceDO> services, String inControlId) {
        // 如果不等于激活失败才会发送TSDP指令
        if (FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getStatus().equals(fulfilmentDO.getServiceStatus())) {
            //插入履约表信息
            Pair<Boolean, VcsOrderRollbackDO> pair = insertOrderRollback(cancelMessage, CancelServiceStatusEnum.ACTIVATE_OFF.getStatus());
            // 只有第一次插入成功时才执行退单状态同步
            if (pair.getLeft()) {
                // 执行退单状态同步
                vcsOrderFufilmentDOService.executeRefundOrderStatusSync(fulfilmentDO.getOrderCode(),cancelMessage.getRefundOrderCode(),fulfilmentDO.getFufilmentId());
            }
            return;
        }
        //插入履约表信息
        OrderCancelListener cancelListener = applicationContext.getBean(getClass());
        Pair<Boolean, VcsOrderRollbackDO> pair = cancelListener.insertRemoteOrderRollback(cancelMessage);
        // 判断回滚履约记录是否已经存在, 证明为重复消费
        if (!pair.getLeft()) {
            return;
        }
        VcsOrderRollbackDO rollbackDO = pair.getRight();
        // 根据履约订单消息，获得车辆信息，服务订阅结束时间，组装数据向TSDP发起服务续约请求
        RemoteModifyRespDTO respDTO = remoteCallService.listenerCallTSDPRenew(services, rollbackDO.getFufilmentId(), cancelMessage, inControlId);
        updateRemoteRollbackRecord(rollbackDO.getRollbackFufilmentId(), respDTO.isSuccess());
    }

    /**
     * 更新回滚履约记录
     */
    public void updateRemoteRollbackRecord(String rollbackFufilmentId, boolean result) {
        VcsOrderRollbackRecords records = new VcsOrderRollbackRecords();
        records.setActivationStatus(result ? CancelServiceStatusEnum.ACTIVATE_OFF.getStatus()
                : CancelServiceStatusEnum.ACTIVATE_OFF_FAILURE.getStatus());
        vcsOrderRollbackRecordsMapper.update(records, new LambdaUpdateWrapper<VcsOrderRollbackRecords>()
                .eq(VcsOrderRollbackRecords::getRollbackFufilmentId, rollbackFufilmentId)
                .eq(VcsOrderRollbackRecords::getServicePackage, ExpirationServiceEnum.REMOTE.getType())
                .eq(BaseDO::getIsDeleted, false));
    }

    /**
     * 插入Remote订单回滚履行记录。
     *
     * @param cancelMessage 履行消息，包含订单履行的详细信息。
     */
    @Transactional(rollbackFor = Exception.class)
    public Pair<Boolean, VcsOrderRollbackDO> insertRemoteOrderRollback(CancelMessage cancelMessage) {
        //插入回滚履约表信息
        Pair<Boolean, VcsOrderRollbackDO> pair = insertOrderRollback(cancelMessage, CancelServiceStatusEnum.UNACTIVATED.getStatus());
        VcsOrderRollbackDO rollbackDO = pair.getRight();
        // 只有OrderFulfilment不存在时，才新增records记录
        if (pair.getLeft()) {
            // 插入回滚履约记录表信息
            VcsOrderRollbackRecords rollbackRecords = new VcsOrderRollbackRecords();
            rollbackRecords.setExpireDate(cancelMessage.getServiceEndDate());
            rollbackRecords.setFufilmentId(rollbackDO.getFufilmentId());
            rollbackRecords.setRollbackFufilmentId(rollbackDO.getRollbackFufilmentId());
            rollbackRecords.setServicePackage(ExpirationServiceEnum.REMOTE.getType());
            rollbackRecords.setServiceName(ExpirationServiceEnum.REMOTE.getType());
            rollbackRecords.setActivationStatus(CancelServiceStatusEnum.UNACTIVATED.getStatus());
            rollbackRecords.setActivationMethod(ActivationMethod.MULTIPLE_ACTIVATION.getMethod());
            vcsOrderRollbackRecordsMapper.insert(rollbackRecords);
        }
        return pair;
    }

    /**
     * 插入Pivi订单回滚履行记录。
     *
     * @param cancelMessage 履行消息，包含订单履行的详细信息。
     */
    @Transactional(rollbackFor = Exception.class)
    public Pair<Boolean, VcsOrderRollbackDO> insertPiviOrderRollback(CancelMessage cancelMessage, boolean needRollbackAppD, boolean needRollbackCu) {
        //插入回滚履约表信息
        Pair<Boolean, VcsOrderRollbackDO> pair = insertOrderRollback(cancelMessage, CancelServiceStatusEnum.UNACTIVATED.getStatus());
        VcsOrderRollbackDO rollbackDO = pair.getRight();

        // 只有OrderFulfilment不存在时，才新增records记录
        if (pair.getLeft()) {
            List<VcsOrderRollbackRecords> rollbackRecordsList = new ArrayList<>();
            if (needRollbackCu) {
                rollbackRecordsList.add(createRollbackRecord(cancelMessage.getServiceEndDate(), rollbackDO.getFufilmentId(),
                        rollbackDO.getRollbackFufilmentId(), ServicePackageEnum.DATA_PLAN.getPackageName(), ServiceNameEnum.UNICOM.getServiceName()));
            }

            if(needRollbackAppD){
                List<VcsOrderRollbackRecords> appDRecords = Arrays.stream(AppDServiceNameEnum.values())
                        .map(serviceNameEnum -> createRollbackRecord(cancelMessage.getServiceEndDate(), rollbackDO.getFufilmentId(),
                                rollbackDO.getRollbackFufilmentId(), ServicePackageEnum.ONLINE_PACK.getPackageName(), serviceNameEnum.getServiceName()))
                        .collect(Collectors.toList());
                rollbackRecordsList.addAll(appDRecords);
            }
            vcsOrderRollbackRecordsMapper.insertBatch(rollbackRecordsList);
        }
        return pair;
    }
}
