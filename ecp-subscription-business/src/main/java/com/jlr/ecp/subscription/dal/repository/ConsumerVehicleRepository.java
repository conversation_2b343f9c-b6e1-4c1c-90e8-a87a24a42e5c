package com.jlr.ecp.subscription.dal.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;
import com.jlr.ecp.subscription.enums.consumer.ConsumerVehicleSourceEnum;

import java.util.List;

/**
 * ConsumerVehicle Repository接口
 *
 */
public interface ConsumerVehicleRepository extends IService<ConsumerVehicleDO> {

    ConsumerVehicleDO getOneByCarVinAndJlrId(String carVin, String jlrId, ConsumerVehicleSourceEnum sourceEnum);

    List<ConsumerVehicleDO> getListByJlrId(String jlrId);

    List<ConsumerVehicleDO> getListByJlrIds(List<String > jlrIds);

    List<ConsumerVehicleDO> getListByVinJlrIdList(List<String> carVinList, List<String> jlrIdList,Integer source);
}
