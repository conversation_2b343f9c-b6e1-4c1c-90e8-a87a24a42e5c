package com.jlr.ecp.subscription.service.icrorder;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.icrvehicle.dto.ValidateICRDTO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import com.jlr.ecp.subscription.controller.admin.dto.vehicle.ConsumerInfoVO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【t_incontrol_vehicle(t_incontrol_vehicle)】的数据库操作Service
 * @createDate 2024-01-16 09:42:36
 */
public interface IncontrolVehicleDOService extends IService<IncontrolVehicleDO> {


    /**
     * 获取车辆信息
     *
     * @param carVin
     * @return
     */
    IcrVehicleRespVO getOneByCarVin(String carVin);


    /**
     * 获取车辆信息
     *
     * @param carVinList
     * @return
     */
    List<IcrVehicleListRespVO> getCarVinInfo(List<String> carVinList);

    CommonResult<ConsumerInfoVO> searchVinInfo(String carVin, String icr);

    Boolean validateICR(ValidateICRDTO validateICRDTO);

    Map<String, String> getByJlrIdAndVinList(ValidateICRDTO validateICRDTO);
}
