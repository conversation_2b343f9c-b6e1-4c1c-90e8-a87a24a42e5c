package com.jlr.ecp.subscription.dal.mysql.icroder;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.incontrol.dto.InControlConsumerVehicleDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.VehicleSubscriptionDTO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.service.vin.expiry.dto.IncontrolVehicleDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertList;

/**
* <AUTHOR>
* @description 针对表【t_incontrol_vehicle(t_incontrol_vehicle)】的数据库操作Mapper
* @createDate 2024-01-16 09:42:36
* @Entity com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO
*/
@Mapper
public interface IncontrolVehicleDOMapper extends BaseMapperX<IncontrolVehicleDO> {

    int deleteVehicleByIncontrol(String incontrolId);

    List<IncontrolVehicleDO> getBindIncontrolVehicle(@Param("consumerCode") String consumerCode,@Param("brandCode") String brandCode);
    List<InControlConsumerVehicleDTO> getBindIncontrolVehicleByConsumerCodes(@Param("consumerCodes") List<String> consumerCodes, @Param("brandCode") String brandCode);

    List<IncontrolVehicleByCarDTO> selectIncontrolVehicleByCarVinList(List<String> carVinList);

    List<Map<String, String>> getPhoneEncryptsByCarVinList(@Param("carVinList") List<String> carVinList);

    int deleteVehicleByCarVinList(@Param("carVinList") List<String> carVinList);

    /**
     * 根据Carvin查询关联信息
     * @param carVinList VIN信息
     * @return IncontrolVehicleDO
     */
    default List<com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleDTO> selectCarInfoByCarVinListAndBrandCode(List<String> carVinList,String brandCode){
        if(CollUtil.isEmpty(carVinList)|| StrUtil.isBlank(brandCode)){
            return Collections.emptyList();
        }
        List<IncontrolVehicleDO> incontrolVehicleDOS = selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .in(IncontrolVehicleDO::getCarVin, carVinList)
                .eq(IncontrolVehicleDO::getIsDeleted, false)
                .eq(IncontrolVehicleDO::getBrandCode,brandCode));

        return convertList(incontrolVehicleDOS, e -> BeanUtil.copyProperties(e, com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleDTO.class));

    }


    /**
     * 根据Carvin查询关联信息
     * @param carVin VIN信息
     * @return IncontrolVehicleDO
     */
    default IncontrolVehicleDO selectOneByCarVin(String carVin){
        return selectOne(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .eq(IncontrolVehicleDO::getCarVin, carVin)
                .eq(IncontrolVehicleDO::getIsDeleted, false)
                .orderByDesc(IncontrolVehicleDO::getId)
                .last(Constants.LIMIT_ONE));
    }

    default List<IncontrolVehicleDO> selectListByICR(String icr){

        return selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .eq(IncontrolVehicleDO::getIncontrolId, icr)
                .eq(IncontrolVehicleDO::getIsDeleted, false)
                .orderByDesc(IncontrolVehicleDO::getId));
    }

    default List<IncontrolVehicleDO> selectListByICRAndBrandCode(String icr,String brandCode){

        return selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .eq(IncontrolVehicleDO::getIncontrolId, icr)
                .eq(IncontrolVehicleDO::getIsDeleted, false)
                .eq(IncontrolVehicleDO::getBrandCode, brandCode));
    }

    List<VehicleSubscriptionDTO> findSubscriptionsByVinListAndDays(
            @Param("carVinList") List<String> carVinList,
            @Param("currentDateTime") LocalDateTime currentDateTime,
            @Param("days") int days,
            @Param("daysPlusOne") int daysPlusOne,
            @Param("serviceType") Integer serviceType);

    List<IncontrolVehicleDTO> getReportOrds(@Param("carVins") List<String> strings);

    void updateBatchWithNulls(@Param("list") List<IncontrolVehicleDO> list);

    List<IncontrolVehicleDO> getBindByJlrIdAndVinList(@Param("consumerCode") String consumerCode,
                                                  @Param("carVinList") List<String> carVinList);
}




