package com.jlr.ecp.subscription.api.subscription;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.notification.api.dto.MsgBlackListDTO;
import com.jlr.ecp.notification.api.notification.NotificationServiceApi;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleDTO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.pivi.dto.AmaPExpireDateRequestDTO;
import com.jlr.ecp.subscription.api.pivi.dto.UnicomExpireDateDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.ServiceExpireInfoVO;
import com.jlr.ecp.subscription.api.remoteservice.RemoteOriginalDataParseBO;
import com.jlr.ecp.subscription.api.subscripiton.SubscriptionServiceApi;
import com.jlr.ecp.subscription.api.subscripiton.dto.*;
import com.jlr.ecp.subscription.api.subscripiton.vo.ProcessFulfilmentVO;
import com.jlr.ecp.subscription.api.subscripiton.vo.ServiceExpireStatisticVo;
import com.jlr.ecp.subscription.api.subscripiton.vo.ServiceExpireVo;
import com.jlr.ecp.subscription.api.subscripiton.vo.SubscriptionServiceQueryVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.FulfilmentServiceStatusVO;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.ProductBookInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderRollbackDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO;
import com.jlr.ecp.subscription.dal.dataobject.incontrol.IncontrolCustomerDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.remoteservice.RemoteOriginalDataDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentDOMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderRollbackDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.SeriesBrandMappingDataDOMapper;
import com.jlr.ecp.subscription.dal.mysql.incontrol.IncontrolCustomerMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remoteservice.RemoteOriginalDataMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.dal.repository.IncontrolVehicleRepository;
import com.jlr.ecp.subscription.enums.fufil.CancelServiceStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.FufilmentServiceStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.FulfilTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.enums.remote.RemoteDataErrorType;
import com.jlr.ecp.subscription.enums.subscribeservice.notify.*;
import com.jlr.ecp.subscription.enums.unicom.UnicomBookStatusEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomRealnameFlagEnum;
import com.jlr.ecp.subscription.model.dto.SimCardInfoDTO;
import com.jlr.ecp.subscription.model.vo.AmaPSearchCenterVO;
import com.jlr.ecp.subscription.service.AmaPUpdateExpireDateService;
import com.jlr.ecp.subscription.service.UnicomExpireDateService;
import com.jlr.ecp.subscription.service.fufilment.VcsOrderFufilmentDOServiceImpl;
import com.jlr.ecp.subscription.service.icrorder.SeriesBrandMappingDataDOService;
import com.jlr.ecp.subscription.service.incontrol.IncontrolCustomerService;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import com.jlr.ecp.subscription.service.pivi.impl.PIVIUnicomServiceImpl;
import com.jlr.ecp.subscription.service.remotepackage.RemotePackageDOService;
import com.jlr.ecp.subscription.service.remoteservice.RemoteOriginalDataService;
import com.jlr.ecp.subscription.service.vehicle.IncontrolVehicleService;
import com.jlr.ecp.subscription.util.PIPLDataUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.framework.common.pojo.CommonResult.success;
import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertMap;
import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertSet;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.TSDP_REQUEST_FORBIDDEN;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.TSDP_REQUEST_RATE_TOO_FAST;

/**
 * 服务包API  提供 RESTful API 接口，给 Feign 调用
 *
 * <AUTHOR>
 */
@RestController
@Validated
@Slf4j
public class SubscriptionServiceApiImpl implements SubscriptionServiceApi {


    @Value("${splitSize:1000}")
    private int splitSize;

    @Value("${missCountMax:5}")
    private int missCountMax;

    @Value("${service-fail-day:7}")
    private Integer serviceFailDay;


    @Resource
    SubscriptionServiceMapper subscriptionServiceMapper;


    @Resource
    private PIVIUnicomServiceImpl piviUniconService;

    @Resource
    PIVIAmaPService piviAmaPService;

    @Resource
    RedisService redisService;

    @Resource
    PIPLDataUtil piplDataUtil;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private IncontrolVehicleDOMapper incontrolVehicleDOMapper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private SeriesBrandMappingDataDOMapper seriesBrandMappingDataDOMapper;

    private static final String OK = "ok";

    @Resource
    RemotePackageDOService remotePackageDOService;

    @Resource
    RemoteOriginalDataService remoteOriginalDataService;

    @Resource
    RestTemplate restTemplate;

    @Value("${tsdp.subscriptionExpireUrl}")
    private String subscriptionExpireUrl;

    @Value("${tsdp.ifEcomToken}")
    private String ifEcomToken;

    @Resource
    IncontrolVehicleService incontrolVehicleService;

    @Resource
    IncontrolCustomerService incontrolCustomerService;

    @Resource
    ApplicationContext applicationContext;

    @Resource
    VehicleModelMasterDataService vehicleModelMasterDataService;

    @Resource
    IncontrolVehicleDOMapper incontrolVehicleMapper;

    @Resource
    private VcsOrderFufilmentDOMapper vcsOrderFufilmentDOMapper;

    @Resource
    private VcsOrderRollbackDOMapper vcsOrderRollbackDOMapper;


    @Resource
    private IncontrolCustomerMapper incontrolCustomerMapper;

    @Resource
    private VcsOrderFufilmentDOServiceImpl vcsOrderFufilmentDOService;

    @Resource
    private NotificationServiceApi notificationServiceApi;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    private PIVIUnicomService PIVIUnicomService;

    @Resource
    private UnicomExpireDateService unicomExpireDateService;

    @Resource
    RemoteOriginalDataMapper remoteOriginalDataMapper;

    @Resource
    private AmaPUpdateExpireDateService amaPUpdateExpireDateService;

    @Resource
    private SeriesBrandMappingDataDOService seriesBrandMappingDataDOService;

    @Resource
    private IncontrolVehicleRepository incontrolVehicleRepository;


    private static final  Integer LIMIT_NUM = 2000;


    @Override
    public CommonResult<List<VinsAndServiceDTO>> fetchServiceExpireInfoByVins(List<String> vins) {
        if (CollUtil.isEmpty(vins)) {
            return success(Collections.emptyList());
        }
        List<IncontrolVehicleDO> vehicles = incontrolVehicleDOMapper.selectList(
                new LambdaQueryWrapperX<IncontrolVehicleDO>()
                        .select(IncontrolVehicleDO::getIncontrolId)
                        .in(IncontrolVehicleDO::getCarVin, vins));
        if (CollUtil.isEmpty(vehicles)) {
            return success(Collections.emptyList());
        }
        Set<IncontrolVehicleDO> icrSet = new HashSet<>(vehicles);
        List<VinsAndServiceDTO> resultList = new ArrayList<>();
        for (IncontrolVehicleDO vehicle : icrSet) {
            if (Objects.isNull(vehicle) || Objects.isNull(vehicle.getIncontrolId())) {
                continue;
            }
            try {
                List<VinsAndServiceDTO> result = incontrolVehicleService.queryAndStoreVinsAndServiceInfo(vehicle.getIncontrolId());
                if (CollUtil.isNotEmpty(result)) {
                    resultList.addAll(result);
                }
            } catch (Exception e) {
                log.error("定时任务抓取ICR账号车辆服务出错", e);
            }
        }
        return success(resultList);
    }

    @Override
    public CommonResult<SubscriptionServiceQueryVO> findServiceExpiryDate(SubscriptionServiceQueryDTO queryDTO){
        SubscriptionServiceQueryVO subscriptionServiceQueryVO = new SubscriptionServiceQueryVO();
        Integer subscriptionServiceType = queryDTO.getServiceType();
        if (Constants.SERVICE_TYPE.NOT_REMOTE.equals(queryDTO.getServiceType())) {
            subscriptionServiceType = Constants.SERVICE_TYPE.PIVI;
        }
        //根据条件查询list
        List<SubscriptionServiceDO> subscriptionServiceList = subscriptionServiceMapper.selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .eq(BaseDO::getIsDeleted, false)
                .eq(SubscriptionServiceDO::getCarVin, queryDTO.getCarVin())
                .eq(SubscriptionServiceDO::getServiceType, subscriptionServiceType)
        );
        if(CollectionUtils.isEmpty(subscriptionServiceList)){
            return CommonResult.success(subscriptionServiceQueryVO);
        }
        //过滤最大过期日期
        SubscriptionServiceDO subscriptionService;
        // PIVI取ONLINE-PACK的过期时间
        if(Constants.SERVICE_TYPE.PIVI.equals(subscriptionServiceType)){
            subscriptionService = subscriptionServiceList.stream().filter(serviceDO -> ServicePackageEnum.ONLINE_PACK.getPackageName().equals(serviceDO.getServicePackage()))
                    .findFirst().orElse(null);
            // APPD为空取UNICOM
            if(Objects.isNull(subscriptionService)){
                subscriptionService = subscriptionServiceList.stream().filter(serviceDO -> ServicePackageEnum.DATA_PLAN.getPackageName().equals(serviceDO.getServicePackage()))
                        .findFirst().orElse(new SubscriptionServiceDO());
            }
            // 查询高德的过期时间
            SubscriptionServiceDO aMapService = subscriptionServiceList.stream().filter(serviceDO -> ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage()))
                    .findFirst().orElse(new SubscriptionServiceDO());
            subscriptionServiceQueryVO.setAMapExpiryDate(aMapService.getExpiryDate());

        }else {
            subscriptionService = subscriptionServiceList.stream().max(Comparator.comparing(SubscriptionServiceDO::getExpiryDate))
                    .orElse(new SubscriptionServiceDO());
        }
        subscriptionServiceQueryVO.setCarVin(subscriptionService.getCarVin());
        subscriptionServiceQueryVO.setExpiryDate(subscriptionService.getExpiryDate());
        return CommonResult.success(subscriptionServiceQueryVO);
    }

    @Override
    public CommonResult<Integer> fetchServiceExpiryDateByExpiryRange(SubscriptionServiceExpireFetchDTO queryDTO) {
        // 构建参数并调用tsdp的过期查询服务
        ResponseEntity<JSONArray> response = buildAndCallTsdpExpireService(queryDTO);
        // 解析结果集
        List<SubscriptionServiceBO> list = Collections.synchronizedList(new ArrayList<>());
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            JSONArray body = response.getBody();
            if (CollUtil.isNotEmpty(body)) {
                log.info("本次定时任务抓取到的原始车辆过期数据有：{}条", body.size());
                parseResult(body, list);
            }
        }
        // mock返回数据
        // String jsonStr = "{\"incontrolId\":\"incontrolId_mock\",\"serviceName\":\"serviceName_mock\",\"servicePackage\":\"servicePackage_mock\",\"expiryDateUTC0\":\"2024-08-05 14:29:13\",\"serviceType\":1,\"firstName\":\"firstName_mock\",\"surname\":\"surname_mock\"}";
        // for (int i = 0; i < 100 * 400; i++) {
        //     for (int j = 0; j < 7; j++) {
        //         SubscriptionServiceBO serviceBO = JSON.parseObject(jsonStr, SubscriptionServiceBO.class);
        //         serviceBO.setIncontrolId("incontrolId_" + i);
        //         serviceBO.setCarVin("carVin_" + i);
        //         serviceBO.setPhone("phone_" + i);
        //         serviceBO.setUserid("userid_" + i);
        //         serviceBO.setServicePackage("svcPkg_" + i + j);
        //         serviceBO.setServiceName("sName_" + i + j);
        //         list.add(serviceBO);
        //     }
        // }
        log.info("本次定时任务解析到的服务数量为：{}条", list.size());
        if (CollUtil.isNotEmpty(list)) {
            // save /update t_subscription_service
            return success(saveOrUpdateSubscription(list));
        }
        log.info("本次定时任务执行完成！！");
        return success(0);
    }

    @NotNull
    private ResponseEntity<JSONArray> buildAndCallTsdpExpireService(SubscriptionServiceExpireFetchDTO queryDTO) {
        // 入参校验
        String build = buildParams(queryDTO);
        // 设置header数据
        HttpEntity<String> entity = buildEntity();
        // 发送GET请求
        log.info("TSDP过期服务查询完整请求url:{}", build);
        ResponseEntity<JSONArray> response;
        try {
            response = restTemplate.exchange(build, HttpMethod.GET, entity, JSONArray.class);
        } catch (RestClientException e) {
            log.info("TSDP过期服务查询失败：", e);
            if (e instanceof HttpClientErrorException.Forbidden) {
                log.error("403错误", e);
                throw exception(TSDP_REQUEST_FORBIDDEN);
            }
            if (e instanceof HttpClientErrorException.TooManyRequests) {
                log.error("429错误", e);
                throw exception(TSDP_REQUEST_RATE_TOO_FAST);
            }
            throw e;
        }
        return response;
    }

    @Override
    public CommonResult<Integer> scrapeAndSaveRemoteServiceData(SubscriptionServiceExpireFetchDTO queryDTO) {
        // 构建参数并调用tsdp的过期查询服务
        ResponseEntity<JSONArray> response = buildAndCallTsdpExpireService(queryDTO);
        // 直接保存原始数据
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            JSONArray body = response.getBody();
            if (CollUtil.isNotEmpty(body)) {
                log.info("本次定时任务抓取到的原始车辆过期数据有：{}条", body.size());
                int count = saveOriginalData(body);
                log.info("保存原始remote服务数据完成！");
                return success(count);
            }
        }

        log.info("本批次remote数据保存任务完成！");
        return success(0);
    }

    @Override
    public CommonResult<Long> queryNeedProcessRemoteServiceNum(String dataNo,Integer status) {
        Long count = remoteOriginalDataMapper.selectCount(new LambdaQueryWrapperX<RemoteOriginalDataDO>()
                .eqIfPresent(RemoteOriginalDataDO::getDataNo, dataNo)
                .eq(RemoteOriginalDataDO::getStatus, status)
                .lt(RemoteOriginalDataDO::getMissCount, missCountMax)
        );
        return success(count);
    }

    @Override
    public CommonResult<Map<Integer, Integer>> processRemoteServiceData(String dataNo, Long pageNo, Long pageSize,Integer status) {
        log.info("remotes数据分页处理入参pageNo{}，pageSize{}", pageNo, pageSize);
        Page<RemoteOriginalDataDO> pageData = remoteOriginalDataMapper.selectPage(new Page<>(pageNo, pageSize,false),
                new LambdaQueryWrapperX<RemoteOriginalDataDO>()
                        .eqIfPresent(RemoteOriginalDataDO::getDataNo, dataNo)
                        .eq(RemoteOriginalDataDO::getStatus, status)
                        .lt(RemoteOriginalDataDO::getMissCount, missCountMax)
        );
        List<RemoteOriginalDataDO> records = pageData.getRecords();
        if (CollUtil.isNotEmpty(records)){
            log.info("本次定时任务要处理的原始数据条数：{}条",records.size());
            List<RemoteOriginalDataParseBO> list = Collections.synchronizedList(new ArrayList<>());
            Set<Long> errorIds = parseRawData(records, list);
            if (CollUtil.isNotEmpty(errorIds)){
                // 记录出错数据
                remoteOriginalDataService.updateDataFail(errorIds, RemoteDataErrorType.RAW_ERROR.getType(), RemoteDataErrorType.RAW_ERROR.getDesc());
            }
            // 处理其他数据的
            Map<Integer, Integer> resultMap = saveOrUpdateRemoteOriginalData(list);
            // 返回数据
            Integer success = resultMap.keySet().toArray(new Integer[0])[0];
            Integer fail = resultMap.values().toArray(new Integer[0])[0];
            return success(Map.of(success,fail + errorIds.size()));
        }
        log.info("本次批次定时任务处理结束");
        return success(Map.of(0,0));
    }

    private int saveOriginalData(JSONArray body) {
        List<List<Object>> split = ListUtil.split(body, 10000);
        int count = 0;
        for (List<Object> jsonList : split) {
            ArrayList<RemoteOriginalDataDO> insertList = new ArrayList<>(jsonList.size());
            for (Object obj : jsonList) {
                RemoteOriginalDataDO originalDataDO = new RemoteOriginalDataDO();
                originalDataDO.setDataNo(DateUtil.today());
                originalDataDO.setStatus(Constants.PENDING);
                originalDataDO.setMissCount(0);
                originalDataDO.setRawJson(new JSONObject((Map) obj));
                insertList.add(originalDataDO);
            }
            remoteOriginalDataMapper.insertBatch(insertList);
            count += insertList.size();
        }
        return count;
    }

    /**
     *
     * @param list
     * @return 解析需要处理的车辆数
     */
    private Integer saveOrUpdateSubscription(List<SubscriptionServiceBO> list) {
        // 只处理remote类型的服务
        List<String> remotePackage = remotePackageDOService.getAllRemotePackageCode();
        list.removeIf(e -> !remotePackage.contains(e.getServicePackage()));
        if (CollUtil.isEmpty(list)){
            log.warn("本次定时任务抓取到的服务未找到remote类型的，任务结束");
            return 0;
        }
        Map<String, List<SubscriptionServiceBO>> carServiceMap = list.parallelStream().collect(Collectors.groupingBy(k -> k.getCarVin()));
        log.info("本次定时任务解析到的车辆数为：{}", carServiceMap.size());
        // 每次处理1000辆车数据
        List<List<Map.Entry<String, List<SubscriptionServiceBO>>>> splitList = CollUtil.split(carServiceMap.entrySet(), splitSize);

        SubscriptionServiceApiImpl current = (SubscriptionServiceApiImpl) AopContext.currentProxy();
        for (List<Map.Entry<String, List<SubscriptionServiceBO>>> entries : splitList) {
            List<SubscriptionServiceBO> collect = entries.parallelStream().flatMap(entry -> entry.getValue().parallelStream()).collect(Collectors.toList());
            CompletableFuture.runAsync(() -> {
                current.saveOrUpdateSubscriptionService(collect);
            }).exceptionally(e -> {
                log.error("有一批定时任务执行异常：", e);
                return null;
            });
        }
        return carServiceMap.size();
    }


    /**
     *
     * @param list
     * @return 出参 key为成功处理数量 value为失败处理数量
     */
    private Map<Integer, Integer> saveOrUpdateRemoteOriginalData(List<RemoteOriginalDataParseBO> list) {
        Set<Long> totalIds = convertSet(list, RemoteOriginalDataParseBO::getId);
        // 只处理remote类型的服务
        List<String> remotePackage = remotePackageDOService.getAllRemotePackageCode();
        list.removeIf(e -> !remotePackage.contains(e.getServicePackage()));

        // 将这一部分数据标记为已完成
        Set<Long> leftIds = convertSet(list, RemoteOriginalDataParseBO::getId);
        totalIds.removeAll(leftIds);
        log.info("非remote数据id：{}",totalIds);
        remoteOriginalDataService.updateDataSuccess(totalIds);

        // 数据按车分组处理
        Map<String, List<RemoteOriginalDataParseBO>> carServiceMap = list.parallelStream().collect(Collectors.groupingBy(RemoteOriginalDataParseBO::getCarVin));
        log.info("滤除非remote数据后本次定时任务解析到的车辆数为：{}", carServiceMap.size());

        // 每次处理1000辆车数据
        List<List<Map.Entry<String, List<RemoteOriginalDataParseBO>>>> splitList = CollUtil.split(carServiceMap.entrySet(), splitSize);
        SubscriptionServiceApiImpl current = (SubscriptionServiceApiImpl) AopContext.currentProxy();
        List<CompletableFuture<Map<Integer, Integer>>> futureList = new ArrayList<>(splitList.size());

        // 数量统计
        AtomicInteger success = new AtomicInteger(totalIds.size());
        AtomicInteger fail = new AtomicInteger();
        for (List<Map.Entry<String, List<RemoteOriginalDataParseBO>>> entries : splitList) {
            List<RemoteOriginalDataParseBO> eachBatchData = entries.parallelStream().flatMap(entry -> entry.getValue().parallelStream()).collect(Collectors.toList());
            // 每一批次数据处理
            CompletableFuture<Map<Integer, Integer>> future = CompletableFuture.supplyAsync(() -> current.processEachBatchData(eachBatchData)).exceptionally(e -> {
                log.error("有一批定时任务执行异常：", e);
                // 记录处理失败
                Set<Long> errorIds = convertSet(eachBatchData, RemoteOriginalDataParseBO::getId);
                remoteOriginalDataService.updateDataFail(errorIds, RemoteDataErrorType.OTHER_FAIL.getType(), StrUtil.maxLength(ExceptionUtil.getRootCauseMessage(e),250));
                // 标记失败返回
                return Map.of(0,errorIds.size());
            });
            futureList.add(future);
        }
        // 汇总结果
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
        for (CompletableFuture<Map<Integer, Integer>> mapCompletableFuture : futureList) {
            Map<Integer, Integer> eachProcessResult = mapCompletableFuture.join();
            success.addAndGet(eachProcessResult.keySet().toArray(new Integer[0])[0]);
            fail.addAndGet(eachProcessResult.values().toArray(new Integer[0])[0]);
        }
        return Map.of(success.get(),fail.get());
    }

    private void parseResult(JSONArray body, List<SubscriptionServiceBO> list) {
        body.parallelStream().map(o -> new JSONObject((Map) o)).forEach(bodyJson -> {
            JSONArray subscriptions = bodyJson.getJSONArray("subscriptions");
            JSONObject vehicleInformation = bodyJson.getJSONObject("vehicleInformation");
            JSONObject boundToCustomer = bodyJson.getJSONObject("boundToCustomer");
            String vin = vehicleInformation.getString("vin");
            String email = boundToCustomer.getString("email");
            String phone = boundToCustomer.getString("phone");
            String userid = boundToCustomer.getString("userid");
            String firstName = boundToCustomer.getString("firstName");
            String surname = boundToCustomer.getString("surname");
            SubscriptionServiceBO baseServiceDO = new SubscriptionServiceBO();
            baseServiceDO.setCarVin(vin);
            baseServiceDO.setIncontrolId(email);
            baseServiceDO.setPhone(phone);
            baseServiceDO.setUserid(userid);
            baseServiceDO.setFirstName(firstName);
            baseServiceDO.setSurname(surname);
            baseServiceDO.setServiceType(Constants.SERVICE_TYPE.REMOTE);
            if (CollUtil.isNotEmpty(subscriptions)) {
                for (Object subscription : subscriptions) {
                    JSONObject subscriptionJson = new JSONObject((Map) subscription);
                    String servicePackage = subscriptionJson.getString("servicePackage");
                    Date expiryDate = subscriptionJson.getDate("expiryDate");
                    if (expiryDate == null) {
                        continue;
                    }
                    buildSubscriptionList(list, subscriptionJson, servicePackage, expiryDate, baseServiceDO);
                }
            }
        });
    }

    /**
     * 构建订阅服务列表
     *
     * @param list 存储订阅服务的列表
     * @param subscriptionJson 包含订阅服务信息的JSON对象
     * @param servicePackage 服务包名称
     * @param expiryDate 服务过期日期
     * @param baseServiceDO 用于复制基础属性的订阅服务对象
     */
    private static void buildSubscriptionList(List<SubscriptionServiceBO> list, JSONObject subscriptionJson, String servicePackage, Date expiryDate, SubscriptionServiceBO baseServiceDO) {
        JSONArray services = subscriptionJson.getJSONArray("services");
        if (CollUtil.isNotEmpty(services)) {
            for (Object service : services) {
                SubscriptionServiceBO serviceDO = new SubscriptionServiceBO();
                BeanUtils.copyProperties(baseServiceDO, serviceDO);
                serviceDO.setServiceName(String.valueOf(service));
                serviceDO.setServicePackage(servicePackage);
                serviceDO.setExpiryDateUTC0(LocalDateTime.ofInstant(expiryDate.toInstant(), ZoneId.of("UTC")));
                list.add(serviceDO);
            }
        }
    }

    private Set<Long> parseRawData(List<RemoteOriginalDataDO> records, List<RemoteOriginalDataParseBO> list) {
        Set<Long> errorDataSet = Collections.synchronizedSet(new HashSet<>());
        records.parallelStream().forEach(dataDO -> {
            Long id = dataDO.getId();
            JSONObject rawJson = dataDO.getRawJson();
            JSONArray subscriptions = rawJson.getJSONArray("subscriptions");
            JSONObject vehicleInformation = rawJson.getJSONObject("vehicleInformation");
            JSONObject boundToCustomer = rawJson.getJSONObject("boundToCustomer");
            String vin = vehicleInformation.getString("vin");
            String email = boundToCustomer.getString("email");
            String phone = boundToCustomer.getString("phone");
            String userid = boundToCustomer.getString("userid");
            String firstName = boundToCustomer.getString("firstName");
            String surname = boundToCustomer.getString("surname");
            if (CollUtil.isNotEmpty(subscriptions)) {
                for (Object subscription : subscriptions) {
                    JSONObject subscriptionJson = new JSONObject((Map) subscription);
                    String servicePackage = subscriptionJson.getString("servicePackage");
                    Date expiryDate = subscriptionJson.getDate("expiryDate");
                    JSONArray services = subscriptionJson.getJSONArray("services");
                    if (expiryDate == null || CollUtil.isEmpty(services)) {
                        errorDataSet.add(id);
                        continue;
                    }
                    for (Object service : services) {
                        RemoteOriginalDataParseBO parseBO = new RemoteOriginalDataParseBO();
                        parseBO.setId(id);
                        parseBO.setServiceName(String.valueOf(service));
                        parseBO.setServicePackage(servicePackage);
                        parseBO.setExpiryDateUTC0(LocalDateTime.ofInstant(expiryDate.toInstant(), ZoneId.of("UTC")));
                        parseBO.setCarVin(vin);
                        parseBO.setIncontrolId(email);
                        parseBO.setPhone(phone);
                        parseBO.setUserid(userid);
                        parseBO.setFirstName(firstName);
                        parseBO.setSurname(surname);
                        parseBO.setServiceType(Constants.SERVICE_TYPE.REMOTE);
                        list.add(parseBO);
                    }
                }
            }
        });
        return errorDataSet;
    }

    @NotNull
    private HttpEntity<String> buildEntity() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        headers.set("Accept-encoding", "gzip");
        headers.set("Authorization", ifEcomToken);
        // 创建HttpEntity对象，包含header和body数据
        return new HttpEntity<>(headers);
    }

    private String buildParams(SubscriptionServiceExpireFetchDTO queryDTO) {
        LocalDateTime fromDateTime = queryDTO.getFromDateTime().minusHours(8).withNano(0);
        LocalDateTime toDateTime = queryDTO.getToDateTime().minusHours(8).withNano(999999999);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        String fromDateTimeStr = fromDateTime.format(formatter);
        String toDateTimeStr = toDateTime.format(formatter);
        return UrlBuilder.of(subscriptionExpireUrl)
                .addQuery("fromDateTime", fromDateTimeStr)
                .addQuery("toDateTime", toDateTimeStr).build();
    }


    /**
     * 分页查询服务到期信息
     *
     * @param pageParam 分页参数
     * @return 返回服务到期信息的分页查询结果，包括服务的基本信息和是否到期的状态
     */
    @Override
    public CommonResult<ServiceExpireStatisticVo> getRemoteExpireDateInfoByPage(ServiceExpirePageParam pageParam) {
        ServiceExpireStatisticVo remoteExpireStatisticVo = new ServiceExpireStatisticVo();
        List<SubscriptionServiceDO> serviceDOS = queryRemoteExpireDayByPage(pageParam);
        log.info("remote分页查{}天到期数据, pageParam:{}, 查询的总数:{}", pageParam.getExpireDay(), pageParam, serviceDOS.size());
        if (CollectionUtils.isEmpty(serviceDOS)) {
            remoteExpireStatisticVo.setEndFlag(true);
            return CommonResult.success(remoteExpireStatisticVo);
        }
        List<ServiceExpireVo> serviceExpireVos = buildServiceExpireVoByDO(serviceDOS);
        remoteExpireStatisticVo.setTotal(serviceExpireVos.size());
        List<ServiceExpireVo> addBrandExpireVoList = addBrandNameCarType(serviceExpireVos, remoteExpireStatisticVo);
        List<Boolean> idempotentCheckResp = batchIdempotentCheck(addBrandExpireVoList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        int idempotentFilterCount = 0;
        for (int i = 0; i < Math.min(idempotentCheckResp.size(), addBrandExpireVoList.size()); i++) {
            if (!idempotentCheckResp.get(i)) {
                String key = Constants.SERVICE_EXPIRE_KEY + addBrandExpireVoList.get(i).getCarVin() + ":" +
                        addBrandExpireVoList.get(i).getServiceType();
                log.info("Remote服务过期幂等校验为重复提交, serviceExpireVo:{}, 到期时间毫秒:{}",
                        addBrandExpireVoList.get(i), getExpireTimeMills(key));
                idempotentFilterCount++;
                continue;
            }
            resp.add(addBrandExpireVoList.get(i));
        }
        remoteExpireStatisticVo.setIdempotentFilterCount(idempotentFilterCount);
        remoteExpireStatisticVo.setValidDataList(resp);
        remoteExpireStatisticVo.setEndFlag(false);
        return CommonResult.success(remoteExpireStatisticVo);
    }



    /**
     * 分页查询PIVI设备到期信息
     *
     * @param pageParam 分页参数，包含查询条件和分页信息
     * @return 返回查询结果的分页信息，如果查询结果为空，则返回空列表
     */
    @Override
    public CommonResult<ServiceExpireStatisticVo> getPIVIExpireDateInfoByPage(ServiceExpirePageParam pageParam) {
        ServiceExpireStatisticVo piviExpireStatisticVo = new ServiceExpireStatisticVo();
        List<ServiceExpireVo> serviceExpireVos = queryPIVIExpireDayByPage(pageParam);
        piviExpireStatisticVo.setTotal(serviceExpireVos.size());
        log.info("pivi分页查{}天到期数据, pageParam:{}, 查询的数据总量:{}", pageParam.getExpireDay(), pageParam, serviceExpireVos.size());
        if (CollectionUtils.isEmpty(serviceExpireVos)) {
            piviExpireStatisticVo.setEndFlag(true);
            return CommonResult.success(piviExpireStatisticVo);
        }
        //添加车辆、品牌信息
        List<ServiceExpireVo> carBrandExpireList = addBrandNameCarType(serviceExpireVos, piviExpireStatisticVo);
        List<ServiceExpireVo> validDataList = filterInvalidExpireDateInfo(carBrandExpireList, piviExpireStatisticVo);
        List<Boolean> idempotentCheckResp = batchIdempotentCheck(validDataList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        int idempotentFilterCount = 0;
        for (int i = 0; i < Math.min(idempotentCheckResp.size(), validDataList.size()); i++) {
            if (!idempotentCheckResp.get(i)) {
                String key = Constants.SERVICE_EXPIRE_KEY + validDataList.get(i).getCarVin() + ":" +
                        validDataList.get(i).getServiceType();
                log.info("PIVI服务过期幂等校验为重复提交, serviceExpireVo:{}, 到期时间毫秒:{}",
                        validDataList.get(i), getExpireTimeMills(key));
                idempotentFilterCount++;
                continue;
            }
            resp.add(validDataList.get(i));
        }
        piviExpireStatisticVo.setIdempotentFilterCount(idempotentFilterCount);
        piviExpireStatisticVo.setValidDataList(resp);
        piviExpireStatisticVo.setEndFlag(false);
        return CommonResult.success(piviExpireStatisticVo);
    }

    /**
     * 分页查询PIVI已过期的信息
     *
     * @param pageParam 分页参数，包含查询条件和分页信息
     * @return 返回查询结果的分页信息，如果查询结果为空，则返回空列表
     */
    @Override
    public CommonResult<ServiceExpireStatisticVo> getPIVIExpireDateInfoByPageAfter(ServiceExpirePageParam pageParam) {
        ServiceExpireStatisticVo piviExpireStatisticVo = new ServiceExpireStatisticVo();
        List<ServiceExpireVo> serviceExpireVos = queryPIVIExpireDayByPage(pageParam);
        log.info("分页查询PIVI已过期{}天数据, pageParam:{}, 查询的数据总量:{}", pageParam.getExpireDay(), pageParam, serviceExpireVos.size());
        piviExpireStatisticVo.setTotal(serviceExpireVos.size());
        if (CollectionUtils.isEmpty(serviceExpireVos)) {
            piviExpireStatisticVo.setEndFlag(true);
            return CommonResult.success(piviExpireStatisticVo);
        }
        //添加车辆、品牌信息
        List<ServiceExpireVo> carBrandExpireList = addBrandNameCarType(serviceExpireVos, piviExpireStatisticVo);
        List<ServiceExpireVo> validDataList = filterInvalidExpireDateInfo(carBrandExpireList, piviExpireStatisticVo);
        List<Boolean> idempotentCheckResp = batchIdempotentCheck(validDataList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        int idempotentFilterCount = 0;
        for (int i = 0; i < Math.min(idempotentCheckResp.size(), validDataList.size()); i++) {
            if (!idempotentCheckResp.get(i)) {
                String key = Constants.SERVICE_EXPIRE_KEY + validDataList.get(i).getCarVin() + ":" +
                        validDataList.get(i).getServiceType();
                log.info("分页查询PIVI已过期的信息幂等校验为重复提交, serviceExpireVo:{}, 到期时间毫秒:{}",
                        validDataList.get(i), getExpireTimeMills(key));
                idempotentFilterCount++;
                continue;
            }
            resp.add(validDataList.get(i));
        }
        piviExpireStatisticVo.setIdempotentFilterCount(idempotentFilterCount);
        piviExpireStatisticVo.setValidDataList(resp);
        piviExpireStatisticVo.setEndFlag(false);
        return CommonResult.success(piviExpireStatisticVo);
    }

    /**
     * 未实名-分页查询要过期的信息
     *
     * @param pageParam 分页参数，包含查询条件和分页信息
     * @return 返回查询结果的分页信息，如果查询结果为空，则返回空列表
     */
    @Override
    public CommonResult<ServiceExpireStatisticVo> getPIVIExpireDateInfoByUnnamed(ServiceExpirePageParam pageParam) {
        ServiceExpireStatisticVo piviExpireStatisticVo = new ServiceExpireStatisticVo();
        List<ServiceExpireVo> serviceExpireVos = queryPIVIExpireDayByPage(pageParam);
        log.info("未实名通知分页查{}天到期数据, pageParam:{}, 查询的数据总量:{}", pageParam.getExpireDay(), pageParam, serviceExpireVos.size());
        piviExpireStatisticVo.setTotal(serviceExpireVos.size());
        if (CollectionUtils.isEmpty(serviceExpireVos)) {
            piviExpireStatisticVo.setEndFlag(true);
            return CommonResult.success(piviExpireStatisticVo);
        }
        //添加车辆、品牌信息
        List<ServiceExpireVo> carBrandExpireList = addBrandNameCarType(serviceExpireVos, piviExpireStatisticVo);
        List<ServiceExpireVo> validDataList = filterInvalidInfoByUnnamed(carBrandExpireList, piviExpireStatisticVo);
        List<Boolean> idempotentCheckResp = batchIdempotentCheck(validDataList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        int idempotentFilterCount = 0;
        for (int i = 0; i < Math.min(idempotentCheckResp.size(), validDataList.size()); i++) {
            if (!idempotentCheckResp.get(i)) {
                String key = Constants.SERVICE_EXPIRE_KEY + validDataList.get(i).getCarVin() + ":" +
                        validDataList.get(i).getServiceType();
                log.info("未实名通知幂等校验为重复提交, serviceExpireVo:{}, 到期时间毫秒:{}",
                        validDataList.get(i), getExpireTimeMills(key));
                idempotentFilterCount++;
                continue;
            }
            resp.add(validDataList.get(i));
        }
        piviExpireStatisticVo.setIdempotentFilterCount(idempotentFilterCount);
        piviExpireStatisticVo.setValidDataList(resp);
        piviExpireStatisticVo.setEndFlag(false);
        return CommonResult.success(piviExpireStatisticVo);
    }

    /**
     * 根据订阅服务数据对象构建服务过期信息视图对象列表。
     *
     * @param serviceDOS 订阅服务数据对象列表，包含了需要处理的服务信息。
     * @return 返回构建的服务过期信息视图对象列表，该列表中包含了经过幂等性检查和信息转换后的服务过期信息。
     */
    private List<ServiceExpireVo> buildServiceExpireVoByDO(List<SubscriptionServiceDO> serviceDOS) {
        List<ServiceExpireVo> serviceExpireVos = new ArrayList<>();
        for (SubscriptionServiceDO serviceDO : serviceDOS) {
            ServiceExpireVo serviceExpireVo = new ServiceExpireVo();
            BeanUtils.copyProperties(serviceDO, serviceExpireVo);
            serviceExpireVo.setUuid(UUID.randomUUID().toString().replace("-", ""));
            serviceExpireVos.add(serviceExpireVo);
        }
        return serviceExpireVos;
    }

    /**
     * 获取指定键的过期时间
     *
     * @param key 键，用于指定要查询的缓存项
     * @return 返回缓存项的过期时间，单位为毫秒；如果缓存项不存在或永不过期，则返回-1
     */
    public Long getExpireTimeMills(String key) {
        return redisTemplate.getExpire(key, TimeUnit.MILLISECONDS);
    }


    /**
     * 过滤不合法的carVin
     *
     * @param serviceExpireVoList 服务到期数据
     * @param piviExpireStatisticVo pivi过期数据统计
     * @return CommonResult<List < ServiceExpireVo>>
     */
    private List<ServiceExpireVo> filterInvalidExpireDateInfo(List<ServiceExpireVo> serviceExpireVoList,
                                                              ServiceExpireStatisticVo piviExpireStatisticVo) {
        log.info("过滤不合法的carVin, 待过滤总数据量:{}", serviceExpireVoList.size());
        //1.过滤carVin黑名单
        List<ServiceExpireVo> blackListResp = filterBlackList(serviceExpireVoList);
        int blackFilterCount = Math.abs(serviceExpireVoList.size() - blackListResp.size());
        //2.过滤没有实名认证的carVin
        List<ServiceExpireVo> filterUnicomResp = filterUnicomByUnnamed(blackListResp);
        int unicomFilterCount = Math.abs(blackListResp.size() - filterUnicomResp.size());
        //3.过滤AMAP到期时间<ecp到期时间的carVin
        List<ServiceExpireVo> resp = filterAmaPExpireDateList(filterUnicomResp);
        int ecpFilterCount = Math.abs(filterUnicomResp.size() - resp.size());

        piviExpireStatisticVo.setBlackFilterCount(blackFilterCount);
        piviExpireStatisticVo.setUnicomFilterCount(unicomFilterCount);
        piviExpireStatisticVo.setEcpFilterCount(ecpFilterCount);
        return resp;
    }

    /**
     * 根据条件过滤掉不合法的服务到期信息
     *
     * @param serviceExpireVoList 服务到期信息列表
     * @param piviExpireStatisticVo 统计过滤结果的对象
     * @param conditionDTO 自动任务通知条件DTO，用于指定过滤条件
     * @return 过滤后的服务到期信息列表
     */
    private List<ServiceExpireVo> filterInvalidByCondition(List<ServiceExpireVo> serviceExpireVoList,
                                                           ServiceExpireStatisticVo piviExpireStatisticVo,
                                                           AutoTaskNotifyConditionDTO conditionDTO) {
        log.info("过滤不合法的carVin, conditionDTO:{}, 待过滤总数据量:{}", conditionDTO, serviceExpireVoList.size());
        List<ServiceExpireVo> blackListResp = serviceExpireVoList;
        int blackFilterCount = 0;
        //1.过滤carVin黑名单 (只有PIVI到期前通知过滤黑名单)
        if (ExpirationIntervalEnum.BEFORE_EXPIRED.getType().equals(conditionDTO.getExpirationInterval())) {
            blackListResp = filterBlackList(serviceExpireVoList);
            blackFilterCount = Math.abs(serviceExpireVoList.size() - blackListResp.size());
        }
        //2.联通按照条件过滤
        List<ServiceExpireVo> filterUnicomResp = blackListResp;
        if (RealNameStatusEnum.RNR_TRUE.getType().equals(conditionDTO.getRealNameStatus())) {
            filterUnicomResp = filterUnicomUnnamedByCondition(blackListResp);
        } else if (RealNameStatusEnum.RNR_FALSE.getType().equals(conditionDTO.getRealNameStatus())) {
            filterUnicomResp = filterUnicomRealNameByCondition(blackListResp);
        }
        int unicomFilterCount = Math.abs(blackListResp.size() - filterUnicomResp.size());
        //3.过滤AMAP到期时间<ecp到期时间的carVin
        List<ServiceExpireVo> resp = filterAmaPExpireDateList(filterUnicomResp);
        int ecpFilterCount = Math.abs(filterUnicomResp.size() - resp.size());

        piviExpireStatisticVo.setBlackFilterCount(blackFilterCount);
        piviExpireStatisticVo.setUnicomFilterCount(unicomFilterCount);
        piviExpireStatisticVo.setEcpFilterCount(ecpFilterCount);
        log.info("过滤不合法的carVin, conditionDTO:{}, 过滤后总数据量:{}, blackFilterCount:{}, unicomFilterCount:{}, ecpFilterCount:{}",
                conditionDTO, resp.size(), blackFilterCount, unicomFilterCount, ecpFilterCount);
        return resp;
    }

    /**
     * 未实名-过滤不合法的carVin
     *
     * @param serviceExpireVoList 服务到期数据
     * @param piviExpireStatisticVo pivi过期数据统计
     * @return CommonResult<List < ServiceExpireVo>>
     */
    private List<ServiceExpireVo> filterInvalidInfoByUnnamed(List<ServiceExpireVo> serviceExpireVoList,
                                                             ServiceExpireStatisticVo piviExpireStatisticVo) {
        log.info("未实名过滤不合法的carVin, 待过滤总数据量:{}", serviceExpireVoList.size());
        //1.过滤carVin黑名单
        List<ServiceExpireVo> blackListResp = filterBlackList(serviceExpireVoList);
        int blackFilterCount = Math.abs(serviceExpireVoList.size() - blackListResp.size());
        //2.过滤已实名认证的carVin
        List<ServiceExpireVo> filterUnicomResp = filterUnicomByRealName(blackListResp);
        int unicomFilterCount = Math.abs(blackListResp.size() - filterUnicomResp.size());
        //3.过滤AMAP到期时间<ecp到期时间的carVin
        List<ServiceExpireVo> resp = filterAmaPExpireDateList(filterUnicomResp);
        int ecpFilterCount = Math.abs(filterUnicomResp.size() - resp.size());

        piviExpireStatisticVo.setBlackFilterCount(blackFilterCount);
        piviExpireStatisticVo.setUnicomFilterCount(unicomFilterCount);
        piviExpireStatisticVo.setEcpFilterCount(ecpFilterCount);
        return resp;
    }

    /**
     * 已过期过滤不合法的carVin
     *
     * @param serviceExpireVoList 服务到期数据
     * @param piviExpireStatisticVo pivi过期数据统计
     * @return CommonResult<List < ServiceExpireVo>>
     */
    private List<ServiceExpireVo> filterInvalidExpireDateInfoAfter(List<ServiceExpireVo> serviceExpireVoList,
                                                                   ServiceExpireStatisticVo piviExpireStatisticVo) {
        log.info("已过期过滤不合法的carVin, 待过滤总数据量:{}", serviceExpireVoList.size());
        //2.过滤没有实名认证的carVin
        List<ServiceExpireVo> filterUnicomResp = filterUnicomByUnnamed(serviceExpireVoList);
        int unicomFilterCount = Math.abs(serviceExpireVoList.size() - filterUnicomResp.size());
        //3.过滤AMAP到期时间<ecp到期时间的carVin
        List<ServiceExpireVo> resp = filterAmaPExpireDateList(filterUnicomResp);
        int ecpFilterCount = Math.abs(filterUnicomResp.size() - resp.size());

        piviExpireStatisticVo.setUnicomFilterCount(unicomFilterCount);
        piviExpireStatisticVo.setEcpFilterCount(ecpFilterCount);
        return resp;
    }

    /**
     * 过滤出高德服务到期时间晚于ECP服务到期时间的车辆信息列表。
     *
     * @param serviceExpireVoList 车辆服务过期信息列表
     * @return 过滤后的车辆服务过期信息列表，仅包含高德服务到期时间早于或等于ECP服务到期时间的车辆
     */
    private List<ServiceExpireVo> filterAmaPExpireDateList(List<ServiceExpireVo> serviceExpireVoList) {
        log.info("过滤出高德服务到期时间晚于ECP服务到期时间的车辆信息列表, 待过滤总数量:{}", serviceExpireVoList.size());
        if (CollUtil.isEmpty(serviceExpireVoList)) {
            return new ArrayList<>();
        }
        List<String> allCarVinList = serviceExpireVoList.stream()
                .map(ServiceExpireVo::getCarVin).collect(Collectors.toList());
        Map<String, SubscriptionServiceDO> amapServiceDOMap = getSubscriptionServiceDOMap(allCarVinList);
        List<String> packageCarVinList = filterServiceDOCarVinList(amapServiceDOMap, allCarVinList);
        Map<String, PIVIPackageDO> packageDOMap = getPIVIPackageDOMap(packageCarVinList);
        List<ServiceExpireVo> filterSubscriptionAmapList = filterAmaPExpireDateSubscription(serviceExpireVoList, amapServiceDOMap);
        List<ServiceExpireVo> resp = filterAmaPExpireDatePackage(filterSubscriptionAmapList, packageDOMap);
        log.info("过滤出高德服务到期时间晚于ECP服务到期时间的车辆信息列表, 过滤后合法总数量:{}", resp.size());
        return resp;
    }

    /**
     * PIVIPackage过滤掉高德地图过期时间早于车辆服务过期时间
     *
     * @param serviceExpireVoList 车辆服务过期信息列表，包含每个车辆的vin和服务过期时间等信息。
     * @param packageDOMap 高德地图包信息映射表，通过车辆vin查找对应的高德地图包信息。
     * @return 过滤后的车辆服务过期信息列表，不包含高德地图包过期时间早于车辆服务过期时间的记录。
     */
    private List<ServiceExpireVo> filterAmaPExpireDatePackage(List<ServiceExpireVo> serviceExpireVoList,
                                                              Map<String,PIVIPackageDO> packageDOMap) {
        log.info("PIVIPackage过滤掉高德地图过期时间早于车辆服务过期时间, 待过滤数据总量:{}", serviceExpireVoList.size());
        List<ServiceExpireVo> resp = new ArrayList<>();
        for (ServiceExpireVo serviceExpireVo : serviceExpireVoList) {
            PIVIPackageDO piviPackageDO = packageDOMap.get(serviceExpireVo.getCarVin());
            if (Objects.nonNull(piviPackageDO) &&  Objects.nonNull(piviPackageDO.getExpiryDate()) &&
                    piviPackageDO.getAmaPExpireDate().toLocalDate().isBefore(serviceExpireVo.getExpiryDate().toLocalDate())
                    && LocalDateTime.now().toLocalDate().isBefore(serviceExpireVo.getExpiryDate().toLocalDate())) {
                log.info("该carVin在PIVIPackage, 且高德到期时间小于ECP过期时间，进行过滤，piviPackageDO:{}, serviceExpireVo:{}",
                        piviPackageDO, serviceExpireVo);
                continue;
            }
            resp.add(serviceExpireVo);
        }
        log.info("PIVIPackage过滤掉高德地图过期时间早于车辆服务过期时间, 过滤后合法数据总量:{}", resp.size());
        return resp;
    }

    /**
     * subscription过滤掉高德地图服务到期时间早于ECP服务到期时间。
     *
     * @param serviceExpireVoList 服务过期信息列表，包含所有车辆的服务过期信息。
     * @param amapServiceDOMap 高德地图服务信息映射表，key为车辆vin号，value为对应的高德地图服务信息。
     * @return 过滤后的服务过期信息列表，仅包含高德地图服务到期时间晚于或等于ECP服务到期时间的车辆信息。
     */
    private List<ServiceExpireVo> filterAmaPExpireDateSubscription(List<ServiceExpireVo> serviceExpireVoList,
                                                                   Map<String,SubscriptionServiceDO> amapServiceDOMap) {
        log.info("subscription过滤掉高德地图服务到期时间早于ECP服务到期时间, 待过滤数据量:{}", serviceExpireVoList.size());
        List<ServiceExpireVo> resp = new ArrayList<>();
        for (ServiceExpireVo serviceExpireVo : serviceExpireVoList) {
            SubscriptionServiceDO amaPServiceDO = amapServiceDOMap.get(serviceExpireVo.getCarVin());
            if (Objects.nonNull(amaPServiceDO) &&  Objects.nonNull(amaPServiceDO.getExpiryDate()) &&
                    amaPServiceDO.getExpiryDate().toLocalDate().isBefore(serviceExpireVo.getExpiryDate().toLocalDate())
                    && LocalDateTime.now().toLocalDate().isBefore(serviceExpireVo.getExpiryDate().toLocalDate())) {
                log.info("该carVin在subscription, 且高德到期时间小于ECP过期时间，进行过滤，amaPServiceDO:{}, serviceExpireVo:{}",
                        amaPServiceDO, serviceExpireVo);
                continue;
            }
            resp.add(serviceExpireVo);
        }
        log.info("subscription过滤掉高德地图服务到期时间早于ECP服务到期时间, 过滤后合法滤数据量:{}", resp.size());
        return resp;
    }

    /**
     *
     * 根据车辆VIN列表获取对应的PIVIPackageDO对象的映射。
     *
     * @param packageCarVinList 车辆VIN列表，用于查询PIVIPackageDO信息。
     * @return 返回一个映射，其中包含车辆VIN和对应的PIVIPackageDO对象。
     */
    private Map<String, PIVIPackageDO> getPIVIPackageDOMap(List<String> packageCarVinList) {
        log.info("根据车辆VIN列表获取对应的PIVIPackageDO对象的映射, packageCarVinList:{}", packageCarVinList);
        if (CollUtil.isEmpty(packageCarVinList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<PIVIPackageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PIVIPackageDO::getVin, packageCarVinList)
                .eq(PIVIPackageDO::getIsDeleted, false);
        List<PIVIPackageDO> packageDOS = new ArrayList<>();
        try {
            packageDOS = piviPackageDOMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("根据车辆VIN列表获取相应的PIVIPackage信息列表异常:", e);
        }
        if (CollUtil.isEmpty(packageDOS)) {
            return new HashMap<>();
        }
        return packageDOS.stream().collect(Collectors.toMap(PIVIPackageDO::getVin, Function.identity(), (v1,v2) -> v1));
    }

    /**
     * 过滤掉已订阅服务的车辆VIN列表。
     *
     * @param serviceDOMap 一个映射，其中包含车辆VIN和对应的订阅服务信息。如果车辆VIN存在于映射中，则表示该车辆已订阅服务。
     * @param allCarVinList 所有车辆的VIN列表，无论是否已订阅服务。
     * @return 未订阅任何服务的车辆VIN列表。
     */
    private List<String> filterServiceDOCarVinList(Map<String, SubscriptionServiceDO> serviceDOMap,
                                                   List<String> allCarVinList) {
        if (CollUtil.isEmpty(serviceDOMap)) {
            return allCarVinList;
        }
        List<String> resp = new ArrayList<>();
        for (String carVin : allCarVinList) {
            if (serviceDOMap.containsKey(carVin)) {
                continue;
            }
            resp.add(carVin);
        }
        return resp;
    }


    /**
     * 根据车辆VIN列表获取AMAP订阅服务信息的映射表。
     *
     * @param carVinList 车辆VIN列表，用于查询订阅服务信息。
     * @return 返回一个映射表，其中键是车辆VIN，值是对应的SubscriptionServiceDO对象。
     *         如果输入列表为空或查询结果为空，则返回一个空的映射表。
     *         该映射表用于快速查找特定车辆的订阅服务信息。
     */
    private Map<String, SubscriptionServiceDO> getSubscriptionServiceDOMap(List<String> carVinList) {
        if (CollUtil.isEmpty(carVinList)) {
            return new HashMap<>();
        }
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SubscriptionServiceDO::getCarVin, carVinList)
                .eq(SubscriptionServiceDO::getIsDeleted, false)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                .eq(SubscriptionServiceDO::getServicePackage, ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName());
        List<SubscriptionServiceDO> serviceDOList = new ArrayList<>();
        try {
            serviceDOList = subscriptionServiceMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("根据车辆VIN列表获取AMAP订阅服务信息的映射表异常:", e);
        }
        if (CollUtil.isEmpty(serviceDOList)) {
            return new HashMap<>();
        }
        return serviceDOList.stream().collect(Collectors.toMap(SubscriptionServiceDO::getCarVin,
                Function.identity(), (v1,v2) -> v1));
    }

    /**
     *  根据条件过滤联通未实名认证
     *
     * @param serviceExpireVoList 待过滤的服务到期列表（包含iccid和车架号信息）
     * @return 符合实名认证要求的服务到期列表
     */
    private List<ServiceExpireVo> filterUnicomUnnamedByCondition(List<ServiceExpireVo> serviceExpireVoList) {
        log.info("根据条件过滤联通未实名认证，待过滤数据总量:{}", serviceExpireVoList.size());
        //修改iccid为package的iccid
        updateIccidToPackage(serviceExpireVoList);
        Map<String, ServiceExpireVo> map = getServiceExpireVoMap(serviceExpireVoList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        List<SimCardInfoDTO> simCardInfoDTOList = getSimCardInfoDTOList(serviceExpireVoList);
        List<UnicomRespVO> unicomRespVOList = PIVIUnicomService.getSimCardInfoSync(simCardInfoDTOList);
        for (int i = 0; i < Math.min(serviceExpireVoList.size(), unicomRespVOList.size()); i++) {
            String key = serviceExpireVoList.get(i).getIccid() + "," + serviceExpireVoList.get(i).getCarVin();
            if (Objects.isNull(unicomRespVOList.get(i)) || Objects.isNull(unicomRespVOList.get(i).getUnicomRespData())
                    || Objects.isNull(unicomRespVOList.get(i).getUnicomRespData().getSimCardInfo())) {
                log.info("根据条件过滤联通未实名认证，进行过滤，serviceExpireVo:{}", map.get(key));
                continue;
            } else if (UnicomRealnameFlagEnum.FALSE.getCode().equals(unicomRespVOList.get(i).getUnicomRespData().getSimCardInfo().getRealnameFlag())) {
                log.info("根据条件过滤联通未实名认证，进行过滤，serviceExpireVo:{}", map.get(key));
                continue;
            }
            resp.add(map.get(key));
        }
        log.info("根据条件过滤联通未实名认证，过滤后合法的数据总量:{}", resp.size());
        return resp;
    }

    /**
     * 更新ServiceExpireVo列表中的ICCID信息为PIVIPackage。
     *
     * @param serviceExpireVoList 包含服务过期信息的列表，每个元素为 ServiceExpireVo对象。
     *
     */
    public void updateIccidToPackage(List<ServiceExpireVo> serviceExpireVoList) {
        log.info("更新ServiceExpireVo列表中的ICCID信息为PIVIPackage, serviceExpireVoList的数量:{}", serviceExpireVoList.size());
        if (CollUtil.isEmpty(serviceExpireVoList)) {
            return ;
        }
        List<String> carVinList = serviceExpireVoList.stream()
                .map(ServiceExpireVo::getCarVin)
                .distinct()
                .collect(Collectors.toList());
        Map<String, PIVIPackageDO> vinToPackageMap = getVinToPackageMap(carVinList);
        for (ServiceExpireVo serviceExpireVo : serviceExpireVoList) {
            if (!vinToPackageMap.containsKey(serviceExpireVo.getCarVin())) {
                log.info("更新ServiceExpireVo列表中的ICCID信息为PIVIPackage, 未查询到对应的PIVIPackageDO, serviceExpireVo:{}", serviceExpireVo);
                continue;
            }
            serviceExpireVo.setIccid(vinToPackageMap.get(serviceExpireVo.getCarVin()).getIccid());
        }
    }

    /**
     * 生成一个以VIN为键、PIVIPackageDO对象为值的映射表。
     *
     * @param carVinList 车辆识别码（VIN）列表。如果列表为空或为null，则返回一个空的映射表。
     * @return 一个Map对象，其中键为VIN字符串，值为对应的PIVIPackageDO对象。
     *
     */
    public Map<String, PIVIPackageDO> getVinToPackageMap(List<String> carVinList) {
        log.info("生成一个以VIN为键、PIVIPackageDO对象为值的映射表，carVinList的数量:{}", carVinList.size());
        Map<String, PIVIPackageDO> map = new HashMap<>();
        if (CollUtil.isEmpty(carVinList)) {
            return new HashMap<>();
        }
        List<PIVIPackageDO> packageDOList = batchQueryPackageDOsByVin(carVinList);
        for (PIVIPackageDO packageDO : packageDOList) {
            map.put(packageDO.getVin(), packageDO);
        }
        log.info("生成一个以VIN为键、PIVIPackageDO对象为值的映射表，packageDOList的数量:{}, map的数量：{}", packageDOList.size(), map.size());
        return map;
    }

    /**
     * 根据车辆识别号（VIN）列表批量查询包装对象
     *
     * @param carVinList 车辆识别号（VIN）列表，用于查询包装对象
     * @return 返回一个包装对象列表，如果查询不到任何记录，则返回空列表
     */
    public  List<PIVIPackageDO> batchQueryPackageDOsByVin(List<String> carVinList) {
        if (CollUtil.isEmpty(carVinList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<PIVIPackageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PIVIPackageDO::getVin, carVinList)
                .eq(PIVIPackageDO::getIsDeleted, false);
        return piviPackageDOMapper.selectList(queryWrapper);
    }


    /**
     * 过滤中国联通服务列表，移除未实名认证状态为未认证的项目。
     *
     * @param serviceExpireVoList 服务到期信息列表，包含ICCID和车架号等信息。
     * @return 过滤后的服务到期信息列表，不包含实名认证状态为未认证的项目。
     */
    private List<ServiceExpireVo> filterUnicomByUnnamed(List<ServiceExpireVo> serviceExpireVoList) {
        log.info("过滤未实名认证状态为的数据，待过滤数据总量:{}", serviceExpireVoList.size());
        //修改iccid为package的iccid
        updateIccidToPackage(serviceExpireVoList);
        Map<String, ServiceExpireVo> map = getServiceExpireVoMap(serviceExpireVoList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        List<SimCardInfoDTO> simCardInfoDTOList = getSimCardInfoDTOList(serviceExpireVoList);
        List<UnicomRespVO> unicomRespVOList = PIVIUnicomService.getSimCardInfoList(simCardInfoDTOList);
        for (int i = 0; i < Math.min(serviceExpireVoList.size(), unicomRespVOList.size()); i++) {
            String key = serviceExpireVoList.get(i).getIccid() + "," + serviceExpireVoList.get(i).getCarVin();
            if (Objects.isNull(unicomRespVOList.get(i)) || Objects.isNull(unicomRespVOList.get(i).getUnicomRespData())
                    || Objects.isNull(unicomRespVOList.get(i).getUnicomRespData().getSimCardInfo())) {
                log.info("过滤未实名认证当前数据没有返回数据，进行过滤，serviceExpireVo:{}", map.get(key));
                continue;
            } else if (UnicomRealnameFlagEnum.FALSE.getCode().equals(unicomRespVOList.get(i).getUnicomRespData().getSimCardInfo().getRealnameFlag())) {
                log.info("过滤未实名认证当前数据没有实名认证，进行过滤，serviceExpireVo:{}", map.get(key));
                continue;
            }
            resp.add(map.get(key));
        }
        log.info("过滤未实名认证状态为的数据，过滤后合法的数据总量:{}", resp.size());
        return resp;
    }

    /**
     * 根据条件过滤联通已实名
     *
     * @param serviceExpireVoList 服务到期信息列表，包含ICCID和车架号等信息。
     * @return 过滤后的服务到期信息列表，不包含实名认证状态为未认证的项目。
     */
    private List<ServiceExpireVo> filterUnicomRealNameByCondition(List<ServiceExpireVo> serviceExpireVoList) {
        log.info("根据条件过滤联通已实名，待过滤数据总量:{}", serviceExpireVoList.size());
        //修改Iccid为package的Iccid
        updateIccidToPackage(serviceExpireVoList);
        Map<String, ServiceExpireVo> map = getServiceExpireVoMap(serviceExpireVoList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        List<SimCardInfoDTO> simCardInfoDTOList = getSimCardInfoDTOList(serviceExpireVoList);
        List<UnicomRespVO> unicomRespVOList = PIVIUnicomService.getSimCardInfoSync(simCardInfoDTOList);
        for (int i = 0; i < Math.min(serviceExpireVoList.size(), unicomRespVOList.size()); i++) {
            String key = serviceExpireVoList.get(i).getIccid() + "," + serviceExpireVoList.get(i).getCarVin();
            if (Objects.isNull(unicomRespVOList.get(i)) || Objects.isNull(unicomRespVOList.get(i).getUnicomRespData())
                    || Objects.isNull(unicomRespVOList.get(i).getUnicomRespData().getSimCardInfo())) {
                log.info("根据条件过滤联通已实名，进行过滤，实名数据为空，serviceExpireVo:{}", serviceExpireVoList.get(i));
            } else if (UnicomRealnameFlagEnum.FALSE.getCode().equals(unicomRespVOList.get(i).getUnicomRespData().getSimCardInfo().getRealnameFlag())) {
                resp.add(map.get(key));
            }
        }
        log.info("根据条件过滤联通已实名，过滤后合法的数据总量:{}", resp.size());
        return resp;
    }

    /**
     * 过滤中国联通服务列表，移除已实名的数据。
     *
     * @param serviceExpireVoList 服务到期信息列表，包含ICCID和车架号等信息。
     * @return 过滤后的服务到期信息列表，不包含实名认证状态为未认证的项目。
     */
    private List<ServiceExpireVo> filterUnicomByRealName(List<ServiceExpireVo> serviceExpireVoList) {
        log.info("过滤已实名的数据，待过滤数据总量:{}", serviceExpireVoList.size());
        //修改iccid为package的iccid
        updateIccidToPackage(serviceExpireVoList);
        Map<String, ServiceExpireVo> map = getServiceExpireVoMap(serviceExpireVoList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        List<SimCardInfoDTO> simCardInfoDTOList = getSimCardInfoDTOList(serviceExpireVoList);
        List<UnicomRespVO> unicomRespVOList = PIVIUnicomService.getSimCardInfoList(simCardInfoDTOList);
        for (int i = 0; i < Math.min(serviceExpireVoList.size(), unicomRespVOList.size()); i++) {
            String key = serviceExpireVoList.get(i).getIccid() + "," + serviceExpireVoList.get(i).getCarVin();
            if (Objects.isNull(unicomRespVOList.get(i)) || Objects.isNull(unicomRespVOList.get(i).getUnicomRespData())
                    || Objects.isNull(unicomRespVOList.get(i).getUnicomRespData().getSimCardInfo())) {
                log.info("过滤已实名的数据，进行过滤，实名数据为空，serviceExpireVo:{}", serviceExpireVoList.get(i));
            } else if (UnicomRealnameFlagEnum.FALSE.getCode().equals(unicomRespVOList.get(i).getUnicomRespData().getSimCardInfo().getRealnameFlag())) {
                resp.add(map.get(key));
            }
        }
        log.info("过滤已实名的数据，过滤后合法的数据总量:{}", resp.size());
        return resp;
    }

    /**
     * 将服务到期信息列表转换为Map，使用iccid和carVin作为键
     *
     * @param serviceExpireVoList 服务到期信息列表，不得为空
     * @return 包含服务到期信息的Map，键为iccid和carVin的组合
     */
    private Map<String, ServiceExpireVo> getServiceExpireVoMap(List<ServiceExpireVo> serviceExpireVoList) {
        log.info("将服务到期信息列表转换为Map，使用iccid和carVin作为键, 待处理数据量:{}", serviceExpireVoList.size());
        Map<String, ServiceExpireVo> map = new HashMap<>();
        if (CollUtil.isEmpty(serviceExpireVoList)) {
            return map;
        }
        for (ServiceExpireVo serviceExpireVo : serviceExpireVoList) {
            String key = serviceExpireVo.getIccid() + "," + serviceExpireVo.getCarVin();
            map.put(key, serviceExpireVo);
        }
        log.info("将服务到期信息列表转换为Map，使用iccid和carVin作为键, 处理后数据总量:{}", map.size());
        return map;
    }

    /**
     * 根据服务到期信息列表获取SIM卡信息DTO列表
     *
     * @param serviceExpireVoList 服务到期信息列表，包含ICCID和车辆VIN号等信息
     * @return SimCardInfoDTO列表，每个DTO包含简化或占位符信息的ICCID和车辆VIN号
     */
    private List<SimCardInfoDTO> getSimCardInfoDTOList(List<ServiceExpireVo> serviceExpireVoList) {
        List<SimCardInfoDTO> simCardInfoDTOList = new ArrayList<>();
        for (ServiceExpireVo serviceExpireVo : serviceExpireVoList) {
            SimCardInfoDTO simCardInfoDTO = new SimCardInfoDTO();
            String iccid = StringUtils.isBlank(serviceExpireVo.getIccid()) ? "iccid" : serviceExpireVo.getIccid();
            String carVin = StringUtils.isBlank(serviceExpireVo.getCarVin()) ? "carVin" : serviceExpireVo.getCarVin();
            simCardInfoDTO.setIccid(iccid);
            simCardInfoDTO.setVin(carVin);
            simCardInfoDTOList.add(simCardInfoDTO);
        }
        return simCardInfoDTOList;
    }


    /**
     * 过滤黑名单中的车辆信息
     *
     * @param serviceExpireVoList 待检查的车辆服务过期信息列表。
     * @return 过滤后的车辆服务过期信息列表，不包含在黑名单中的车辆。
     */
    private List<ServiceExpireVo> filterBlackList(List<ServiceExpireVo> serviceExpireVoList) {
        log.info("过滤黑名单中的车辆信息, 待过滤总数量:{}", serviceExpireVoList.size());
        List<ServiceExpireVo> resp = new ArrayList<>();
        if (CollUtil.isEmpty(serviceExpireVoList)) {
            return resp;
        }
        List<String> carVinList = serviceExpireVoList.stream()
                .map(ServiceExpireVo::getCarVin).collect(Collectors.toList());
        CommonResult<List<MsgBlackListDTO>> msgBlackListDTOS;
        try {
            msgBlackListDTOS = notificationServiceApi.checkCarVinBlackList(carVinList);
            log.info("过滤黑名单中的车辆信息结果, code:{}, msg:{}", msgBlackListDTOS.getCode(), msgBlackListDTOS.getMsg());
        } catch (Exception e) {
            log.error("过滤黑名单中的车辆信息,获取黑名单异常:", e);
            return serviceExpireVoList;
        }
        if (!msgBlackListDTOS.isSuccess() || CollUtil.isEmpty(msgBlackListDTOS.getData())) {
            log.info("过滤黑名单中的车辆信息,获取黑名单请求结果错误或数据为空");
            return serviceExpireVoList;
        }
        log.info("过滤黑名单中的车辆信息, 黑名单总数量：{}", msgBlackListDTOS.getData().size());
        Map<String, MsgBlackListDTO> msgBlackListDTOMap = msgBlackListDTOS.getData().stream()
                .collect(Collectors.toMap(
                        MsgBlackListDTO::getCarVin,
                        Function.identity(),
                        (v1, v2) -> v1
                ));
        for (ServiceExpireVo serviceExpireVo : serviceExpireVoList) {
            if (msgBlackListDTOMap.containsKey(serviceExpireVo.getCarVin())) {
                log.info("过滤不合法的carVin, 当前数据在黑名单中直接过滤，serviceExpireVo:{}", serviceExpireVo);
                continue;
            }
            resp.add(serviceExpireVo);
        }
        log.info("过滤黑名单中的车辆信息, 过滤后合法的总数量:{}", resp.size());
        return resp;
    }

    /***
     * 使用管道批量
     * 对生成的服务过期数据幂等校验, 保证同样的数据, 每天只能发送一条。按照传入数据的顺序，依次返回校验的结果
     * @param serviceExpireVoList 服务到期数据
     * @return List<Boolean>
     * */
    public List<Boolean> batchIdempotentCheck(List<ServiceExpireVo> serviceExpireVoList) {
        List<Boolean> checkResp = new ArrayList<>();
        if (redisTemplate == null || CollectionUtils.isEmpty(serviceExpireVoList)) {
            return checkResp;
        }
        for (ServiceExpireVo serviceExpireVo : serviceExpireVoList) {
            String key = Constants.SERVICE_EXPIRE_KEY + serviceExpireVo.getCarVin() + ":" + serviceExpireVo.getServiceType();
            Boolean res = redisTemplate.opsForValue().setIfAbsent(key, OK, getIdempotentTimeoutMillis(), TimeUnit.MILLISECONDS);
            checkResp.add(res);
        }
        return checkResp;
    }

    /**
     * 获取当天剩余时间的毫秒数，用于设置幂次校验的超时时间
     *
     * @return 返回当天剩余时间的毫秒数
     */
    private static long  getIdempotentTimeoutMillis() {
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();

        // 当天的最大时间，即小时、分钟、秒、纳秒均为最大值
        LocalTime maxTime = LocalTime.MAX;
        LocalDateTime maxLocalDateTime = LocalDateTime.of(now.toLocalDate(), maxTime);

        // 计算差值
        Duration duration = Duration.between(now, maxLocalDateTime);
        return duration.toMillis();
    }

    /**
     * 添加品牌和车辆信息
     * @param serviceExpireVos 服务到期数据列表
     * @param serviceExpireStatisticVo 统计信息
     * @return List<ServiceExpireVo>
     * */
    public List<ServiceExpireVo> addBrandNameCarType(List<ServiceExpireVo> serviceExpireVos,
                                                     ServiceExpireStatisticVo serviceExpireStatisticVo) {
        log.info("添加品牌和车辆信息, 待处理数据总量：{}", serviceExpireVos.size());
        if (CollectionUtils.isEmpty(serviceExpireVos)) {
            return serviceExpireVos;
        }
        List<String> carVinList = serviceExpireVos.stream().map(ServiceExpireVo::getCarVin).collect(Collectors.toList());
        List<IncontrolVehicleDO> incontrolVehicleDOList = getVehicleDOListByCarVinList(carVinList);
        if (CollectionUtils.isEmpty(incontrolVehicleDOList)) {
            return serviceExpireVos;
        }
        List<String> seriesCodeList = incontrolVehicleDOList.stream().map(IncontrolVehicleDO::getSeriesCode).distinct()
                .collect(Collectors.toList());
        Map<String, String> seriesNameMap = getSeriesNameMap(seriesCodeList);
        Map<String, IncontrolVehicleDO> vehicleDoMap = getVehicleDoMap(incontrolVehicleDOList);
        log.info("添加品牌和车辆信息, 到期的数量serviceExpireVos:{}, 车辆的数量vehicleDoMap:{}", serviceExpireVos.size(), vehicleDoMap.size());
        if (CollectionUtils.isEmpty(vehicleDoMap)) {
            return serviceExpireVos;
        }
        List<ServiceExpireVo> resp = new ArrayList<>();
        for (ServiceExpireVo serviceExpireVo : serviceExpireVos) {
            if (Objects.isNull(vehicleDoMap.get(serviceExpireVo.getCarVin()))) {
                log.info("添加品牌和车辆信息没有车辆信息, serviceExpireVo:{}", serviceExpireVo);
                continue;
            }
            serviceExpireVo.setSeriesCode(vehicleDoMap.get(serviceExpireVo.getCarVin()).getSeriesCode());
            serviceExpireVo.setSeriesName(getSeriesName(serviceExpireVo.getSeriesCode(), seriesNameMap,
                    vehicleDoMap.get(serviceExpireVo.getCarVin()).getSeriesName()));
            serviceExpireVo.setBrandCode(vehicleDoMap.get(serviceExpireVo.getCarVin()).getBrandCode());
            serviceExpireVo.setBrandName(vehicleDoMap.get(serviceExpireVo.getCarVin()).getBrandName());
            serviceExpireVo.setIncontrolPhone(piplDataUtil.getDecodeText(
                    vehicleDoMap.get(serviceExpireVo.getCarVin()).getIncontrolPhone()));
            resp.add(serviceExpireVo);
        }
        log.info("添加品牌和车辆信息结果, 添加成功总数量:{}", resp.size());
        if (vehicleDoMap.size() <= serviceExpireVos.size()) {
            serviceExpireStatisticVo.setNoVehicleCount(serviceExpireVos.size() - vehicleDoMap.size());
        }
        return resp;
    }

    /**
     * 根据条件添加品牌和车辆类型信息到服务过期
     *
     * @param serviceExpireVos 服务过期Vo列表，用于存储添加了品牌和车辆类型信息后的对象
     * @param conditionDTO 自动任务通知条件DTO，包含用于查询的条件信息
     * @return 返回添加了品牌和车辆类型信息后的服务过期Vo列表
     */

    public List<ServiceExpireVo> addBrandCarTypeByCondition(List<ServiceExpireVo> serviceExpireVos,
                                                            AutoTaskNotifyConditionDTO conditionDTO,
                                                            ServiceExpireStatisticVo serviceExpireStatisticVo) {
        log.info("根据条件添加品牌和车辆类型信息到服务过期, conditionDTO:{}, 待处理数据总量：{}", conditionDTO, serviceExpireVos.size());
        if (CollectionUtils.isEmpty(serviceExpireVos)) {
            return serviceExpireVos;
        }
        List<String> carVinList = serviceExpireVos.stream().map(ServiceExpireVo::getCarVin).collect(Collectors.toList());
        List<IncontrolVehicleDO> incontrolVehicleDOList = getVehicleDOListByCondition(carVinList, conditionDTO);
        log.info("根据条件添加品牌和车辆类型信息到服务过期, conditionDTO:{}, 查询的车辆数量:{}", conditionDTO, incontrolVehicleDOList.size());
        if (CollectionUtils.isEmpty(incontrolVehicleDOList)) {
            log.info("根据条件添加品牌和车辆类型信息到服务过期, 没有车辆信息, conditionDTO:{}", conditionDTO);
            serviceExpireStatisticVo.setNoVehicleCount(serviceExpireVos.size());
            return new ArrayList<>();
        }
        List<String> seriesCodeList = incontrolVehicleDOList.stream().map(IncontrolVehicleDO::getSeriesCode).distinct()
                .collect(Collectors.toList());
        Map<String, String> seriesNameMap = getSeriesNameMap(seriesCodeList);
        Map<String, IncontrolVehicleDO> vehicleDoMap = getVehicleDoMap(incontrolVehicleDOList);
        log.info("根据条件添加品牌和车辆类型信息到服务过期, 到期的数量serviceExpireVos:{}, vehicleDoMap的数量:{}",
                serviceExpireVos.size(), vehicleDoMap.size());
        if (CollectionUtils.isEmpty(vehicleDoMap)) {
            log.info("根据条件添加品牌和车辆类型信息到服务过期, 没有车辆信息, conditionDTO:{}", conditionDTO);
            serviceExpireStatisticVo.setNoVehicleCount(serviceExpireVos.size());
            return new ArrayList<>();
        }
        List<ServiceExpireVo> resp = new ArrayList<>();
        for (ServiceExpireVo serviceExpireVo : serviceExpireVos) {
            if (Objects.isNull(vehicleDoMap.get(serviceExpireVo.getCarVin()))) {
                log.info("根据条件添加品牌和车辆类型信息到服务过期, 没有车辆的信息, serviceExpireVo:{}", serviceExpireVo);
                continue;
            }
            if (StringUtils.isBlank(vehicleDoMap.get(serviceExpireVo.getCarVin()).getIncontrolPhone())) {
                log.info("根据条件添加品牌和车辆类型信息到服务过期, 加密手机号为空, serviceExpireVo:{}", serviceExpireVo);
                continue;
            }
            serviceExpireVo.setSeriesCode(vehicleDoMap.get(serviceExpireVo.getCarVin()).getSeriesCode());
            serviceExpireVo.setSeriesName(getSeriesName(serviceExpireVo.getSeriesCode(), seriesNameMap,
                    vehicleDoMap.get(serviceExpireVo.getCarVin()).getSeriesName()));
            serviceExpireVo.setBrandCode(vehicleDoMap.get(serviceExpireVo.getCarVin()).getBrandCode());
            serviceExpireVo.setBrandName(vehicleDoMap.get(serviceExpireVo.getCarVin()).getBrandName());
            serviceExpireVo.setIncontrolPhone(piplDataUtil.getDecodeText(
                    vehicleDoMap.get(serviceExpireVo.getCarVin()).getIncontrolPhone()));
            resp.add(serviceExpireVo);
        }
        log.info("根据条件添加品牌和车辆类型信息到服务过期, conditionDTO:{}, 成功添加车辆的总数量:{}", conditionDTO, resp.size());
        if (vehicleDoMap.size() <= serviceExpireVos.size()) {
            serviceExpireStatisticVo.setNoVehicleCount(serviceExpireVos.size() - vehicleDoMap.size());
        }
        return resp;
    }


    /**
     * 根据入参控ID列表查询对应的手机号码映射表。
     *
     * @param incontrolIdList 入参控ID的列表，类型为List<String>。
     * @return 返回一个Map<String, String>，其中key为入参控ID，value为对应的手机号码。
     */
    private Map<String, String> queryIncontrolIdToPhoneMap(List<String> incontrolIdList) {
        Map<String, String> map = new HashMap<>();
        if (CollectionUtils.isEmpty(incontrolIdList)) {
            return map;
        }
        LambdaQueryWrapper<IncontrolCustomerDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(IncontrolCustomerDO::getIncontrolId, incontrolIdList)
                .eq(IncontrolCustomerDO::getIsDeleted, false);
        List<IncontrolCustomerDO> customerDOS = incontrolCustomerMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(customerDOS)) {
            return map;
        }
        for (IncontrolCustomerDO customerDO : customerDOS) {
            if (StringUtils.isNotBlank(customerDO.getPhoneEncrypt())) {
                map.put(customerDO.getIncontrolId(), piplDataUtil.getDecodeText(customerDO.getPhoneEncrypt()));
            }
        }
        return map;
    }

    /**
     * 获取SeriesName
     * @param seriesCode 车型code
     * @param name 默认的名称（非真实）
     * @return String
     * */
    public String getSeriesName(String seriesCode, Map<String, String> seriesNameMap, String name) {
        if (CollectionUtils.isEmpty(seriesNameMap) || StringUtils.isBlank(seriesCode)) {
            return name;
        }
        String res = seriesNameMap.get(seriesCode);
        if (StringUtils.isBlank(res)) {
            res = name;
        }
        if (StringUtils.isBlank(res)) {
            return name;
        }
        return res;
    }

    /**
     * 获取VehicleDoMap
     * @param incontrolVehicleDOList vehicle的列表
     * @return Map<String, IncontrolVehicleDO>
     * */
    public Map<String, IncontrolVehicleDO> getVehicleDoMap(List<IncontrolVehicleDO> incontrolVehicleDOList) {
        Map<String, IncontrolVehicleDO> map = new HashMap<>();
        if (CollectionUtils.isEmpty(incontrolVehicleDOList)) {
            return map;
        }
        for (IncontrolVehicleDO incontrolVehicleDO : incontrolVehicleDOList) {
            map.put(incontrolVehicleDO.getCarVin(), incontrolVehicleDO);
        }
        return map;
    }

    /**
     * 批量获取IncontrolVehicleDO
     * @param carVinList carvin数据
     * @return List<IncontrolVehicleDO>
     * */
    public List<IncontrolVehicleDO> getVehicleDOListByCarVinList(List<String> carVinList) {
        if (CollectionUtils.isEmpty(carVinList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<IncontrolVehicleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(IncontrolVehicleDO::getCarVin, carVinList)
                .eq(IncontrolVehicleDO::getCarSystemModel, CarSystemModelEnum.PIVI.getCode())
                .eq(IncontrolVehicleDO::getIsDeleted, false);
        return incontrolVehicleDOMapper.selectList(queryWrapper);
    }

    /**
     * 根据条件获取车辆信息列表
     *
     * @param carVinList 车辆VIN码列表，用于筛选车辆
     * @param conditionDTO 查询条件对象，包含品牌和车辆来源等查询条件
     * @return 符合条件的车辆信息列表
     */
    public List<IncontrolVehicleDO> getVehicleDOListByCondition(List<String> carVinList,
                                                                AutoTaskNotifyConditionDTO conditionDTO) {
        log.info("根据条件获取车辆信息列表, conditionDTO:{}, carVinList的数量:{}, carVin:{}", conditionDTO, carVinList.size(),
                carVinList);
        if (CollectionUtils.isEmpty(carVinList)) {
            return new ArrayList<>();
        }
        List<IncontrolVehicleDO> resp = new ArrayList<>();
        try {
            LambdaQueryWrapper<IncontrolVehicleDO> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(IncontrolVehicleDO::getCarVin, carVinList)
                    .eq(IncontrolVehicleDO::getCarSystemModel, CarSystemModelEnum.PIVI.getCode())
                    .eq(IncontrolVehicleDO::getIsDeleted, false);
            if (!BrandEnum.ALL.getType().equals(conditionDTO.getBrand())){
                queryWrapper.eq(IncontrolVehicleDO::getBrandCode, conditionDTO.getBrand());
            }
            if (VehicleOriginEnum.IMPORTED.getType().equals(conditionDTO.getVehicleOrigin())) {
                queryWrapper.eq(IncontrolVehicleDO::getProductionEn, conditionDTO.getVehicleOrigin());
            } else if (!VehicleOriginEnum.ALL.getType().equals(conditionDTO.getVehicleOrigin())) {
                queryWrapper.ne(IncontrolVehicleDO::getProductionEn, VehicleOriginEnum.IMPORTED.getType());
            }
            resp = incontrolVehicleDOMapper.selectList(queryWrapper);
            if (CollUtil.isEmpty(resp)) {
                log.info("根据条件获取车辆信息列表, conditionDTO:{}, resp的数量为空", conditionDTO);
                return resp;
            }
            log.info("根据条件获取车辆信息列表, conditionDTO:{}, resp的数量:{}", conditionDTO, resp.size());
        } catch (Exception e) {
            log.info("根据条件获取车辆信息列表查询异常，conditionDTO:{}, exception:{}", conditionDTO,e.getMessage());
        }
        return resp;
    }



    public void saveOrUpdateSubscriptionService(List<SubscriptionServiceBO> each) {

        Set<String> vinSet = convertSet(each, e -> e.getCarVin());
        Map<String, String> carPhoneMap = each.parallelStream().filter(e -> e.getPhone() != null).collect(Collectors.toMap(k -> k.getCarVin(), v -> v.getPhone(), (v1, v2) -> v1));
        Map<String, String> carIcrMap = each.parallelStream().filter(e -> e.getIncontrolId() != null).collect(Collectors.toMap(k -> k.getCarVin(), v -> v.getIncontrolId(), (v1, v2) -> v1));

        // 查DP 目前一次最多查100条
        List<List<String>> splitVin = CollUtil.split(vinSet, 100);
        List<UserDPResultVO> dpList = new ArrayList<>(vinSet.size());
        for (List<String> oneTenVin : splitVin) {
            // vehicleModelMasterDataService.findDpListMock(oneTenVin);
            // vehicleModelMasterDataService.findDpList(List.of("SALEA7RU2N2084182","SALEA7RUXN2085614"));
            dpList.addAll(vehicleModelMasterDataService.findDpList(oneTenVin));
        }
        log.info("本次拆分任务处理{}条Vin数据，从DP获取到{}条数据", vinSet.size(), dpList.size());
        if (CollUtil.isEmpty(dpList)) {
            log.warn("该批次vin：{}未查询到相关DP数据", vinSet);
            return;
        }
        Set<String> dpVinSet = dpList.stream().map(e -> e.getVin()).collect(Collectors.toSet());
        HashSet<String> lostVin = new HashSet<>(vinSet);
        lostVin.removeAll(dpVinSet);
        if (CollUtil.isNotEmpty(lostVin)){
            log.info("未从DP查询到数据的VIN号：{}", lostVin);
        }
        // PIPL信息获取
        Set<String> piplData = each.parallelStream()
                .flatMap(service -> Stream.of(service.getFirstName(), service.getSurname(), service.getPhone()))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
        Map<String, String> encryptListText = piplDataUtil.getEncryptListText(piplData);
        log.info("本批次批量加密结果集大小：{}", encryptListText.size());

        // 比对生产 subscriptionService数据
        HashSet<IncontrolCustomerDO> customerSet = Sets.newHashSet();
        List<SubscriptionServiceDO> serviceDOList = new ArrayList<>();
        compareAndGenSubService(each, vinSet, serviceDOList, encryptListText, customerSet);

        // 车辆数据处理
        ArrayList<IncontrolVehicleDO> vehicleList = new ArrayList<>(dpList.size());
        compareAndGenVehicleData(vinSet, dpList, carIcrMap, encryptListText, carPhoneMap, vehicleList);

        // 数据按 vin分组单台车事务处理
        Map<String, List<SubscriptionServiceDO>> serviceDOMap = serviceDOList.stream().collect(Collectors.groupingBy(k -> k.getCarVin()));
        Map<String, IncontrolVehicleDO> vehicleDOMap = convertMap(vehicleList, k -> k.getCarVin(), v -> v, (v1, v2) -> v1);
        SubscriptionServiceApiImpl selfBean = applicationContext.getBean(getClass());
        vinSet.parallelStream().forEach(vin -> {
            try {
                selfBean.insertOrUpdateVehicleAndServiceForVin(vin, serviceDOMap.get(vin), vehicleDOMap.get(vin));
            } catch (Exception e) {
                log.error("过期服务数据处理车辆{}时出现错误", vin, e);
            }
        });
        // 保存用户信息（包含手机号）到t_incontrol_customer表
        log.info("要处理的customerSet数据有：{}条",customerSet.size());
        incontrolCustomerService.saveOrUpdateIncontrolCustomerWithTSDP(customerSet);
    }

    /**
     * 每一批次数据的处理
     * @param each
     * @return 原始数据处理成功 VS 处理失败的 键值对
     */
    public Map<Integer,Integer> processEachBatchData(List<RemoteOriginalDataParseBO> each) {
        // 获取 vin集合 、 vin和手机号map 、 vin和ICR账号map
        Set<String> vinSet = convertSet(each, RemoteOriginalDataParseBO::getCarVin);
        Map<String, String> carPhoneMap = each.parallelStream().filter(e -> e.getPhone() != null).collect(Collectors.toMap(RemoteOriginalDataParseBO::getCarVin, RemoteOriginalDataParseBO::getPhone, (v1, v2) -> v1));
        Map<String, String> carIcrMap = each.parallelStream().filter(e -> e.getIncontrolId() != null).collect(Collectors.toMap(RemoteOriginalDataParseBO::getCarVin, RemoteOriginalDataParseBO::getIncontrolId, (v1, v2) -> v1));

        // vin号和原始数据id的映射map
        Map<String, Set<Long>> vinIdMap = each.parallelStream().collect(Collectors.groupingBy(RemoteOriginalDataParseBO::getCarVin, Collectors.mapping(RemoteOriginalDataParseBO::getId, Collectors.toSet())));

        // PIPL信息获取
        Set<String> piplData = each.parallelStream()
                .flatMap(service -> Stream.of(service.getFirstName(), service.getSurname(), service.getPhone()))
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
        Map<String, String> encryptListText = piplDataUtil.getEncryptListText(piplData);
        log.info("本批次批量加密结果集大小：{}", encryptListText.size());

        // 车辆数据处理
        ArrayList<IncontrolVehicleDO> vehicleList = new ArrayList<>(each.size());
        // 返回DP失败的VIN号集合
        Set<String> failVinSet = compareAndGenVehicleDataNew(new HashSet<>(vinSet), carIcrMap, encryptListText, carPhoneMap, vehicleList, vinIdMap);

        // 比对生产 subscriptionService数据
        HashSet<IncontrolCustomerDO> customerSet = Sets.newHashSet();
        List<SubscriptionServiceDO> serviceDOList = new ArrayList<>();
        HashSet<String> serviceVinSet = new HashSet<>(vinSet);
        serviceVinSet.removeAll(failVinSet);
        compareAndGenSubServiceNew(each, serviceVinSet, serviceDOList, encryptListText, customerSet);

        // 数据按 vin分组单台车事务处理
        Map<String, List<SubscriptionServiceDO>> serviceDOMap = serviceDOList.stream().collect(Collectors.groupingBy(SubscriptionServiceDO::getCarVin));
        Map<String, IncontrolVehicleDO> vehicleDOMap = convertMap(vehicleList, IncontrolVehicleDO::getCarVin, v -> v, (v1, v2) -> v1);
        SubscriptionServiceApiImpl selfBean = applicationContext.getBean(getClass());

        // 成功 失败 vin集合
        Set<String> successVin = Collections.synchronizedSet(new HashSet<>());
        Set<String> failedVin = Collections.synchronizedSet(failVinSet);
        serviceVinSet.parallelStream().forEach(vin -> {
            try {
                selfBean.insertOrUpdateVehicleAndServiceForVinNew(vin, serviceDOMap.get(vin), vehicleDOMap.get(vin));
                successVin.add(vin);
            } catch (Exception e) {
                log.error("过期服务数据处理车辆{}时出现错误,涉及的原始数据id{}", vin,vinIdMap.get(vin), e);
                remoteOriginalDataService.updateDataFail(vinIdMap.get(vin), RemoteDataErrorType.SAVE_FAIL.getType(), StrUtil.maxLength(ExceptionUtil.getRootCauseMessage(e),250));
                failedVin.add(vin);
            }
        });
        // 保存用户信息（包含手机号）到t_incontrol_customer表
        log.info("要处理的customerSet数据有：{}条",customerSet.size());
        incontrolCustomerService.saveOrUpdateIncontrolCustomerWithTSDP(customerSet);

        // 处理成功数据状态
        Set<Long> successIds = vinIdMap.entrySet().stream().filter(e -> successVin.contains(e.getKey())).flatMap(v -> v.getValue().stream()).collect(Collectors.toSet());
        Set<Long> failedIds = vinIdMap.entrySet().stream().filter(e -> failedVin.contains(e.getKey())).flatMap(v -> v.getValue().stream()).collect(Collectors.toSet());
        remoteOriginalDataService.updateDataSuccess(successIds);

        // 返回数量结果
        return Map.of(successIds.size(),failedIds.size());
    }

    private void compareAndGenVehicleData(Set<String> vinSet, List<UserDPResultVO> dpList, Map<String, String> carIcrMap, Map<String, String> encryptListText, Map<String, String> carPhoneMap, ArrayList<IncontrolVehicleDO> vehicleList) {
        // 查旧车数据
        List<IncontrolVehicleDO> oldCarList = incontrolVehicleMapper.selectList(IncontrolVehicleDO::getCarVin, vinSet);
        Map<String, IncontrolVehicleDO> oldCarMap = oldCarList.stream().collect(Collectors.toMap(k -> k.getCarVin(), v -> v, (v1, v2) -> v1));
        for (UserDPResultVO dpResultVO : dpList) {
            String carVin = dpResultVO.getVin();
            IncontrolVehicleDO oldVehicle = oldCarMap.get(carVin);
            if (oldVehicle == null) {
                oldVehicle = new IncontrolVehicleDO();
            }
            oldVehicle.setCarVin(carVin);
            oldVehicle.setSeriesCode(dpResultVO.getSeriesCode());
            oldVehicle.setSeriesName(dpResultVO.getSeriesName());
            oldVehicle.setModelYear(dpResultVO.getModelYear());
            oldVehicle.setCarSystemModel(dpResultVO.getCarSystemModel());
            oldVehicle.setBrandCode(dpResultVO.getBrandCode());
            oldVehicle.setBrandName(dpResultVO.getBrandName());
            oldVehicle.setConfigCode(dpResultVO.getConfigCode());
            oldVehicle.setConfigName(dpResultVO.getConfigName());
            oldVehicle.setHobEn(dpResultVO.getHobEn());
            oldVehicle.setProductionEn(dpResultVO.getProductionEn());
            oldVehicle.setBindTime(LocalDateTime.now());
            oldVehicle.setIncontrolId(carIcrMap.get(carVin));
            oldVehicle.setIncontrolPhone(encryptListText.get(carPhoneMap.getOrDefault(carVin,"")));
            oldVehicle.setUpdatedTime(LocalDateTime.now());
            vehicleList.add(oldVehicle);
        }
    }


    private Set<String> compareAndGenVehicleDataNew(Set<String> vinSet, Map<String, String> carIcrMap, Map<String, String> encryptListText, Map<String, String> carPhoneMap, ArrayList<IncontrolVehicleDO> vehicleList,Map<String, Set<Long>> vinIdMap) {
        // 查旧车数据
        List<IncontrolVehicleDO> oldCarList = incontrolVehicleMapper.selectList(IncontrolVehicleDO::getCarVin, vinSet);
        Map<String, IncontrolVehicleDO> oldCarMap = oldCarList.stream().collect(Collectors.toMap(IncontrolVehicleDO::getCarVin, v -> v, (v1, v2) -> v1));
        // 已有数据的无需处理
        vinSet.removeAll(oldCarMap.keySet());
        // 查DP 目前一次最多查100条
        List<List<String>> splitVin = CollUtil.split(vinSet, 100);
        List<UserDPResultVO> dpList = new ArrayList<>(vinSet.size());
        for (List<String> oneTenVin : splitVin) {
            // vehicleModelMasterDataService.findDpListMock(oneTenVin);
            // vehicleModelMasterDataService.findDpList(List.of("SALEA7RU2N2084182","SALEA7RUXN2085614"));
            dpList.addAll(vehicleModelMasterDataService.findDpList(oneTenVin));
        }
        log.info("本次拆分任务过滤已有数据后处理{}条Vin数据，从DP获取到{}条数据", vinSet.size(), dpList.size());

        Set<String> dpVinSet = dpList.stream().map(UserDPResultVO::getVin).collect(Collectors.toSet());
        vinSet.removeAll(dpVinSet);
        Set<Long> lostIds;
        if (CollUtil.isNotEmpty(vinSet)){
            log.info("未从DP查询到数据的VIN号：{}", vinSet);
            // 这一部分标记DP_FAIL
            lostIds = vinIdMap.entrySet().stream().filter(e -> vinSet.contains(e.getKey())).flatMap(v -> v.getValue().stream()).collect(Collectors.toSet());
            remoteOriginalDataService.updateDataFail(lostIds, RemoteDataErrorType.DP_FAIL.getType(), RemoteDataErrorType.DP_FAIL.getDesc());
        }
        for (UserDPResultVO dpResultVO : dpList) {
            String carVin = dpResultVO.getVin();
            //根据SeriesCode在mappingVOMap查找，如果存在就插入值
            //如果seriesMappingVO为空、并且是PIVI车机 或者 seriesMappingVO不为空并且seriesMappingVO.getSeriesName和dpResultVO.getSeriesName()
            seriesBrandMappingDataDOService.createSeriesBrandMappingData(dpResultVO);
            IncontrolVehicleDO oldVehicle = oldCarMap.get(carVin);
            if (oldVehicle == null) {
                oldVehicle = new IncontrolVehicleDO();
                oldVehicle.setCarVin(carVin);
                oldVehicle.setSeriesCode(dpResultVO.getSeriesCode());
                oldVehicle.setSeriesName(dpResultVO.getSeriesName());
                oldVehicle.setModelYear(dpResultVO.getModelYear());
                oldVehicle.setCarSystemModel(dpResultVO.getCarSystemModel());
                oldVehicle.setBrandCode(dpResultVO.getBrandCode());
                oldVehicle.setBrandName(dpResultVO.getBrandName());
                oldVehicle.setConfigCode(dpResultVO.getConfigCode());
                oldVehicle.setConfigName(dpResultVO.getConfigName());
                oldVehicle.setHobEn(dpResultVO.getHobEn());
                oldVehicle.setProductionEn(dpResultVO.getProductionEn());
                oldVehicle.setBindTime(LocalDateTime.now());
                oldVehicle.setIncontrolId(carIcrMap.get(carVin));
                oldVehicle.setIncontrolPhone(encryptListText.get(carPhoneMap.getOrDefault(carVin,"")));
                vehicleList.add(oldVehicle);
            }
        }
        return vinSet;
    }


    private void compareAndGenSubService(List<SubscriptionServiceBO> each, Set<String> vinSet,List<SubscriptionServiceDO> saveOrUpdateList, Map<String, String> encryptListText, HashSet<IncontrolCustomerDO> customerSet) {
        // 查旧 服务数据
        List<SubscriptionServiceDO> oldServiceList = subscriptionServiceMapper.selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .in(SubscriptionServiceDO::getCarVin, vinSet)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE)
        );
        Map<String, SubscriptionServiceDO> oldServiceMap = oldServiceList.stream().collect(Collectors.toMap(k ->  k.getCarVin() + Constants.DEFAULT_CONCAT_STR + k.getServicePackage() + Constants.DEFAULT_CONCAT_STR +  k.getServiceName(), v -> v, (v1, v2) -> v1));

        for (SubscriptionServiceBO serviceBO : each) {
            SubscriptionServiceDO oldService = oldServiceMap.get(serviceBO.getCarVin() + Constants.DEFAULT_CONCAT_STR +serviceBO.getServicePackage() + Constants.DEFAULT_CONCAT_STR + serviceBO.getServiceName());
            String incontrolId = serviceBO.getIncontrolId();
            if (oldService != null){
                oldService.setExpireDateUtc0(serviceBO.getExpiryDateUTC0());
                oldService.setExpiryDate(serviceBO.getExpiryDateUTC0().plusHours(8));
                oldService.setUpdatedTime(LocalDateTime.now());
                saveOrUpdateList.add(oldService);
            }else {
                SubscriptionServiceDO insertService = BeanUtil.copyProperties(serviceBO, SubscriptionServiceDO.class);
                insertService.setSubscriptionId(ecpIdUtil.nextIdStr());
                insertService.setExpireDateUtc0(serviceBO.getExpiryDateUTC0());
                insertService.setExpiryDate(serviceBO.getExpiryDateUTC0().plusHours(8));
                saveOrUpdateList.add(insertService);
            }
            if (incontrolId != null) {
                IncontrolCustomerDO customerDO = new IncontrolCustomerDO();
                customerDO.setIncontrolId(incontrolId);
                customerDO.setUserid(serviceBO.getUserid());
                customerDO.setFirstName(encryptListText.get(serviceBO.getFirstName()));
                customerDO.setSurname(encryptListText.get(serviceBO.getSurname()));
                customerDO.setPhoneEncrypt(encryptListText.get(serviceBO.getPhone()));
                customerSet.add(customerDO);
            }
        }
    }
    private void compareAndGenSubServiceNew(List<RemoteOriginalDataParseBO> each, Set<String> vinSet,List<SubscriptionServiceDO> saveOrUpdateList, Map<String, String> encryptListText, HashSet<IncontrolCustomerDO> customerSet) {
        // 查旧 服务数据
        List<SubscriptionServiceDO> oldServiceList = subscriptionServiceMapper.selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .in(SubscriptionServiceDO::getCarVin, vinSet)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE)
        );
        Map<String, SubscriptionServiceDO> oldServiceMap = oldServiceList.stream().collect(Collectors.toMap(k ->  k.getCarVin() + Constants.DEFAULT_CONCAT_STR + k.getServicePackage() + Constants.DEFAULT_CONCAT_STR +  k.getServiceName(), v -> v, (v1, v2) -> v1));

        // Set<Long> noChangeIds = new HashSet<>();
        for (RemoteOriginalDataParseBO serviceBO : each) {
            SubscriptionServiceDO oldService = oldServiceMap.get(serviceBO.getCarVin() + Constants.DEFAULT_CONCAT_STR +serviceBO.getServicePackage() + Constants.DEFAULT_CONCAT_STR + serviceBO.getServiceName());
            String inControlId = serviceBO.getIncontrolId();
            if (oldService != null) {
                if (!serviceBO.getExpiryDateUTC0().equals(oldService.getExpireDateUtc0())) {
                    oldService.setExpireDateUtc0(serviceBO.getExpiryDateUTC0());
                    oldService.setExpiryDate(serviceBO.getExpiryDateUTC0().plusHours(8));
                    oldService.setUpdatedTime(LocalDateTime.now());
                    saveOrUpdateList.add(oldService);
                }/* else {
                    // 这部分数据也需要标记完成
                    noChangeIds.add(serviceBO.getId());
                } */
            } else {
                SubscriptionServiceDO insertService = BeanUtil.copyProperties(serviceBO, SubscriptionServiceDO.class);
                insertService.setId(null);
                insertService.setSubscriptionId(ecpIdUtil.nextIdStr());
                insertService.setExpireDateUtc0(serviceBO.getExpiryDateUTC0());
                insertService.setExpiryDate(serviceBO.getExpiryDateUTC0().plusHours(8));
                saveOrUpdateList.add(insertService);
            }
            if (inControlId != null) {
                IncontrolCustomerDO customerDO = new IncontrolCustomerDO();
                customerDO.setIncontrolId(inControlId);
                customerDO.setUserid(serviceBO.getUserid());
                customerDO.setFirstName(encryptListText.get(serviceBO.getFirstName()));
                customerDO.setSurname(encryptListText.get(serviceBO.getSurname()));
                customerDO.setPhoneEncrypt(encryptListText.get(serviceBO.getPhone()));
                customerSet.add(customerDO);
            }
        }
        // remoteOriginalDataService.updateDataSuccess(noChangeIds);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdateVehicleAndServiceForVin(String vin, List<SubscriptionServiceDO> serviceDOList, IncontrolVehicleDO vehicleDO) {

        if (CollUtil.isNotEmpty(serviceDOList) && vehicleDO != null) {
            // serviceDOList 去重   相同服务包和服务名的
            Map<String, SubscriptionServiceDO> filterServiceMap = serviceDOList.stream().collect(Collectors.toMap(k -> k.getCarVin() + Constants.DEFAULT_CONCAT_STR + k.getServicePackage() + Constants.DEFAULT_CONCAT_STR + k.getServiceName(), v -> v, (v1, v2) -> v1));
            // 新增/保存服务信息
            subscriptionServiceMapper.saveOrUpdateBatch(filterServiceMap.values());
            // 新增/保存车辆信息
            incontrolVehicleMapper.saveOrUpdateBatch(Set.of(vehicleDO));
        }else {
            log.warn("定时任务处理车辆{}时数据不完整未予保存，获取到服务数据：{}条",vin,serviceDOList.size());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertOrUpdateVehicleAndServiceForVinNew(String vin, List<SubscriptionServiceDO> serviceDOList, IncontrolVehicleDO vehicleDO) {
        // 由于有旧数据比对 所以可能需要分开判断
        if (CollUtil.isNotEmpty(serviceDOList)) {
            // serviceDOList 去重   相同服务包和服务名的
            Map<String, SubscriptionServiceDO> filterServiceMap = serviceDOList.stream().collect(Collectors.toMap(k -> k.getCarVin() + Constants.DEFAULT_CONCAT_STR + k.getServicePackage() + Constants.DEFAULT_CONCAT_STR + k.getServiceName(), v -> v, (v1, v2) -> v1));
            // 新增/保存服务信息
            subscriptionServiceMapper.saveOrUpdateBatch(filterServiceMap.values());
        }
        if (vehicleDO != null) {
            // 新增/保存车辆信息
            incontrolVehicleMapper.saveOrUpdateBatch(Set.of(vehicleDO));
        }
    }

    /**
     * 按照过期天数查询订阅服务信息
     * @return List<SubscriptionServiceDO>
     * */
    public List<SubscriptionServiceDO> querySubscriptionInfoByExpireDay(Integer expireDay) {
        // 创建LocalDateTime实例，时分秒部分设为00:00:00
        LocalDateTime startTime = LocalDate.now().plusDays(expireDay).atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1).minusNanos(1);

        List<SubscriptionServiceDO> resp = new ArrayList<>();
        try {
            resp = subscriptionServiceMapper.queryExpireService(startTime, endTime);
        } catch (Exception e) {
            log.error("按照过期天数查询订阅服务信息异常：", e);
        }
        return resp;
    }

    /**
     * Remote按照过期天数，分页查询订阅服务信息
     * @param pageParam 分页查询参数
     * @return List<SubscriptionServiceDO>
     * */
    public List<SubscriptionServiceDO> queryRemoteExpireDayByPage(ServiceExpirePageParam pageParam) {
        // 获取即将到期的数据
        // 创建LocalDateTime实例，时分秒部分设为00:00:00
        LocalDateTime startTime = LocalDate.now().plusDays(pageParam.getExpireDay()).atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1).minusNanos(1);

        //如果有指定日期，就按照指定日期进行计算到期日期
        if (Objects.nonNull(pageParam.getAppointedDate())) {
            startTime = pageParam.getAppointedDate().plusDays(pageParam.getExpireDay()).atStartOfDay();
            endTime = startTime.plusDays(1).minusNanos(1);
        }

        Page<SubscriptionServiceDO> pageReq = new Page<>(pageParam.getPageNo(), pageParam.getPageSize());
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionServiceDO::getIsDeleted, false)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE)
                .ge(SubscriptionServiceDO::getExpiryDate, startTime)
                .le(SubscriptionServiceDO::getExpiryDate, endTime)
                .groupBy(SubscriptionServiceDO::getCarVin);
        List<SubscriptionServiceDO> resp = new ArrayList<>();
        try {
            Page<SubscriptionServiceDO> page = subscriptionServiceMapper.selectPage(pageReq, queryWrapper);
            resp = page.getRecords();
        } catch (Exception e) {
            log.error("按照过期天数查询订阅服务信息异常：", e);
        }
        return resp;
    }

    /**
     * PIVI按照过期天数，分页查询订阅服务信息
     * @param pageParam 分页查询参数
     * @return List<SubscriptionServiceDO>
     * */
    public List<ServiceExpireVo> queryPIVIExpireDayByPage(ServiceExpirePageParam pageParam) {
        log.info("PIVI按照过期天数分页查询订阅服务信息, pageParam:{}", pageParam);
        // 获取即将到期的数据  创建LocalDateTime实例，时分秒部分设为00:00:00
        LocalDateTime startTime = LocalDate.now().plusDays(pageParam.getExpireDay()).atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1).minusNanos(1);
        //如果有指定日期，就按照指定日期进行计算到期日期
        if (Objects.nonNull(pageParam.getAppointedDate())) {
            startTime = pageParam.getAppointedDate().plusDays(pageParam.getExpireDay()).atStartOfDay();
            endTime = startTime.plusDays(1).minusNanos(1);
        }
        List<ServiceExpireVo> resp = subscriptionServiceMapper.queryCombinedExpireList(startTime, endTime,
                Constants.SERVICE_TYPE.PIVI, ServicePackageEnum.DATA_PLAN.getPackageName(),
                pageParam.getPageSize(), (pageParam.getPageNo() - 1) * pageParam.getPageSize());
        resp.forEach(item -> item.setServiceType(Constants.SERVICE_TYPE.PIVI));
        log.info("PIVI按照过期天数分页查询订阅服务信息, pageParam:{}, 当前页查询数量total:{}", pageParam, resp.size());
        return resp;
    }

    /**
     * 根据分页参数和时间范围，从PIVI套餐包中查询即将过期的服务信息。
     *
     * @param pageNo 当前页码。
     * @param pageSize 每页记录数。
     * @param startTime 查询的开始时间。
     * @param endTime 查询的结束时间。
     * @return 返回分页查询后的服务过期信息列表。
     */
    private List<ServiceExpireVo> queryPIVIExpireByPIVIPackage(long pageNo, long pageSize,
                                                               LocalDateTime startTime,
                                                               LocalDateTime endTime) {
        Page<PIVIPackageDO> pageReq = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<PIVIPackageDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PIVIPackageDO::getIsDeleted, false)
                .ge(PIVIPackageDO::getExpiryDate, startTime)
                .le(PIVIPackageDO::getExpiryDate, endTime)
                .orderByAsc(PIVIPackageDO::getId);
        List<PIVIPackageDO> PIVIPackageDOS = new ArrayList<>();
        try {
            Page<PIVIPackageDO> page = piviPackageDOMapper.selectPage(pageReq, queryWrapper);
            PIVIPackageDOS = page.getRecords();
        } catch (Exception e) {
            log.error("根据条件分页查询即将过期的服务信息:", e);
        }
        return buildServiceExpireVoListByPIVIPackage(PIVIPackageDOS);
    }


    /**
     * PIVI套餐信息构建服务过期信息列表。
     *
     * @param PIVIPackageDOS VIP套餐的数据对象列表，包含车辆VIN、服务名称、套餐代码和过期日期等信息。
     * @return 返回一个服务过期信息对象的列表，每个对象包含车辆VIN、过期日期、服务名称和套餐代码。
     */
    private List<ServiceExpireVo> buildServiceExpireVoListByPIVIPackage(List<PIVIPackageDO> PIVIPackageDOS) {
        if (CollUtil.isEmpty(PIVIPackageDOS)) {
            return new ArrayList<>();
        }
        List<ServiceExpireVo> serviceExpireVos = new ArrayList<>();
        for (PIVIPackageDO PIVIPackageDO : PIVIPackageDOS) {
            ServiceExpireVo serviceExpireVo = new ServiceExpireVo();
            serviceExpireVo.setUuid(UUID.randomUUID().toString().replace("-", ""));
            serviceExpireVo.setCarVin(PIVIPackageDO.getVin());
            serviceExpireVo.setExpiryDate(PIVIPackageDO.getExpiryDate());
            serviceExpireVo.setServiceName(PIVIPackageDO.getServiceName());
            serviceExpireVo.setServicePackage(PIVIPackageDO.getPackageCode());
            serviceExpireVo.setIccid(PIVIPackageDO.getIccid());
            serviceExpireVos.add(serviceExpireVo);
        }
        return serviceExpireVos;
    }



    /**
     * 根据条件分页查询即将过期的服务信息。(pivi--ONLINE_PACK)
     *
     * @param pageNo 页码。
     * @param pageSize 每页大小。
     * @param startTime 查询范围的开始时间。
     * @param endTime 查询范围的结束时间。
     * @return 返回分页查询后的服务过期信息列表。
     */
    private Map<String, ServiceExpireVo> queryPIVIExpireMapByServiceDO(long pageNo, long pageSize,
                                                                       LocalDateTime startTime,
                                                                       LocalDateTime endTime) {
        Page<SubscriptionServiceDO> pageReq = new Page<>(pageNo, pageSize);
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionServiceDO::getIsDeleted, false)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                .ge(SubscriptionServiceDO::getExpiryDate, startTime)
                .le(SubscriptionServiceDO::getExpiryDate, endTime)
                .eq(SubscriptionServiceDO::getServicePackage, ServicePackageEnum.ONLINE_PACK.getPackageName())
                .groupBy(SubscriptionServiceDO::getCarVin)
                .orderByAsc(SubscriptionServiceDO::getId);
        List<SubscriptionServiceDO> serviceDOList = new ArrayList<>();
        try {
            Page<SubscriptionServiceDO> page = subscriptionServiceMapper.selectPage(pageReq, queryWrapper);
            serviceDOList = page.getRecords();
        } catch (Exception e) {
            log.error("根据条件分页查询即将过期的服务信息:", e);
        }
        List<ServiceExpireVo> serviceExpireVos = buildServiceExpireVoByDO(serviceDOList);
        if (CollUtil.isEmpty(serviceExpireVos)) {
            return new HashMap<>();
        }
        return serviceExpireVos.stream()
                .collect(Collectors.toMap(ServiceExpireVo::getCarVin, Function.identity(), (v1,v2) -> v1));
    }



    /**
     *  通过seriesCode构建map
     * @param seriesCodeList  seriesCode的列表集合
     * @return Map<String, String>
     * */
    public Map<String, String> getSeriesNameMap(List<String> seriesCodeList) {
        List<SeriesBrandMappingDataDO> brandMappingDataDOS = getSeriesBrandMappingDataDOByCode(seriesCodeList);
        log.info("通过seriesCode构建map, brandMappingDataDOS:{}", brandMappingDataDOS);
        if (CollectionUtils.isEmpty(brandMappingDataDOS)) {
            return new HashMap<>();
        }
        Map<String, String> map = new HashMap<>();
        for (SeriesBrandMappingDataDO brandMappingDataDO : brandMappingDataDOS) {
            if (StringUtils.isBlank(brandMappingDataDO.getSeriesCode())) {
                continue;
            }
            map.put(brandMappingDataDO.getSeriesCode(), brandMappingDataDO.getSeriesName());
        }
        return map;
    }

    /**
     * 按照seriesCode查询SeriesBrandMappingDataDO
     * @param seriesCodeList  seriesCode列表
     * @return List<SeriesBrandMappingDataDO>
     * */
    public List<SeriesBrandMappingDataDO> getSeriesBrandMappingDataDOByCode(List<String> seriesCodeList) {
        log.info("按照seriesCode查询SeriesBrandMappingDataDO, seriesCodeList:{}", seriesCodeList);
        if (CollectionUtils.isEmpty(seriesCodeList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SeriesBrandMappingDataDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SeriesBrandMappingDataDO::getSeriesCode, seriesCodeList)
                .eq(SeriesBrandMappingDataDO::getIsDeleted, false);
        return seriesBrandMappingDataDOMapper.selectList(wrapper);
    }


    /**
     * 定时任务主动查询履约状态
     * 查询TSDP有没有更新到最新过期时间，如果是的话就将状态变更，并且操作order服务同步订单状态
     * 1.优先看退订 如果退订是1状态，时间相等，说明退订成功，如果包含了订阅状态没改，也要把订阅状态改为订阅成功
     * 2.再看订阅状态，如果服务包过期时间相等就说明订阅成功，
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public CommonResult<Integer> checkFulfilmentStatus(ProcessFulfilmentVO processFulfilmentVO) {

        return CommonResult.success(processFulfilmentStatus(
                processFulfilmentVO.getFulfilmentServiceStatusVO(),
                processFulfilmentVO.getVinAndService(),
                processFulfilmentVO.getExistPackageCode()
        ));
    }

    @Override
    public CommonResult<List<FulfilmentServiceStatusVO>> findActiveService(Integer total) {
        if(total == null){
            total = LIMIT_NUM;
        }
        return CommonResult.success(vcsOrderFufilmentDOMapper.findActiveService(total));
    }

    @Override
    public CommonResult<List<String>> getExistPackageCode(List<String> packageCodes) {
        return CommonResult.success(remotePackageDOService.getExistPackageCode(packageCodes));
    }


    /**
     * 查询车辆VIN码到电话号码的映射。
     *
     * @param vinList VIN码列表，用于查询对应的电话号码。
     * @return 返回一个包含车辆VIN码和对应电话号码的映射结果。如果查询列表为空或查询结果为空，则返回空映射。
     */
    @Override
    public CommonResult<Map<String, String>> queryCarVinToPhoneMap(List<String> vinList) {
        log.info("查询车辆VIN码到电话号码的映射, vinList的数量:{}", vinList.size());
        Map<String, String> carVinToPhoneMap = new HashMap<>();
        if (CollectionUtils.isEmpty(vinList)) {
            return CommonResult.success(carVinToPhoneMap);
        }
        carVinToPhoneMap = getPhoneEncrypts(vinList);
        log.info("查询车辆VIN码到电话号码的映射, carVinToPhoneMap的数量:{}",carVinToPhoneMap.size());
        if (CollectionUtils.isEmpty(carVinToPhoneMap)) {
            log.info("查询车辆VIN码到电话号码的映射, 查询的vehicleDOList为空");
            return CommonResult.success(carVinToPhoneMap);
        }
        return CommonResult.success(carVinToPhoneMap);
    }

    /**
     * 目前只查询  remote 和 onlinePack服务
     * @param carVin
     * @return
     */
    @Override
    public CommonResult<List<ServiceExpireInfoVO>> getExpireDateByVin(String carVin) {
        log.info("getExpireDateByVin service handing ~~~~~");
        List<SubscriptionServiceDO> list = subscriptionServiceMapper.selectList(
                new LambdaQueryWrapperX<SubscriptionServiceDO>()
                        .eq(SubscriptionServiceDO::getCarVin, carVin)
                        .in(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE, Constants.SERVICE_TYPE.PIVI)
                        .eq(SubscriptionServiceDO::getIsDeleted, false)
        );
        LocalDateTime appdExpireDate;
        LocalDateTime amapExpireDate;
        // 查 t_pivi_package表
        PIVIPackageDO piviPackageDO = piviPackageDOMapper.selectOne(PIVIPackageDO::getVin, carVin);
        log.info("piviPackageDO : {}",JSON.toJSONString(piviPackageDO));
        appdExpireDate = piviPackageDO == null ? null : piviPackageDO.getExpiryDate();
        amapExpireDate = piviPackageDO == null ? null : piviPackageDO.getAmaPExpireDate();

        if (CollectionUtils.isEmpty(list) && piviPackageDO == null) {
            return success(null);
        }
        log.info("list size: {}",list.size());
        ArrayList<ServiceExpireInfoVO> result = Lists.newArrayListWithExpectedSize(2);
        // remote服务
        SubscriptionServiceDO remoteService = list.stream().filter(e -> Constants.SERVICE_TYPE.REMOTE.equals(e.getServiceType())).min(Comparator.comparing(SubscriptionServiceDO::getExpiryDate)).orElse(new SubscriptionServiceDO());
        result.add(ServiceExpireInfoVO.builder().serviceName("InControl远程车控服务").expireDate(DateUtil.format(remoteService.getExpiryDate(),NORM_DATE_PATTERN)).build());


        // online在线服务
        Map<String, SubscriptionServiceDO> onlinePackServiceMap = list.stream().filter(e -> Constants.SERVICE_TYPE.PIVI.equals(e.getServiceType())).collect(Collectors.toMap(e -> e.getServicePackage(), e -> e, (e1, e2) -> e1));

        // 子服务
        ArrayList<ServiceExpireInfoVO> onlinePackChildren = Lists.newArrayListWithExpectedSize(3);

        // appd 服务
        ServiceExpireInfoVO appdExpireInfoVo = ServiceExpireInfoVO.builder().serviceName(ServicePackageEnum.ONLINE_PACK.getDescCN())
                .expireDate(DateUtil.format(onlinePackServiceMap.getOrDefault(ServicePackageEnum.ONLINE_PACK.getPackageName(), new SubscriptionServiceDO().setExpiryDate(appdExpireDate)).getExpiryDate(), NORM_DATE_PATTERN)).build();
        onlinePackChildren.add(appdExpireInfoVo);

        // amap服务   2024年7月25日会议后表示取 appd 服务一样的值
        ServiceExpireInfoVO amapExpireInfoVo = ServiceExpireInfoVO.builder().serviceName(ServicePackageEnum.CONNECTED_NAVIGATION.getDescCN()).expireDate(appdExpireInfoVo.getExpireDate()).build();
        onlinePackChildren.add(amapExpireInfoVo);

        // unicom服务
        SubscriptionServiceDO dataServiceDO = onlinePackServiceMap.get(ServicePackageEnum.DATA_PLAN.getPackageName());
        ServiceExpireInfoVO unicomExpireInfoVo = ServiceExpireInfoVO.builder().serviceName(ServicePackageEnum.DATA_PLAN.getDescCN()).expireDate(DateUtil.format(qryUnicomExpireDate(carVin), NORM_DATE_PATTERN)).build();
        onlinePackChildren.add(unicomExpireInfoVo);

        ServiceExpireInfoVO onlinePackVO = ServiceExpireInfoVO.builder().serviceName("InControl在线服务").expireDate(appdExpireInfoVo.getExpireDate()).build();

        onlinePackVO.setChild(onlinePackChildren);
        result.add(onlinePackVO);

        return success(result);
    }

    private LocalDateTime qryUnicomExpireDate(String carVin) {
        try {
            UnicomRespVO unicomRespVO = piviUniconService.getSimCarInfoByCarVin(carVin);
            List<ProductBookInfo> productBookInfoList = unicomRespVO.getUnicomRespData().getProductBookInfoList();
            ProductBookInfo latestProductBookInfo = productBookInfoList.stream().filter(e -> UnicomBookStatusEnum.ACTIVE.getType().equals(e.getBookStatus()))
                    .max(Comparator.comparingLong(e -> Long.parseLong(e.getExpireTime()))).get();
            LocalDateTime expireTime = DateUtil.parseLocalDateTime(latestProductBookInfo.getExpireTime(), "yyyyMMddHHmmSS");
            return expireTime;
        } catch (Exception e) {
            log.error("查询VIN:{}联通真实过期服务异常", carVin, e);
        }
        return null;
    }

    @Override
    public CommonResult<Map<String, String>> getAmapRealExpireDate(List<String> carVins) {
        if (CollectionUtils.isEmpty(carVins)) {
            return success(null);
        }
        ArrayList<CompletableFuture<Map<String, AmaPSearchCenterVO>>> futureList = Lists.newArrayListWithExpectedSize(carVins.size());
        for (String carVin : carVins) {
            CompletableFuture<Map<String, AmaPSearchCenterVO>> future = CompletableFuture.supplyAsync(() -> Collections.singletonMap(carVin, piviAmaPService.queryAmaPInfo(carVin)));
            futureList.add(future);
        }
        // 获取总结果
        Map<String, String> resultMap = Collections.synchronizedMap(new HashMap<>(futureList.size()));
        for (CompletableFuture<Map<String, AmaPSearchCenterVO>> future : futureList) {
            try {
                Map<String, AmaPSearchCenterVO> result = future.get();
                resultMap.put(String.join(",", result.keySet()), result.values().stream().findFirst().get().getAmaPExpireDate());
            } catch (Exception e) {
                log.error("异步执行获取高德过期服务出错", e);
            }
        }
        return success(resultMap);
    }

    @Override
    public CommonResult<Map<String, LocalDateTime>> getUnicomExpireDate(List<String> carVins) {
        if (CollectionUtils.isEmpty(carVins)) {
            return success(null);
        }
        ArrayList<CompletableFuture<Map<String, LocalDateTime>>> futureList = Lists.newArrayListWithExpectedSize(carVins.size());
        for (String carVin : carVins) {
            CompletableFuture<Map<String, LocalDateTime>> future = CompletableFuture.supplyAsync(() -> {
                try {

                    UnicomRespVO unicomRespVO = piviUniconService.getSimCarInfoByCarVin(carVin);
                    List<ProductBookInfo> productBookInfoList = unicomRespVO.getUnicomRespData().getProductBookInfoList();
                    ProductBookInfo latestProductBookInfo = productBookInfoList.stream().filter(e -> UnicomBookStatusEnum.ACTIVE.getType().equals(e.getBookStatus()))
                            .max(Comparator.comparingLong(e -> Long.parseLong(e.getExpireTime()))).get();
                    return Map.of(carVin, DateUtil.parseLocalDateTime(latestProductBookInfo.getExpireTime(),"yyyyMMddHHmmSS"));
                } catch (Exception e) {
                    log.error("查询VIN:{}联通真实过期服务异常", carVin, e);
                    return Map.of();
                }
            });
            futureList.add(future);
        }
        // 获取总结果
        Map<String, LocalDateTime> resultMap = Maps.newConcurrentMap();
        for (CompletableFuture<Map<String, LocalDateTime>> future : futureList) {
            try {
                Map<String, LocalDateTime> result = future.get();
                resultMap.putAll(result);
            } catch (Exception e) {
                log.error("异步执行获取联通过期时间出错", e);
            }
        }
        return success(resultMap);
    }

    @Override
    public CommonResult<List<ProductInfoDTO>> getLatestExpireDateByProducts(ProductQueryDTO productQueryDTO) {
        List<ProductInfoDTO> productList = productQueryDTO.getProductList();
        log.info("productList参数：{}",JSON.toJSONString(productList));
        // 查询用户下有哪些绑定的vin
        List<IncontrolVehicleDTO> vehicleDTOS = incontrolVehicleRepository.getIncontrolVehicleByConsumerCode(productQueryDTO.getJlrId(), productQueryDTO.getBrandCode());
        vehicleDTOS = vehicleDTOS.stream().filter(e-> CarSystemModelEnum.PIVI.getCode().equals(e.getCarSystemModel())).collect(Collectors.toList());
        if(CollUtil.isEmpty(vehicleDTOS)){
            return success(productList);
        }
        // SQL已经把serviceType=3的转成serviceType=2，适配fulfilmentType的类型
        Set<String> vinSet = vehicleDTOS.stream().map(IncontrolVehicleDTO::getCarVin).collect(Collectors.toSet());
        List<SubscriptionServiceDO> expireDateList = subscriptionServiceMapper.getLatestExpireDate(vinSet);
        // 提取 service_type = 2 的 VIN 集合
        Set<String> serviceTypeTwoVinSet = expireDateList.stream()
                .filter(item -> item.getServiceType() == 2)
                .map(SubscriptionServiceDO::getCarVin)
                .collect(Collectors.toSet());

        // 找出 vinSet 中未出现在 serviceTypeTwoVinSet 中的 VIN
        Set<String> missingVinSet = vinSet.stream()
                .filter(vin -> !serviceTypeTwoVinSet.contains(vin))
                .collect(Collectors.toSet());
        //如果missingVinSet不为空 则查询联通过期时间
        if(!CollUtil.isEmpty(missingVinSet)){
            List<SubscriptionServiceDO> expireUnicomDateList = subscriptionServiceMapper.getLatestUnicomExpireDate(missingVinSet);

            expireDateList.addAll(expireUnicomDateList);
        }

        log.info("查询最早过期时间 expireDateList={}, vin={}", expireDateList, vinSet);
        if(CollUtil.isEmpty(expireDateList)){
            return success(productList);
        }
        Map<Integer, LocalDateTime> serviceTypeMap = expireDateList.stream()
                .collect(Collectors.toMap(SubscriptionServiceDO::getServiceType, SubscriptionServiceDO::getExpiryDate, (e1, e2) -> e1));
        for (ProductInfoDTO dto : productList) {
            Set<Integer> childFulfilmentType = dto.getChildFulfilmentType();
            Optional<Map.Entry<Integer, LocalDateTime>> optional = serviceTypeMap.entrySet().stream().filter(e -> childFulfilmentType.contains(e.getKey())).min(Map.Entry.comparingByValue());



            optional.ifPresent(entry -> dto.setExpireDate(entry.getValue()));
        }
        return success(productList);
    }

    /**
     *  获取unicom全量的过期数据
     *  @param unicomExpireDateDTO 联通过期数据的dto
     * */
    @Override
    public void getAllUnicomExpireDate(UnicomExpireDateDTO unicomExpireDateDTO) {
        log.info("获取unicom全量的过期数据, unicomExpireDateDTO:{}", unicomExpireDateDTO);
        new Thread(()-> unicomExpireDateService.getUnicomExpireDate(unicomExpireDateDTO.getPageNo(),
                unicomExpireDateDTO.getTotalPage(),unicomExpireDateDTO.getPageSize())).start();
    }

    /**
     * 更新AMAP到期时间
     *
     * @param amaPExpireDateDTO 包含AMAP到期时间信息的数据传输对象
     */
    @Override
    public void updateAmaPExpireDate(AmaPExpireDateRequestDTO amaPExpireDateDTO) {
        log.info("更新AMAP到期时间, amaPExpireDateDTO:{}", amaPExpireDateDTO);
        new Thread(()-> amaPUpdateExpireDateService.updateAmaPExpireDate(amaPExpireDateDTO)).start();
    }

    /**
     * 根据条件获取过期统计信息
     *
     * @param pageParam 自动任务通知条件DTO，包含用于查询的条件信息
     * @return 返回一个CommonResult对象，其中包含ServiceExpireStatisticVo类型的对象
     */
    @Override
    public CommonResult<ServiceExpireStatisticVo> expireInfoPageByCondition(AutoTaskNotifyConditionDTO pageParam) {
        log.info("根据条件获取过期统计信息, pageParam:{}", pageParam);
        if (ExpirationIntervalEnum.AFTER_EXPIRED.getType().equals(pageParam.getExpirationInterval())) {
            pageParam.setExpireDays(-1 * pageParam.getExpireDays());
        }
        ServiceExpireStatisticVo resp = null;
        if (ExpirationServiceEnum.REMOTE.getType().equals(pageParam.getExpirationService())) {
            resp = getRemoteExpireInfoByCondition(pageParam);
        } else if (ExpirationServiceEnum.PIVI.getType().equals(pageParam.getExpirationService())) {
            resp = getPIVIExpireInfoByCondition(pageParam);
        }
        return CommonResult.success(resp);
    }

    /**
     * 根据条件获取远程过期信息
     *
     * @param pageParam 查询条件参数，包含用于查询的页码和其它筛选条件
     * @return 包含服务过期统计信息的通用结果对象
     */
    public ServiceExpireStatisticVo getRemoteExpireInfoByCondition(AutoTaskNotifyConditionDTO pageParam) {
        ServiceExpireStatisticVo remoteExpireStatisticVo = new ServiceExpireStatisticVo();
        List<SubscriptionServiceDO> serviceDOS = queryRemotePageByCondition(pageParam);
        log.info("根据条件获取远程过期信息, pageParam:{}, 查询到期车辆的数量:{}", pageParam, serviceDOS.size());
        if (CollectionUtils.isEmpty(serviceDOS)) {
            remoteExpireStatisticVo.setEndFlag(true);
            return remoteExpireStatisticVo;
        }
        List<ServiceExpireVo> serviceExpireVos = buildServiceExpireVoByDO(serviceDOS);
        remoteExpireStatisticVo.setTotal(serviceExpireVos.size());
        List<ServiceExpireVo> addBrandExpireVoList = addBrandCarTypeByCondition(serviceExpireVos, pageParam, remoteExpireStatisticVo);
        List<Boolean> idempotentCheckResp = batchIdempotentCheck(addBrandExpireVoList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        int idempotentFilterCount = 0;
        for (int i = 0; i < Math.min(idempotentCheckResp.size(), addBrandExpireVoList.size()); i++) {
            if (!idempotentCheckResp.get(i)) {
                String key = Constants.SERVICE_EXPIRE_KEY + addBrandExpireVoList.get(i).getCarVin() + ":" +
                        addBrandExpireVoList.get(i).getServiceType();
                log.info("根据条件获取远程过期信息幂等校验, serviceExpireVo:{}, 到期时间毫秒:{}",
                        addBrandExpireVoList.get(i), getExpireTimeMills(key));
                idempotentFilterCount++;
                continue;
            }
            resp.add(addBrandExpireVoList.get(i));
        }
        remoteExpireStatisticVo.setIdempotentFilterCount(idempotentFilterCount);
        remoteExpireStatisticVo.setValidDataList(resp);
        remoteExpireStatisticVo.setEndFlag(false);
        log.info("根据条件获取远程过期信息, pageParam:{}, idempotentFilterCount:{}, resp的数量:{}", pageParam,
                idempotentFilterCount, resp.size());
        return remoteExpireStatisticVo;
    }


    /**
     * 根据条件获取PIVI到期信息
     *
     * @param pageParam 查询条件，包含分页和筛选信息
     * @return 包含PIVI到期统计信息的通用结果对象
     */
    public ServiceExpireStatisticVo getPIVIExpireInfoByCondition(AutoTaskNotifyConditionDTO pageParam) {
        ServiceExpireStatisticVo piviExpireStatisticVo = new ServiceExpireStatisticVo();
        List<ServiceExpireVo> serviceExpireVos = queryPIVIExpirePageByCondition(pageParam);
        log.info("根据条件获取PIVI到期信息, pageParam:{}, 查询到期车数量:{}", pageParam, serviceExpireVos.size());
        piviExpireStatisticVo.setTotal(serviceExpireVos.size());
        if (CollectionUtils.isEmpty(serviceExpireVos)) {
            piviExpireStatisticVo.setEndFlag(true);
            return piviExpireStatisticVo;
        }
        //添加车辆、品牌信息
        List<ServiceExpireVo> carBrandExpireList = addBrandCarTypeByCondition(serviceExpireVos, pageParam, piviExpireStatisticVo);

        List<ServiceExpireVo> validDataList = filterInvalidByCondition(carBrandExpireList, piviExpireStatisticVo, pageParam);
        List<Boolean> idempotentCheckResp = batchIdempotentCheck(validDataList);
        List<ServiceExpireVo> resp = new ArrayList<>();
        int idempotentFilterCount = 0;
        for (int i = 0; i < Math.min(idempotentCheckResp.size(), validDataList.size()); i++) {
            if (!idempotentCheckResp.get(i)) {
                String key = Constants.SERVICE_EXPIRE_KEY + validDataList.get(i).getCarVin() + ":" +
                        validDataList.get(i).getServiceType();
                log.info("根据条件获取PIVI到期信息, 幂等校验为重复提交, serviceExpireVo:{}, 到期时间毫秒:{}",
                        validDataList.get(i), getExpireTimeMills(key));
                idempotentFilterCount++;
                continue;
            }
            resp.add(validDataList.get(i));
        }
        piviExpireStatisticVo.setIdempotentFilterCount(idempotentFilterCount);
        piviExpireStatisticVo.setValidDataList(resp);
        piviExpireStatisticVo.setEndFlag(false);
        log.info("根据条件获取PIVI到期信息, pageParam:{}, idempotentFilterCount:{}, resp的数量:{}", pageParam,
                idempotentFilterCount, resp.size());
        return piviExpireStatisticVo;
    }

    /**
     * 根据条件分页查询远程订阅服务信息
     *
     * @param pageParam 页面参数对象，包含查询所需的条件如到期日、页码、页面大小等
     * @return 返回一个订阅服务数据对象列表，符合查询条件的记录
     */
    public List<SubscriptionServiceDO> queryRemotePageByCondition(AutoTaskNotifyConditionDTO pageParam) {
        log.info("根据条件分页查询远程订阅服务信息, pageParam:{}", pageParam);
        // 获取即将到期的数据  创建LocalDateTime实例，时分秒部分设为00:00:00
        LocalDateTime startTime = LocalDate.now().plusDays(pageParam.getExpireDays()).atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1).minusNanos(1);

        //如果有指定日期，就按照指定日期进行计算到期日期
//        if (Objects.nonNull(pageParam.getAppointedDate())) {
//            startTime = pageParam.getAppointedDate().plusDays(pageParam.getExpireDay()).atStartOfDay();
//            endTime = startTime.plusDays(1).minusNanos(1);
//        }

        Page<SubscriptionServiceDO> pageReq = new Page<>(pageParam.getPageNo(), pageParam.getPageSize());
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SubscriptionServiceDO::getIsDeleted, false)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE)
                .ge(SubscriptionServiceDO::getExpiryDate, startTime)
                .le(SubscriptionServiceDO::getExpiryDate, endTime)
                .groupBy(SubscriptionServiceDO::getCarVin);
        List<SubscriptionServiceDO> resp = new ArrayList<>();
        try {
            Page<SubscriptionServiceDO> page = subscriptionServiceMapper.selectPage(pageReq, queryWrapper);
            resp = page.getRecords();
            log.info("根据条件分页查询远程订阅服务信息, 查询的数量:{}", resp.size());
        } catch (Exception e) {
            log.error("根据条件分页查询远程订阅服务信息：", e);
        }
        return resp;
    }

    /**
     * 根据条件分页查询PIVI即将到期信息
     *
     * @param pageParam 查询条件参数对象，包含到期天数、过期间隔类型、页码和页面大小等信息
     * @return List<ServiceExpireVo>
     */
    private List<ServiceExpireVo> queryPIVIExpirePageByCondition(AutoTaskNotifyConditionDTO pageParam) {
        log.info("根据条件分页查询PIVI即将到期信息, pageParam:{}", pageParam);
        // 获取即将到期的数据  创建LocalDateTime实例，时分秒部分设为00:00:00
        LocalDateTime startTime = LocalDate.now().plusDays(pageParam.getExpireDays()).atStartOfDay();
        LocalDateTime endTime = startTime.plusDays(1).minusNanos(1);
        //如果有指定日期，就按照指定日期进行计算到期日期
//        if (Objects.nonNull(pageParam.getAppointedDate())) {
//            startTime = pageParam.getAppointedDate().plusDays(pageParam.getExpireDay()).atStartOfDay();
//            endTime = startTime.plusDays(1).minusNanos(1);
//        }
        List<ServiceExpireVo> resp = subscriptionServiceMapper.queryCombinedExpireList(startTime, endTime,
                Constants.SERVICE_TYPE.PIVI, ServicePackageEnum.DATA_PLAN.getPackageName(),
                pageParam.getPageSize(), (pageParam.getPageNo() - 1) * pageParam.getPageSize());
        resp.forEach(item -> item.setServiceType(Constants.SERVICE_TYPE.PIVI));
        log.info("根据条件分页查询PIVI即将到期信息, pageParam:{}, 查询的resp数量:{}", pageParam, resp.size());
        return resp;
    }


    /**
     * 根据车辆识别码列表获取对应的手机加密信息。
     *
     * @param carVinList 车辆识别码列表，用于查询手机加密信息。
     * @return 返回一个映射表，其中键为车辆识别码，值为对应的手机加密信息。如果查询列表为空或结果中无对应信息，则返回空映射表。
     */
    public Map<String, String> getPhoneEncrypts(List<String> carVinList) {
        log.info("根据车辆识别码列表获取对应的手机加密信息,carVinList的数量：{}", carVinList.size());
        Map<String, String> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(carVinList)) {
            return resultMap;
        }
        List<IncontrolVehicleDO> incontrolVehicleDOList = selectIncontrolByCarVinList(carVinList);
        log.info("根据车辆识别码列表获取对应的手机加密信息,inControlVehicleDOList的数量：{}", incontrolVehicleDOList.size());
        if (CollUtil.isEmpty(incontrolVehicleDOList)) {
            return resultMap;
        }
        List<String> phoneDecodeList = incontrolVehicleDOList.stream()
                .map(IncontrolVehicleDO::getIncontrolPhone)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        Map<String, String> phoneMap = piplDataUtil.getDecodeListText(phoneDecodeList);
        log.info("根据车辆识别码列表获取对应的手机加密信息, phoneDecodeList数量:{}, phoneMap的数量：{}",
                phoneDecodeList.size(), phoneMap.size());
        for (IncontrolVehicleDO vehicleDO : incontrolVehicleDOList) {
            resultMap.put(vehicleDO.getCarVin(), phoneMap.get(vehicleDO.getIncontrolPhone()));
        }
        return resultMap;
    }

    /**
     * 根据车辆VIN码列表查询不受控制的车辆信息
     *
     * @param carVinList 车辆VIN码列表，用于查询车辆信息
     * @return 返回一个包含不受控制车辆信息的列表如果输入列表为空或null，则返回一个空列表
     */
    private List<IncontrolVehicleDO> selectIncontrolByCarVinList(List<String> carVinList) {
        if(CollUtil.isEmpty(carVinList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<IncontrolVehicleDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(IncontrolVehicleDO::getCarVin, carVinList)
                .eq(IncontrolVehicleDO::getIsDeleted, false);
        return incontrolVehicleMapper.selectList(queryWrapper);
    }


    public Integer processFulfilmentStatus(FulfilmentServiceStatusVO fulfilmentServiceStatusVO,
                                           List<VinsAndServiceDTO> vinAndService,
                                           List<String> existPackageCode) {

        int num = 0;
        //查找对应vin的TSDP数据
        VinsAndServiceDTO serviceDTO = vinAndService.stream()
                .filter(v -> v.getVin().equals(fulfilmentServiceStatusVO.getCarVin()))
                .findFirst().orElse(null);
        if (serviceDTO == null) {
            log.info("定时任务checkFulfilmentStatus 查找TSDP数据不存在 对应carvin:{}", fulfilmentServiceStatusVO.getCarVin());
            return 0;
        }
        //拿着TSDP数据去查询过滤对应的PIVI服务包
        List<ServicePackageDTO> servicePackage = serviceDTO.getServicePackage();
        if (CollUtil.isNotEmpty(servicePackage)) {
            log.info("拿着TSDP数据去查询过滤对应的PIVI服务包 servicePackage :{}",JSON.toJSONString(servicePackage));
            //获取PIVI ServicePackage
            List<ServicePackageDTO> newServicePackages = servicePackage.stream()
                    .filter(sp -> existPackageCode.contains(sp.getServicePackageName()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(newServicePackages)) {
                //取第一条PIVI服务包数据
                ServicePackageDTO servicePackageDTO = newServicePackages.get(0);
                log.info("取第一条PIVI服务包数据 servicePackageDTO:{}",JSON.toJSONString(servicePackageDTO));
                //先判断是否有退单，优先对比退单数据
                Integer vorServiceStatus = fulfilmentServiceStatusVO.getVorServiceStatus();
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime expireDate = servicePackageDTO.getExpireDate();
                if (CancelServiceStatusEnum.UNACTIVATED.getStatus().equals(vorServiceStatus)) {
                    num = rollbackHandle(fulfilmentServiceStatusVO,now, expireDate,newServicePackages);
                } else {
                    log.info("进入正向履约比较");
                    num = activeHandle(fulfilmentServiceStatusVO,now,expireDate,newServicePackages);

                }
            }
        }
        return num;
    }

    private int activeHandle(FulfilmentServiceStatusVO fulfilmentServiceStatusVO,
                             LocalDateTime now,
                             LocalDateTime expireDate,List<ServicePackageDTO> newServicePackages) {
        int num = 0;
        //查询履约DO
        VcsOrderFufilmentDO vcsOrderFufilmentDO = vcsOrderFufilmentDOMapper
                .getOneByFulfilmentId(fulfilmentServiceStatusVO.getFufilmentId());
        LocalDateTime createdTime = vcsOrderFufilmentDO.getCreatedTime();
        LocalDateTime vofServiceEndDate = fulfilmentServiceStatusVO.getVofServiceEndDate();
        String vofServiceEndDateStr = TimeFormatUtil.timeToStringByFormat(vofServiceEndDate, TimeFormatUtil.formatter_3);

        //服务列表
        long days = Duration.between(createdTime, now).toDays();
        if(Math.abs(days)>serviceFailDay){
            vcsOrderFufilmentDO.setServiceStatus(FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getStatus());
            num = vcsOrderFufilmentDOMapper.updateById(vcsOrderFufilmentDO);
            log.info("正向履约激活失败  超过30天");
            return num;
        }
        String expireDateString = TimeFormatUtil.timeToStringByFormat(expireDate, TimeFormatUtil.formatter_3);

        //对比正向履约时间
        if (expireDateString.equals(vofServiceEndDateStr)) {
            log.info("进入正向履约比较时间对比一致");
            //履约成功
            //1.修改正向履约状态
            vcsOrderFufilmentDO.setServiceStatus(FufilmentServiceStatusEnum.ACTIVATED.getStatus());
            num = vcsOrderFufilmentDOMapper.updateById(vcsOrderFufilmentDO);
            //2.修改subscriptionService服务包时间
            updateSubscriptionServicePackageTime(fulfilmentServiceStatusVO, expireDate, newServicePackages);
            //3.TODO 修改订单状态 考虑失败之后的异常重试
            vcsOrderFufilmentDOService.executeOrderStatusSync(fulfilmentServiceStatusVO.getOrderCode(),fulfilmentServiceStatusVO.getVcsOrderCode());
//            orderAppApi.tsdpCallBack(fulfilmentServiceStatusVO.getVcsOrderCode());
        }
        return num;
    }

    private int rollbackHandle(FulfilmentServiceStatusVO fulfilmentServiceStatusVO,
                               LocalDateTime now,LocalDateTime expireDate,List<ServicePackageDTO> newServicePackages) {
        int num = 0;
        VcsOrderRollbackDO vcsOrderRollbackDO = vcsOrderRollbackDOMapper.getOneByRollBackId(fulfilmentServiceStatusVO.getRollbackFufilmentId());
        LocalDateTime createdTime = vcsOrderRollbackDO.getCreatedTime();
        LocalDateTime vorServiceEndDate = fulfilmentServiceStatusVO.getVorServiceEndDate();
        String vorServiceEndDateStr = TimeFormatUtil.timeToStringByFormat(vorServiceEndDate, TimeFormatUtil.formatter_3);
        long days = Duration.between(createdTime, now).toDays();
        if(Math.abs(days)>serviceFailDay){
            vcsOrderRollbackDO.setServiceStatus(CancelServiceStatusEnum.ACTIVATE_OFF_FAILURE.getStatus());
            num = vcsOrderRollbackDOMapper.updateById(vcsOrderRollbackDO);
            log.info("关闭履约失败 超过{}天", serviceFailDay);
            return num;
        }
        String expireDateString = TimeFormatUtil.timeToStringByFormat(expireDate, TimeFormatUtil.formatter_3);
        log.info("进入退单履约比较");
        if (expireDateString.equals(vorServiceEndDateStr)) {
            log.info("进入退单履约比较对比一致");
            // 修改退单履约状态
            vcsOrderRollbackDO.setServiceStatus(CancelServiceStatusEnum.ACTIVATE_OFF.getStatus());
            num = vcsOrderRollbackDOMapper.updateById(vcsOrderRollbackDO);
            // 判断正向履约状态 如果为4 ACTIVATE_FAILURE 也需要对应修改为 3 ACTIVATE_OFF 激活关闭
//            if (!FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getStatus().equals(fulfilmentServiceStatusVO.getVofServiceStatus())) {
//                VcsOrderFufilmentDO vcsOrderFufilmentDO = vcsOrderFufilmentDOMapper
//                        .getOneByFulfilmentId(fulfilmentServiceStatusVO.getFufilmentId());
//                vcsOrderFufilmentDO.setServiceStatus(FufilmentServiceStatusEnum.ACTIVATE_OFF.getStatus());
//                vcsOrderFufilmentDOMapper.updateById(vcsOrderFufilmentDO);
//            }
            // 修改subscriptionService服务包时间
            updateSubscriptionServicePackageTime(fulfilmentServiceStatusVO, expireDate, newServicePackages);
            //还需要 考虑 TODO 考虑失败之后的异常重试
            vcsOrderFufilmentDOService.executeRefundOrderStatusSync(fulfilmentServiceStatusVO.getOrderCode()
                    ,fulfilmentServiceStatusVO.getRefundOrderCode()
                    ,fulfilmentServiceStatusVO.getFufilmentId());
//            orderAppApi.tsdpRefundCallBack(fulfilmentServiceStatusVO.getRefundOrderCode());
        }
        return num;
    }


    private void updateSubscriptionServicePackageTime(FulfilmentServiceStatusVO fulfilmentServiceStatusVO, LocalDateTime expireDate, List<ServicePackageDTO> newServicePackages) {
        SubscriptionServiceDO subscriptionServiceDO = new SubscriptionServiceDO();
        subscriptionServiceDO.setServiceType(1);
        subscriptionServiceDO.setExpiryDate(expireDate);
        Set<String> updatePackageCodes = newServicePackages.stream().map(ServicePackageDTO::getServicePackageName).collect(Collectors.toSet());
        int update = subscriptionServiceMapper.update(subscriptionServiceDO, new LambdaUpdateWrapper<SubscriptionServiceDO>()
                .eq(SubscriptionServiceDO::getCarVin, fulfilmentServiceStatusVO.getCarVin())
                .in(SubscriptionServiceDO::getServicePackage, updatePackageCodes)
                .eq(BaseDO::getIsDeleted,false));
    }

}
