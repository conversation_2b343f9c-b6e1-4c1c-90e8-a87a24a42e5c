package com.jlr.ecp.subscription.dal.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.VehicleBindMessageDO;
import com.jlr.ecp.subscription.dal.mysql.vehicle.VehicleBindMessageMapper;
import com.jlr.ecp.subscription.dal.repository.VehicleBindMessageRepository;
import com.jlr.ecp.subscription.enums.message.MessageStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * VehicleBindMessageDO Repository接口
 *
 */
@Component
@Slf4j
public class VehicleBindMessageRepositoryImpl extends ServiceImpl<VehicleBindMessageMapper, VehicleBindMessageDO> implements VehicleBindMessageRepository {

    // 补偿最大重试次数
    private static final Integer MAX_RETRY_TIMES = 3;

    @Override
    public List<VehicleBindMessageDO> getPreDealIdList(Integer status) {
        LambdaQueryWrapperX<VehicleBindMessageDO> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.lt(VehicleBindMessageDO::getRetryTimes, MAX_RETRY_TIMES);
        // 默认处理消费失败的数据
        if(Objects.isNull(status)){
            wrapperX.eq(VehicleBindMessageDO::getMessageStatus, MessageStatusEnum.CONSUMED_ERROR.getType());
        }else {
            wrapperX.eq(VehicleBindMessageDO::getMessageStatus, status);
        }
        return list(wrapperX);
    }
}
