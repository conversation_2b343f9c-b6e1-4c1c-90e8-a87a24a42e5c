package com.jlr.ecp.subscription.dal.mysql.consumer;

import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.mapper.BaseMapperX;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;
import com.jlr.ecp.subscription.enums.consumer.ConsumerBindEnum;
import com.jlr.ecp.subscription.enums.consumer.ConsumerVehicleSourceEnum;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 消费者车辆 Mapper
 */
@Mapper
public interface ConsumerVehicleMapper extends BaseMapperX<ConsumerVehicleDO> {


    default List<ConsumerVehicleDO> selectListByJlrId(String jlrId){
        return selectList(new LambdaQueryWrapperX<ConsumerVehicleDO>()
                .eq(ConsumerVehicleDO::getConsumerCode,jlrId)
                .eq(ConsumerVehicleDO::getBindStatus, ConsumerBindEnum.BIND.getBindStatus())
                .eq(ConsumerVehicleDO::getSource, ConsumerVehicleSourceEnum.MINI_HOME.getCode())
                .eq(BaseDO::getIsDeleted,false));
    }
}
