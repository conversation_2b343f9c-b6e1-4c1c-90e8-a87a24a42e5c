package com.jlr.ecp.subscription.dal.repository.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;
import com.jlr.ecp.subscription.dal.mysql.consumer.ConsumerVehicleMapper;
import com.jlr.ecp.subscription.dal.repository.ConsumerVehicleRepository;
import com.jlr.ecp.subscription.enums.consumer.ConsumerBindEnum;
import com.jlr.ecp.subscription.enums.consumer.ConsumerVehicleSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * ConsumerVehicle Repository接口
 *
 */
@Component
@Slf4j
public class ConsumerVehicleRepositoryImpl extends ServiceImpl<ConsumerVehicleMapper, ConsumerVehicleDO> implements ConsumerVehicleRepository {

    @Override
    public ConsumerVehicleDO getOneByCarVinAndJlrId(String carVin, String jlrId, ConsumerVehicleSourceEnum sourceEnum) {
        return getOne(new LambdaQueryWrapperX<ConsumerVehicleDO>()
                .eq(ConsumerVehicleDO::getCarVin, carVin)
                .eq(ConsumerVehicleDO::getConsumerCode, jlrId)
                .eq(ConsumerVehicleDO::getSource, sourceEnum.getCode())
                .eq(ConsumerVehicleDO::getIsDeleted, false)
                .orderByDesc(ConsumerVehicleDO::getId)
                .last(Constants.LIMIT_ONE));
    }

    @Override
    public List<ConsumerVehicleDO> getListByJlrId(String jlrId) {
        return list(new LambdaQueryWrapperX<ConsumerVehicleDO>()
                .eq(ConsumerVehicleDO::getConsumerCode, jlrId)
                .eq(ConsumerVehicleDO::getBindStatus, ConsumerBindEnum.BIND.getBindStatus())
                .eq(ConsumerVehicleDO::getIsDeleted, false));
    }


    @Override
    public List<ConsumerVehicleDO> getListByJlrIds(List<String > jlrIds) {
        return list(new LambdaQueryWrapperX<ConsumerVehicleDO>()
                .in(ConsumerVehicleDO::getConsumerCode, jlrIds)
                .eq(ConsumerVehicleDO::getBindStatus, ConsumerBindEnum.BIND.getBindStatus())
                .eq(ConsumerVehicleDO::getIsDeleted, false));
    }

    @Override
    public List<ConsumerVehicleDO> getListByVinJlrIdList(List<String> carVinList, List<String> jlrIdList, Integer source) {
        log.info("getListByVinJlrIdList, carVinList数量:{}, jlrIdList数量:{}, source:{}",
                CollUtil.size(carVinList), CollUtil.size(jlrIdList), source);
        if (CollUtil.isEmpty(carVinList) || CollUtil.isEmpty(jlrIdList)) {
            return new ArrayList<>();
        }
        return list((new LambdaQueryWrapperX<ConsumerVehicleDO>()
                .in(ConsumerVehicleDO::getCarVin, carVinList)
                .eq(ConsumerVehicleDO::getSource, source)
                .eq(ConsumerVehicleDO::getIsDeleted, false)));
    }
}
