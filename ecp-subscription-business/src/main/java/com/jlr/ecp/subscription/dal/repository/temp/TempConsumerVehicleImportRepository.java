package com.jlr.ecp.subscription.dal.repository.temp;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jlr.ecp.subscription.api.temp.dto.ImportProcessResultDTO;
import com.jlr.ecp.subscription.dal.dataobject.temp.TempConsumerVehicleImportDO;

import java.util.List;

public interface TempConsumerVehicleImportRepository extends IService<TempConsumerVehicleImportDO> {

    /**
     * 根据状态获取vin绑定临时表的ID列表
     *
     * @param status 状态值，如果为null则返回空列表, VinBindImportTempStatusEnum
     * @return 符合条件的记录ID列表
     */
    List<TempConsumerVehicleImportDO> getImportListByStatus(Integer status);

    /**
     * 根据ID列表查询临时消费者车辆导入数据
     *
     * @param idList ID列表
     * @return 临时消费者车辆导入数据列表，如果查询结果为空则返回空列表
     */
    List<TempConsumerVehicleImportDO> selectByIdList(List<Long> idList);

    /**
     * 根据导入处理结果DTO列表更新状态
     *
     * @param importDTOList 导入处理结果DTO列表，包含需要更新状态的数据
     * @return boolean 更新成功返回true，失败返回false
     */
    boolean updateStatusByIdList(List<ImportProcessResultDTO> importDTOList);

    /**
     * 根据导入状态查询车辆信息
     *
     * @param importStatus 导入状态标识
     * @return 返回符合指定导入状态的临时消费者车辆导入数据列表
     */
    List<TempConsumerVehicleImportDO> queryInVehicleByStatus(Integer importStatus);

    /**
     * 根据导入状态查询未在车辆表中存在的消费者车辆导入记录
     *
     * @param importStatus 导入状态标识，用于筛选特定状态的导入记录
     * @return 返回未在车辆表中存在的消费者车辆导入记录列表
     */
    List<TempConsumerVehicleImportDO> queryNotInVehicleByStatus(Integer importStatus);
}
