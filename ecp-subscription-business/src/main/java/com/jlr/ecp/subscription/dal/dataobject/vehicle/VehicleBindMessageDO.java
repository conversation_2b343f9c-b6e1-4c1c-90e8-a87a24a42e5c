package com.jlr.ecp.subscription.dal.dataobject.vehicle;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 车辆绑定消息实体类
 * 对应表：t_vehicle_bind_message
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_vehicle_bind_message")
public class VehicleBindMessageDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 车架号
     */
    @TableField(value = "car_vin")
    private String carVin;

    /**
     * 车辆绑定/解绑唯一流水号
     */
    @TableField(value = "bind_no")
    private String bindNo;

    /**
     * 品牌
     */
    @TableField(value = "brand")
    private String brand;

    /**
     * 用户jlrid
     */
    @TableField(value = "jlr_id")
    private String jlrId;

    /**
     * 车型
     * 小程序车型主数据中的车型code，如果业务没有在小程序后台维护dms的nameplate，则此字段为空
     */
    @TableField(value = "model_code")
    private String modelCode;

    /**
     * DMS 车型nameplate
     */
    @TableField(value = "dms_nameplate")
    private String dmsNameplate;

    /**
     * 配置编码
     * 车款5000号
     */
    @TableField(value = "config_code")
    private String configCode;

    /**
     * 首任车主
     * 是否首任车主，0-否，1-是
     */
    @TableField(value = "first_owner")
    private String firstOwner;

    /**
     * 首任车主jlr id
     */
    @TableField(value = "first_owner_jlr_id")
    private String firstOwnerJlrId;

    /**
     * 首任车主绑定时间
     * 格式：2024-09-01 00:00:00
     */
    @TableField(value = "first_owner_bind_time")
    private String firstOwnerBindTime;

    /**
     * 发票日期
     * 开票日期，来源于DMS VIN验证接口，如果operateType为update, 则来源于SS推送的开票日期
     */
    @TableField(value = "invoice_date")
    private String invoiceDate;

    /**
     * 车主关系类型
     * 和车主关系类型 SELF:车主本人, FAMILY: 家人, FRIEND: 朋友, EMPLOYEE:员工
     */
    @TableField(value = "relation_ship")
    private String relationShip;

    /**
     * 操作类型
     * 操作类型，有以下值：bind（绑定） unbind（解绑） update（更新开票日期）
     * 如果类型为update, 则只推送bindNo，invoiceDate，operateType，operateTime四个字段
     */
    @TableField(value = "operate_type")
    private String operateType;

    /**
     * 操作时间
     * 格式：2024-09-01 00:00:00
     */
    @TableField(value = "operate_time")
    private String operateTime;

    /**
     * 重试次数
     */
    @TableField(value = "retry_times")
    private Integer retryTimes;

    /**
     * 消息消费状态
     */
    @TableField(value = "message_status")
    private Integer messageStatus;

    /**
     * 租户号
     */
    @TableField(value = "tenant_id")
    private Long tenantId;
}
