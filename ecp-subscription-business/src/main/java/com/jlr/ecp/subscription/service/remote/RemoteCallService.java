package com.jlr.ecp.subscription.service.remote;

import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteModifyRespDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteQueryByVinRespDTO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;
import com.jlr.ecp.subscription.kafka.message.BaseMessage;

import java.util.List;

public interface RemoteCallService {
    /**
     * 并发调用TSDP做remote续费
     *
     */
    RemoteModifyRespDTO concurrentCallTSDPRenew(OrdFufilmentBusiDTO param);

    /**
     * kafka消费者调用TSDP做remote续费
     *
     */
    RemoteModifyRespDTO listenerCallTSDPRenew(List<SubscriptionServiceDO> services, String fulfilmentId,
                                              BaseMessage baseMessage, String incontrolId);

    /**
     * 根据VIN去TSDP查询服务信息
     *
     */
    RemoteQueryByVinRespDTO getByVin(String vin);

    /**
     * 根据ICR去TSDP查询服务信息
     *
     */
    List<VinsAndServiceDTO> getByICR(String inControlId);

    /**
     * 将指定的控制ID保存到延迟队列中以进行重试
     *
     */
    void save2DelayQue(String inControlIdAndJlrId);

    /**
     * 清除延迟队列中的特定任务
     *
     */
    void clearDelayQue(String inControlId);
}
