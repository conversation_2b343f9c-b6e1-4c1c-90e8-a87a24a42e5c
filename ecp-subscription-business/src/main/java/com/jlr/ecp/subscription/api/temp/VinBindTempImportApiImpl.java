package com.jlr.ecp.subscription.api.temp;

import cn.hutool.core.collection.CollUtil;
import cn.smallbun.screw.core.util.StringUtils;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.api.temp.dto.ImportProcessResultDTO;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.temp.TempConsumerVehicleImportDO;
import com.jlr.ecp.subscription.dal.repository.ConsumerVehicleRepository;
import com.jlr.ecp.subscription.dal.repository.temp.TempConsumerVehicleImportRepository;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.consumer.ConsumerBindEnum;
import com.jlr.ecp.subscription.enums.consumer.ConsumerVehicleSourceEnum;
import com.jlr.ecp.subscription.enums.temp.VinBindImportTempStatusEnum;
import com.jlr.ecp.subscription.exception.TooManyRequestException;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Validated
@Slf4j
public class VinBindTempImportApiImpl implements VinBindTempImportApi {

    @Resource
    private TempConsumerVehicleImportRepository consumerVehicleImportRepository;

    @Resource
    private VinInitializeService vinInitializeService;

    @Resource
    private ConsumerVehicleRepository consumerVehicleRepository;

    @Resource
    private Redisson redisson;

    @Resource
    private TransactionTemplate transactionTemplate;

    private static final String SPLIT_SYMBOL = ",";

    private static final String VIN_BIND_PROCESS_LIMITER = "global_vin_process_limiter";


    /**
     * 根据状态获取有车辆数据的导入记录ID列表
     *
     * @param status 状态值，用于筛选符合条件的车辆导入记录
     * @return 返回包含车辆导入记录ID的列表结果对象
     */
    @Override
    public CommonResult<List<Long>> getIdListByStatusInVehicle(Integer status) {
        log.info("根据状态获取有车辆数据的导入记录ID列表, status:{}", status);
        List<TempConsumerVehicleImportDO> importDOList = consumerVehicleImportRepository.queryInVehicleByStatus(status);
        if (CollUtil.isEmpty(importDOList)) {
            log.info("根据状态获取有车辆数据的导入记录ID列表，查询结果为空，status:{}", status);
            return CommonResult.success(new ArrayList<>());
        }
        log.info("根据状态获取有车辆数据的导入记录ID列表, importDOList数量:{}", CollUtil.size(importDOList));
        List<Long> idList = importDOList.stream().map(TempConsumerVehicleImportDO::getId).collect(Collectors.toList());
        return CommonResult.success(idList);
    }

    /**
     * 根据状态获取非车辆记录的ID列表
     *
     * @param status 状态值，用于筛选记录
     * @return 返回指定状态的非车辆记录ID列表的通用结果对象
     */
    @Override
    public CommonResult<List<Long>> getIdListByStatusNonVehicle(Integer status) {
        log.info("根据状态获取非车辆记录的ID列表, status:{}", status);
        List<TempConsumerVehicleImportDO> importDOList = consumerVehicleImportRepository.queryNotInVehicleByStatus(status);
        log.info("根据状态获取非车辆记录的ID列表, importDOList数量:{}", CollUtil.size(importDOList));
        if (CollUtil.isEmpty(importDOList)) {
            log.info("根据状态获取非车辆记录的ID列表，查询结果为空，status:{}", status);
            return CommonResult.success(new ArrayList<>());
        }
        log.info("根据状态获取非车辆记录的ID列表, importDOList数量:{}", CollUtil.size(importDOList));
        List<Long> idList = importDOList.stream().map(TempConsumerVehicleImportDO::getId).collect(Collectors.toList());
        return CommonResult.success(idList);
    }


    /**
     * 处理车辆VIN数据导入流程
     *
     * @param idList 需要处理的临时导入数据ID列表
     * @return 包含每个VIN处理结果的通用结果对象，其中数据为处理结果列表
     */
    @Override
    public CommonResult<List<ImportProcessResultDTO>> processInVehicleByIdList(List<Long> idList) {
        log.info("处理vin在车辆表中的数据, idList的size: {}", CollUtil.size(idList));
        List<TempConsumerVehicleImportDO> importDOList = consumerVehicleImportRepository.selectByIdList(idList);
        if (CollUtil.isEmpty(importDOList)) {
            return CommonResult.error(ErrorCodeConstants.VIN_NOT_IN_IMPORT);
        }
        List<ImportProcessResultDTO> resp = new ArrayList<>();
        //1.过滤customer-vehicle已经存在的数据
        List<TempConsumerVehicleImportDO> filterImportDOList = filterVinInCustomerVehicle(importDOList, resp);
        log.info("处理vin在车辆表中的数据, importDOList数量: {}, filterImportDOList数量:{}, 过滤人车表已存在数量:{}",
                CollUtil.size(importDOList), CollUtil.size(filterImportDOList), CollUtil.size(resp));
        //2.单个处理service, 需要知道每个vin的服务是否处理成功
        for (TempConsumerVehicleImportDO importDO : filterImportDOList) {
            ImportProcessResultDTO importProcessResultDTO = new ImportProcessResultDTO();
            importProcessResultDTO.setId(importDO.getId());
            Boolean result = vinInitializeService.saveOrUpdateOnlineServiceByVin(importDO.getVin());
            if (Boolean.TRUE.equals(result)) {
                importProcessResultDTO.setStatus(VinBindImportTempStatusEnum.SUCCESS.getStatus());
            } else {
                importProcessResultDTO.setStatus(VinBindImportTempStatusEnum.FAIL.getStatus());
                importProcessResultDTO.setFailReason("saveOrUpdateOnlineServiceByVin失败");
            }
            resp.add(importProcessResultDTO);
        }
        transactionTemplate.executeWithoutResult(status -> {
            //3.批量保存人车关系表
            batchSaveCustomerVehicle(filterImportDOList);
            //4.批量更新临时表状态
            consumerVehicleImportRepository.updateStatusByIdList(resp);
        });
        return CommonResult.success(resp);
    }

    /**
     * 处理不在车辆数表中数据导入
     * @param idList 需要处理的数据ID列表
     * @return 包含导入处理结果的通用结果对象，数据为ImportProcessResultDTO列表
     */
    @Override
    public CommonResult<List<ImportProcessResultDTO>> processNonVehicleByIdList(List<Long> idList) {
        log.info("处理不在车辆数表中数据导入, idList的size: {}", idList.size());
        List<TempConsumerVehicleImportDO> importDOList = consumerVehicleImportRepository.selectByIdList(idList);
        if (CollUtil.isEmpty(importDOList)) {
            return CommonResult.error(ErrorCodeConstants.VIN_NOT_IN_IMPORT);
        }
        List<ImportProcessResultDTO> resp = new ArrayList<>();

        //1、过滤掉已经在人车辆表中存在的VIN导入数据
        List<TempConsumerVehicleImportDO> filterImportDOList = filterVinInCustomerVehicle(importDOList, resp);
        log.info("处理不在车辆数表中数据导入, 查询importDOList数量: {}, filterImportDOList数量:{}, 已在人车表中的数量：{}",
                CollUtil.size(importDOList), CollUtil.size(filterImportDOList), CollUtil.size(resp));

        globalLimiterProcessNonVehicle(filterImportDOList, resp);

        transactionTemplate.executeWithoutResult(status -> {
            //3.批量保存人车信息
            batchSaveNonVinCustomerVehicle(filterImportDOList, resp);
            //4、批量更新临时表状态
            consumerVehicleImportRepository.updateStatusByIdList(resp);
        });
        return CommonResult.success(resp);
    }

    /**
     * 处理非车辆数据的全局限流流程
     *
     * @param filterImportDOList 需要处理的车辆导入数据列表
     * @param resp 处理结果列表，用于存储每个VIN的处理结果
     */
    public void globalLimiterProcessNonVehicle(List<TempConsumerVehicleImportDO> filterImportDOList,
                                              List<ImportProcessResultDTO> resp) {
        log.info("处理非车辆数据的全局限流流程, filterImportDOList数量:{}", CollUtil.size(filterImportDOList));
        for (TempConsumerVehicleImportDO importDO : filterImportDOList) {
            // 添加全局限流，确保每个VIN处理之间有间隔
            RRateLimiter globalLimiter = redisson.getRateLimiter(VIN_BIND_PROCESS_LIMITER);
            if (!globalLimiter.isExists()) {
                globalLimiter.trySetRate(RateType.OVERALL, 1, 2500, RateIntervalUnit.MILLISECONDS);
            }

            // 等待获取令牌
            globalLimiter.acquire(1);

            ImportProcessResultDTO processResultDTO = processNonVehicleByVin(importDO.getVin());
            processResultDTO.setId(importDO.getId());
            resp.add(processResultDTO);
        }
        log.info("处理非车辆数据的全局限流流程完成， resp数量：{}", CollUtil.size(filterImportDOList));
    }

    /**
     * 根据VIN码处理不在车辆表中的数据
     *
     * @param vin 车辆识别号码
     * @return 处理结果，成功返回true，失败返回false
     */
    public ImportProcessResultDTO processNonVehicleByVin(String vin) {
        log.info("根据VIN码处理不在车辆表中的数据, vin:{}", vin);
        ImportProcessResultDTO processResultDTO = new ImportProcessResultDTO();
        try {
            vinInitializeService.vinInitializeByQueryVin(vin);
            log.info("根据VIN码处理不在车辆表中的数据, 处理成功, vin:{}", vin);
            processResultDTO.setStatus(VinBindImportTempStatusEnum.SUCCESS.getStatus());
        } catch (ServiceException e) {
            log.info("处理不在车辆数表中数据导入, ServiceException:{}, vin:{}", e.getMessage(), vin);
            processResultDTO.setStatus(VinBindImportTempStatusEnum.FAIL.getStatus());
            processResultDTO.setFailReason("TSDP未找到该VIN");
        } catch (TooManyRequestException e) {
            log.info("tsdpByVin请求过于频繁, vin:{}, exception:{}", vin, e.toString());
            processResultDTO.setStatus(VinBindImportTempStatusEnum.API_LIMIT.getStatus());
            // 使用更详细的错误信息
            String errorMessage = e.getMessage();
            if (StringUtils.isBlank(errorMessage)) {
                errorMessage = "TSDPByVin请求过于频繁";
            }
            processResultDTO.setFailReason(errorMessage);
        } catch (Exception e) {
            log.info("处理不在车辆数表中数据导入, vin:{}, 未知异常:,", vin, e);
            processResultDTO.setStatus(VinBindImportTempStatusEnum.FAIL.getStatus());
            processResultDTO.setFailReason("未知异常");
        }
        return processResultDTO;
    }

    /**
     * 批量保存人车辆信息
     *
     * @param importDOList 待导入的客户车辆临时数据列表
     */
    public void batchSaveCustomerVehicle(List<TempConsumerVehicleImportDO> importDOList) {
        if (CollUtil.isEmpty(importDOList)) {
            log.info("批量保存人车辆信息, importDOList为空");
            return ;
        }
        List<ConsumerVehicleDO> consumerVehicleDOList = new ArrayList<>();
        for (TempConsumerVehicleImportDO importDO : importDOList) {
            consumerVehicleDOList.add(processCustomerVehicle(importDO));
        }
        log.info("批量保存人车辆信息, importDOList数量：{}，consumerVehicleDOList数量:{}",
                CollUtil.size(importDOList), CollUtil.size(consumerVehicleDOList));
        consumerVehicleRepository.saveBatch(consumerVehicleDOList);
    }

    /**
     * 批量保存VIN不在车辆表中的数据
     *
     * @param importDOList 需要导入的临时客户车辆数据列表
     * @param importResultDTOList 导入处理结果列表，用于记录每条数据的处理状态
     */
    public void batchSaveNonVinCustomerVehicle(List<TempConsumerVehicleImportDO> importDOList,
                                               List<ImportProcessResultDTO> importResultDTOList) {
        log.info("批量保存VIN不在车辆表中的数据, importDOList数量:{}, importResultDTOList数量:{}",
                CollUtil.size(importDOList), CollUtil.size(importResultDTOList));
        if (CollUtil.isEmpty(importDOList)) {
            return ;
        }
        Set<Long> apiLimitedIdSet = getApiLimitedIdSet(importResultDTOList);
        List<TempConsumerVehicleImportDO> saveDOList = new ArrayList<>();
        for (TempConsumerVehicleImportDO importDO : importDOList) {
            if (apiLimitedIdSet.contains(importDO.getId())) {
                continue;
            }
            saveDOList.add(importDO);
        }
        log.info("批量保存VIN不在车辆表中的数据, importDOList数量:{}, importResultDTOList数量:{}, apiLimitedIdSet数量:{}, saveDOList数量:{}",
                CollUtil.size(importDOList), CollUtil.size(importResultDTOList), CollUtil.size(apiLimitedIdSet), CollUtil.size(saveDOList));
        batchSaveCustomerVehicle(saveDOList);
    }

    /**
     * 获取API限制状态的记录ID集合
     *
     * @param importResultDTOList 导入处理结果DTO列表
     * @return API限制状态的记录ID集合
     */
    public Set<Long> getApiLimitedIdSet(List<ImportProcessResultDTO> importResultDTOList) {
        log.info("获取API限制状态的记录ID集合, importResultDTOList数量：{}", CollUtil.size(importResultDTOList));
        Set<Long> set = new HashSet<>();
        if (CollUtil.isEmpty(importResultDTOList)) {
            return set;
        }
        for (ImportProcessResultDTO importResultDTO : importResultDTOList) {
            if (VinBindImportTempStatusEnum.API_LIMIT.getStatus().equals(importResultDTO.getStatus())) {
                set.add(importResultDTO.getId());
            }
        }
        log.info("获取API限制状态的记录ID集合, importResultDTOList数量:{}, set数量:{}",
                CollUtil.size(importResultDTOList), CollUtil.size(set));
        return set;
    }



    /**
     * 过滤掉已经在人车辆表中存在的VIN导入数据
     *
     * @param importDOList 待过滤的临时消费者车辆导入数据列表
     * @param importResultDTOList 导入处理结果DTO列表
     * @return 过滤后不存于客户车辆表中的导入数据列表
     */
    public List<TempConsumerVehicleImportDO> filterVinInCustomerVehicle(List<TempConsumerVehicleImportDO> importDOList,
                                                                        List<ImportProcessResultDTO> importResultDTOList) {
        if (CollUtil.isEmpty(importDOList)) {
            log.info("过滤掉已经在人车辆表中存在的VIN导入数据, importDOList为空");
            return importDOList;
        }
        List<String> carVinList = importDOList.stream().map(TempConsumerVehicleImportDO::getVin).collect(Collectors.toList());
        List<String> jlrIdList = importDOList.stream().map(TempConsumerVehicleImportDO::getJlrId).collect(Collectors.toList());
        List<ConsumerVehicleDO> consumerVehicleDOList = consumerVehicleRepository.getListByVinJlrIdList(carVinList, jlrIdList, ConsumerVehicleSourceEnum.MINI_HOME.getCode());
        if (CollUtil.isEmpty(consumerVehicleDOList)) {
            log.info("过滤掉已经在人车辆表中存在的VIN导入数据, customerVehicleDOList为空, importDOList数量:{}", CollUtil.size(importDOList));
            return importDOList;
        }
        log.info("过滤掉已经在人车辆表中存在的VIN导入数据, customerVehicleDOList数量:{}", CollUtil.size(consumerVehicleDOList));
        Set<String> vinConsumerCodeSet = new HashSet<>();
        for (ConsumerVehicleDO consumerVehicleDO : consumerVehicleDOList) {
            vinConsumerCodeSet.add(consumerVehicleDO.getCarVin() + SPLIT_SYMBOL + consumerVehicleDO.getConsumerCode());
        }
        List<TempConsumerVehicleImportDO> resp = new ArrayList<>();
        for (TempConsumerVehicleImportDO importDO : importDOList) {
            String vinJlrId = importDO.getVin() + SPLIT_SYMBOL + importDO.getJlrId();
            if (vinConsumerCodeSet.contains(vinJlrId)) {
                log.info("过滤掉已经在人车辆表中存在的VIN导入数据, CustomerVehicle存在, vin:{}", importDO.getVin());
                importResultDTOList.add(
                        ImportProcessResultDTO.builder()
                                .id(importDO.getId())
                                .status(VinBindImportTempStatusEnum.FAIL.getStatus())
                                .failReason("vin和jlrId在customer_vehicle中已存在")
                                .build()
                );
                continue;
            }
            resp.add(importDO);
        }
        log.info("过滤掉已经在人车辆表中存在的VIN导入数据, 原始importDOList数量：{}, 过滤后数量resp:{}, importResultDTOList数量：{}",
                CollUtil.size(importDOList), CollUtil.size(resp), CollUtil.size(importResultDTOList));
        return resp;
    }

    /**
     * 处理客户车辆导入数据，转换为标准的客户车辆对象
     *
     * @param importDO 客户车辆导入数据对象，包含车辆VIN码、JLR ID、绑定序列号、绑定时间等信息
     * @return 转换后的标准客户车辆对象
     */
    public ConsumerVehicleDO processCustomerVehicle(TempConsumerVehicleImportDO importDO) {
        ConsumerVehicleDO consumerVehicleDO = new ConsumerVehicleDO();
        consumerVehicleDO.setCarVin(importDO.getVin());
        consumerVehicleDO.setConsumerCode(importDO.getJlrId());
        consumerVehicleDO.setBindNo(importDO.getBindSerialNo());
        consumerVehicleDO.setSource(ConsumerVehicleSourceEnum.MINI_HOME.getCode());
        consumerVehicleDO.setBindTime(importDO.getBindTime());
        consumerVehicleDO.setBindStatus(ConsumerBindEnum.BIND.getBindStatus());
        return consumerVehicleDO;
    }

}
