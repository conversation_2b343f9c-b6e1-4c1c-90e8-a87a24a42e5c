package com.jlr.ecp.subscription.service.subscription;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.remotepackage.vo.ServiceExpireInfoVO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchLocalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.dto.Vehicle5000InfoDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteSearchResultDTO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.EsimInfoVO;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.ProductBookInfo;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.UnicomRespVO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO;
import com.jlr.ecp.subscription.dal.dataobject.remote.RemoteRenewDetailRecords;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.VehicleDmsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.appd.AppDRenewStatusEnum;
import com.jlr.ecp.subscription.enums.appd.RenewServiceTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomBookStatusEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomRealnameFlagEnum;
import com.jlr.ecp.subscription.exception.TooManyRequestException;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionData;
import com.jlr.ecp.subscription.model.dto.AppDSubscriptionResp;
import com.jlr.ecp.subscription.service.pivi.PIVIAppDService;
import com.jlr.ecp.subscription.service.pivi.impl.PIVIUnicomServiceImpl;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.vehicle.IncontrolVehicleService;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import com.jlr.ecp.subscription.util.PIPLDataUtil;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;

@Service
@Slf4j
public class SubscriptionServiceImpl implements SubscriptionService {

    @Resource
    SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Resource
    IncontrolVehicleDOMapper incontrolVehicleDOMapper;

    @Resource
    private PIVIUnicomServiceImpl piviUniconService;

    @Resource
    private PIVIAppDService appDService;

    @Resource
    private Environment environment;

    @Resource
    private VehicleDmsDOMapper vehicleDmsDOMapper;

    @Resource
    private PIPLDataUtil piplDataUtil;

    @Resource
    private AppDCuRenewRecordsMapper appDCuRenewRecordsMapper;

    @Resource
    private RemoteCallService remoteCallService;

    @Resource
    private IncontrolVehicleService incontrolVehicleService;

    private static final String PROFILE = "spring.profiles.active";

    private static final String VEHICLE_INVOICE_DATE = "车辆发票日期";
    private static final String ONLINE_SERVICE = "InControl在线服务";
    private static final String REMOTE_SERVICE = "InControl远程车控服务";

    @Resource
    private VinInitializeService vinInitializeService;

    /**
     * 根据vin号查询到期服务日期查询
     * amapTime
     * 1. 默认取 ecp系统中 t_subscription_service ecpAppdTime
     * 2. 若无，取ecpUnicomTime
     * 3. 仍无，取pivi表的时间
     *
     * @param carVin
     * @return
     */
    public SubscriptionSearchResultDTO getExpireDateByVin(String carVin) {
        log.info("getExpireDateByVin service handing ~~~~~，{}", carVin);
        List<SubscriptionServiceDO> list = subscriptionServiceMapper.selectList(
                new LambdaQueryWrapperX<SubscriptionServiceDO>()
                        .eq(SubscriptionServiceDO::getCarVin, carVin)
                        .in(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE, Constants.SERVICE_TYPE.PIVI)
                        .eq(SubscriptionServiceDO::getIsDeleted, false)
        );
        // 查 t_pivi_package表
        PIVIPackageDO piviPackageDO = piviPackageDOMapper.selectOne(PIVIPackageDO::getVin, carVin);
        log.info("getExpireDateByVin service, piviPackageDO : {}", JSON.toJSONString(piviPackageDO));

        if (CollectionUtils.isEmpty(list) && piviPackageDO == null) {
            return null;
        }
        // 查是否PIVI 属性
        IncontrolVehicleDO vehicleDO = incontrolVehicleDOMapper.selectOne(
                new LambdaQueryWrapperX<IncontrolVehicleDO>()
                        .eq(IncontrolVehicleDO::getCarVin, carVin)
                        .eq(IncontrolVehicleDO::getIsDeleted, false)
                        .last("limit 1"));
        log.info("getExpireDateByVin service, vehicleDO : {}", JSON.toJSONString(vehicleDO));

        String carSystemModel = null;
        if (vehicleDO != null) {
            carSystemModel = vehicleDO.getCarSystemModel();
        } else if (piviPackageDO != null) {
            carSystemModel = CarSystemModelEnum.PIVI.getCode();
        }

        //0. 获取车辆5000号信息及品牌
        Vehicle5000InfoDTO vehicle5000Info = new Vehicle5000InfoDTO();
        buildVehicle5000InfoDTO(vehicleDO, vehicle5000Info, carSystemModel, piviPackageDO);


        log.info("list size: {}", list.size());
        ArrayList<ServiceExpireInfoVO> serviceList = Lists.newArrayListWithExpectedSize(3);
        // 1. remote服务
        SubscriptionServiceDO remoteService = list.stream().filter(e -> Constants.SERVICE_TYPE.REMOTE.equals(e.getServiceType())).min(Comparator.comparing(SubscriptionServiceDO::getExpiryDate)).orElse(new SubscriptionServiceDO());
        serviceList.add(ServiceExpireInfoVO.builder().serviceName(REMOTE_SERVICE).expireDate(DateUtil.format(remoteService.getExpiryDate(), NORM_DATE_PATTERN)).build());

        if (CarSystemModelEnum.PIVI.getCode().equals(carSystemModel)) {
            // 2. online服务
            buildOnlineService(carVin, list, piviPackageDO, serviceList);
        }

        // 3.车辆发票日期
        // a.t_pivi_package 通过vin 查 dms_invoice_date 查不到
        // b.再去 t_vehicle_dms 查 invoice_date
        buildDMSDate(carVin, piviPackageDO, serviceList);

        SubscriptionSearchResultDTO result = new SubscriptionSearchResultDTO();
        result.setCarSystemModel(carSystemModel);
        result.setServiceList(serviceList);
        result.setVehicle5000Info(vehicle5000Info);
        return result;
    }

    /**
     * 根据车架号和PIVI包装信息构建DMS日期信息
     * 当PIVI包装信息中不存在发票日期时，将从车辆DMS数据中获取发票日期
     * 并将处理后的日期信息添加到服务列表中
     *
     * @param carVin 车架号，用于查询车辆信息
     * @param piviPackageDO PIVI包装信息对象，可能包含DMS发票日期
     * @param serviceList 服务过期信息列表，用于存储处理后的日期信息
     */
    private void buildDMSDate(String carVin, PIVIPackageDO piviPackageDO, ArrayList<ServiceExpireInfoVO> serviceList) {
        boolean noInvoiceDateInPIVI = piviPackageDO == null || Objects.isNull(piviPackageDO.getDmsInvoiceDate());
        log.info("3.开始处理车辆发票日期，piviPackageDO：{}", JSON.toJSONString(piviPackageDO));
        if (noInvoiceDateInPIVI) {
            VehicleDmsDO vehicleDmsDO = vehicleDmsDOMapper.selectVehicleDmsDOByCarVin(carVin);
            log.info("a.通过vin:{} 查t_pivi_package表 dms_invoice_date查不到；b.再去t_vehicle_dms查invoice_date -> vehicleDmsDO : {}", carVin, JSON.toJSONString(vehicleDmsDO));
            LocalDateTime invoiceDateInBaseTable = null;
            if (vehicleDmsDO != null) {
                invoiceDateInBaseTable = TimeFormatUtil.dmsToLocalDate(vehicleDmsDO.getInvoiceDate());
                log.info("TimeFormatUtil.dmsToLocalDate(vehicleDmsDO.getInvoiceDate()):{}", invoiceDateInBaseTable);
            }
            String encryptText = piplDataUtil.getEncryptText(DateUtil.format(invoiceDateInBaseTable, NORM_DATE_PATTERN));
            serviceList.add(ServiceExpireInfoVO.builder().serviceName(VEHICLE_INVOICE_DATE)
                    .expireDate(encryptText).build());
        } else {
            String encryptText = piplDataUtil.getEncryptText(DateUtil.format(piviPackageDO.getDmsInvoiceDate(), NORM_DATE_PATTERN));
            serviceList.add(ServiceExpireInfoVO.builder().serviceName(VEHICLE_INVOICE_DATE)
                    .expireDate(encryptText).build());
        }
    }

    /**
     * 构建在线服务信息
     *
     * @param carVin 车辆识别号，用于查询车辆相关的服务信息
     * @param list 订阅服务列表，包含各种服务的订阅信息
     * @param piviPackageDO PIVI包数据对象，提供PIVI服务相关的数据
     * @param serviceList 服务到期信息列表，用于存储构建的在线服务信息
     */
    private void buildOnlineService(String carVin, List<SubscriptionServiceDO> list, PIVIPackageDO piviPackageDO, ArrayList<ServiceExpireInfoVO> serviceList) {
        Map<String, LocalDateTime> piviExpireMap = list.stream().filter(e -> Constants.SERVICE_TYPE.PIVI.equals(e.getServiceType())).collect(Collectors.toMap(k -> k.getServicePackage(), v -> v.getExpiryDate(), (e1, e2) -> e1));
        LocalDateTime ecpAppdTime = piviExpireMap.get(ServicePackageEnum.ONLINE_PACK.getPackageName());
        LocalDateTime ecpUnicomTime = piviExpireMap.get(ServicePackageEnum.DATA_PLAN.getPackageName());

        // 2. 子服务
        ArrayList<ServiceExpireInfoVO> onlinePackChildren = Lists.newArrayListWithExpectedSize(3);

        // 2.a. appd 服务  取值优先级：服务表、pivi表 , pivi表无jlrSubscriptionId取值为空
        ServiceExpireInfoVO appdExpireInfoVo = ServiceExpireInfoVO.builder().serviceName(ServicePackageEnum.ONLINE_PACK.getDescCN())
                //sprint41: 改造'APPD到期日'为实时获取替换‘.expireDate(DateUtil.format(appdTime, NORM_DATE_PATTERN)).build();’
                .expireDate(DateUtil.format(qryAPPDExpireDate(carVin), NORM_DATE_PATTERN)).build();
        onlinePackChildren.add(appdExpireInfoVo);

        // 2.b. amap服务   默认同ecp的appd时间，若无取ecp的联通时间
        // sprint47:未登录取手动续费记录的到期日,不存在则取 pivi表的时间
        LocalDateTime amapDisplayTime = ObjectUtil.defaultIfNull(ObjectUtil.defaultIfNull(ecpAppdTime, ecpUnicomTime), getExpireDateFromAppDCuRenewRecords(piviPackageDO));
        ServiceExpireInfoVO amapExpireInfoVo = ServiceExpireInfoVO.builder().serviceName(ServicePackageEnum.CONNECTED_NAVIGATION.getDescCN()).expireDate(DateUtil.format(amapDisplayTime, NORM_DATE_PATTERN)).build();
        onlinePackChildren.add(amapExpireInfoVo);

        // 2.c. unicom服务
        ServiceExpireInfoVO unicomExpireInfoVo = ServiceExpireInfoVO.builder().serviceName(ServicePackageEnum.DATA_PLAN.getDescCN())
                // 联通时间查实时接口
                .expireDate(DateUtil.format(resolveUnicomExpireDate(carVin, environment), NORM_DATE_PATTERN)).build();
        onlinePackChildren.add(unicomExpireInfoVo);

        // 2. onlinePack 服务时间  同amp时间取值逻辑
        ServiceExpireInfoVO onlinePackVO = ServiceExpireInfoVO.builder().serviceName(ONLINE_SERVICE)
                .expireDate(DateUtil.format(amapDisplayTime, NORM_DATE_PATTERN)).build();

        onlinePackVO.setChild(onlinePackChildren);
        serviceList.add(onlinePackVO);
    }

    @Override
    public SubscriptionSearchResultDTO getExpireDateByVinDeeply(String carVin) {
        log.info("getExpireDateByVinDeeply request incoming ~~~~~");
        // 查询并存储车辆信息ByVin
        try {
            vinInitializeService.vinInitializeByQueryVin(carVin);
            return getExpireDateByVin(carVin);
        } catch (TooManyRequestException tooManyRequestException) {
            log.info("getExpireDateByVinDeeply tooManyRequestException, carVin:{}", carVin);
            throw exception(ErrorCodeConstants.GET_EXPIRE_DATE_ERROR);
        }
    }

    private void buildVehicleInfoDTOForPC(IncontrolVehicleDO vehicleDO, Vehicle5000InfoDTO vehicle5000Info, String carSystemModel, PIVIPackageDO piviPackageDO) {
        if (vehicleDO != null) {
            BeanUtils.copyProperties(vehicleDO, vehicle5000Info);
            vehicle5000Info.setCarSystemModel(carSystemModel);
            //设置加密carVin
            vehicle5000Info.setVin(piplDataUtil.getEncryptText(vehicleDO.getCarVin()));
            vehicle5000Info.setVinMix(PIPLDataUtil.getVinMix(vehicleDO.getCarVin()));
            //是否具备信息娱乐服务相关配置 t_pivi_package . jlr_subscription_id 不为空
            if (piviPackageDO == null) {
                vehicle5000Info.setHasInfoEntertain("-");
            } else {
                vehicle5000Info.setHasInfoEntertain(Objects.isNull(piviPackageDO.getJlrSubscriptionId()) ? "N" : "Y");
            }
        }
    }

    private static void buildVehicle5000InfoDTO(IncontrolVehicleDO vehicleDO, Vehicle5000InfoDTO vehicle5000Info, String carSystemModel, PIVIPackageDO piviPackageDO) {
        if (vehicleDO != null) {
            vehicle5000Info.setVin(vehicleDO.getCarVin());
            vehicle5000Info.setBrandName(vehicleDO.getBrandName());
            vehicle5000Info.setConfigName(vehicleDO.getConfigName());
            vehicle5000Info.setModelYear(vehicleDO.getModelYear());
            vehicle5000Info.setConfigCode(vehicleDO.getConfigCode());
            vehicle5000Info.setSeriesCode(vehicleDO.getSeriesCode());
            vehicle5000Info.setSeriesName(vehicleDO.getSeriesName());
            vehicle5000Info.setBrandCode(vehicleDO.getBrandCode());

            vehicle5000Info.setCarSystemModel(carSystemModel);
            //是否具备信息娱乐服务相关配置 t_pivi_package . jlr_subscription_id 不为空

            if (piviPackageDO == null) {
                vehicle5000Info.setHasInfoEntertain("-");
            } else {
                vehicle5000Info.setHasInfoEntertain(Objects.isNull(piviPackageDO.getJlrSubscriptionId()) ? "N" : "Y");
            }

        } else {
            // sprint49 新增
            if (piviPackageDO != null) {
                vehicle5000Info.setVin(piviPackageDO.getVin());
            }
        }
    }


    @Override
    public List<SubscriptionServiceDO> getServicesByVin(String carVin) {
        return subscriptionServiceMapper.selectList(
                new LambdaQueryWrapperX<SubscriptionServiceDO>()
                        .eq(SubscriptionServiceDO::getCarVin, carVin)
                        .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                        .eq(SubscriptionServiceDO::getIsDeleted, false));
    }

    @Override
    public RemoteSearchResultDTO getRemoteExpireDateByVin(String carVin) {
        RemoteSearchResultDTO result = new RemoteSearchResultDTO();
        result.setCarVin(carVin);
        // 1.因为需要用vin换icr账号做续费，所以先查询t_incontrol_vehicle库中是否存在该vin
        IncontrolVehicleDO vehicleDO = incontrolVehicleDOMapper.selectOneByCarVin(carVin);
        result.setExistInEcp(Objects.nonNull(vehicleDO));
        if (!result.isExistInEcp()) {
            log.warn("vin={}在t_incontrol_vehicle中不存在", carVin);
            return result;
        }

        result.setIncontrolId(vehicleDO.getIncontrolId());
        result.setPiviModel(Objects.equals(vehicleDO.getCarSystemModel(), CarSystemModelEnum.PIVI.getCode()));
        if (!result.isPiviModel()) {
            log.warn("vin={}不是PIVI车机, carSystemModel={}", carVin, vehicleDO.getCarSystemModel());
            return result;
        }
        // 2.查询t_subscription_service哪些服务包需要做续费
        List<SubscriptionServiceDO> doList = subscriptionServiceMapper.selectList(
                new LambdaQueryWrapperX<SubscriptionServiceDO>()
                        .eq(SubscriptionServiceDO::getCarVin, carVin)
                        .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE)
                        .eq(SubscriptionServiceDO::getIsDeleted, false));
        if (CollectionUtils.isEmpty(doList)) {
            log.warn("vin={}在t_subscription_service中没有remote服务包", carVin);
            return result;
        }

        SubscriptionServiceDO remoteService = doList.stream()
                .filter(serviceDO -> Objects.nonNull(serviceDO.getExpiryDate()))
                .min(Comparator.comparing(SubscriptionServiceDO::getExpiryDate)).orElse(null);
        if (Objects.isNull(remoteService)) {
            log.warn("vin={}在t_subscription_service中的remote服务包到期时间均为空", carVin);
            return result;
        }
        result.setServiceDOList(doList);
        result.setBeforeExpiryDate(remoteService.getExpiryDate());
        return result;
    }

    @Override
    public List<RemoteSearchResultDTO> getRemoteExpireDateByVinList(List<String> vinList) {
        // 1.因为需要用vin换icr账号做续费，所以先查询t_incontrol_vehicle库中是否存在该vin
        if (CollUtil.isEmpty(vinList)) {
            return buildEmptyResult(vinList);
        }
        List<IncontrolVehicleByCarDTO> vehicleList = incontrolVehicleDOMapper.selectIncontrolVehicleByCarVinList(vinList);
        if (CollUtil.isEmpty(vehicleList)) {
            log.warn("vinList={}在t_incontrol_vehicle中不存在", vinList);
            return buildEmptyResult(vinList);
        }

        Map<String, IncontrolVehicleByCarDTO> vinToVehicleMap = vehicleList.stream().collect(Collectors.toMap(IncontrolVehicleByCarDTO::getCarVin, Function.identity(), (o, n) -> o));
        List<RemoteSearchResultDTO> resultDTOList = new ArrayList<>();
        for (String vin : vinList) {
            IncontrolVehicleByCarDTO vehicle = vinToVehicleMap.get(vin);
            RemoteSearchResultDTO resultDTO = new RemoteSearchResultDTO();
            resultDTO.setCarVin(vin);
            resultDTO.setExistInEcp(Objects.nonNull(vehicle));
            if (resultDTO.isExistInEcp()) {
                resultDTO.setIncontrolId(vehicle.getIncontrolId());
                resultDTO.setPiviModel(Objects.equals(vehicle.getCarSystemModel(), CarSystemModelEnum.PIVI.getCode()));
            }
            resultDTOList.add(resultDTO);
        }

        // 2.查询t_subscription_service哪些服务包需要做续费
        List<SubscriptionServiceDO> doList = subscriptionServiceMapper.selectList(
                new LambdaQueryWrapperX<SubscriptionServiceDO>()
                        .in(SubscriptionServiceDO::getCarVin, vinList)
                        .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE)
                        .eq(SubscriptionServiceDO::getIsDeleted, false));
        if (CollectionUtils.isEmpty(doList)) {
            log.warn("vinList={}在t_subscription_service中没有remote服务包", vinList);
            return resultDTOList;
        }

        Map<String, List<SubscriptionServiceDO>> groupByVin = doList.stream().collect(Collectors.groupingBy(SubscriptionServiceDO::getCarVin));
        for (RemoteSearchResultDTO resultDTO : resultDTOList) {
            String vin = resultDTO.getCarVin();
            List<SubscriptionServiceDO> serviceDOList = groupByVin.get(vin);
            if (CollUtil.isEmpty(serviceDOList)) {
                log.warn("vin={}在t_subscription_service中没有remote服务包", vin);
                continue;
            }
            SubscriptionServiceDO remoteService = serviceDOList.stream()
                    .filter(serviceDO -> Objects.nonNull(serviceDO.getExpiryDate()))
                    .min(Comparator.comparing(SubscriptionServiceDO::getExpiryDate)).orElse(null);
            if (Objects.isNull(remoteService)) {
                log.warn("vin={}在t_subscription_service中的remote服务包到期时间均为空", vin);
            } else {
                resultDTO.setServiceDOList(serviceDOList);
                resultDTO.setBeforeExpiryDate(remoteService.getExpiryDate());
            }
        }
        return resultDTOList;
    }

    @Override
    public void updateRemoteExpireDate(RemoteRenewDetailRecords renewRecords) {
        SubscriptionServiceDO subscriptionServiceDO = new SubscriptionServiceDO();
        subscriptionServiceDO.setExpiryDate(renewRecords.getModifyAfterDate());
        subscriptionServiceDO.setExpireDateUtc0(renewRecords.getModifyAfterDate().minusHours(8));
        subscriptionServiceDO.setUpdatedTime(LocalDateTime.now());
        subscriptionServiceMapper.update(subscriptionServiceDO, new LambdaUpdateWrapper<SubscriptionServiceDO>()
                .eq(SubscriptionServiceDO::getCarVin, renewRecords.getCarVin())
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE)
                .eq(BaseDO::getIsDeleted, false));
    }

    /**
     * 构建vinList全部不在ecp的集合
     *
     * @param vinList vinList
     * @return resultDTOList
     */
    private static List<RemoteSearchResultDTO> buildEmptyResult(List<String> vinList) {
        List<RemoteSearchResultDTO> resultDTOList = new ArrayList<>();
        for (String vin : vinList) {
            RemoteSearchResultDTO resultDTO = new RemoteSearchResultDTO();
            resultDTO.setCarVin(vin);
            resultDTO.setExistInEcp(false);
            resultDTOList.add(resultDTO);
        }
        return resultDTOList;
    }


    /**
     * 查询APPD服务到期时间
     *
     * @param carVin
     * @return
     */
    private LocalDateTime qryAPPDExpireDate(String carVin) {
        try {
            AppDSubscriptionResp response = appDService.getVinSubscriptions(carVin);
            log.info("APPD通过getVinSubscriptions接口实时获取车辆订阅信息, carVin:{}, response:{}", carVin, response);
            if (checkAPPD(response)) {
                return response.getResult().get(0).getExpiresDate();
            }
        } catch (Exception e) {
            log.warn("通过getVinSubscriptions接口实时查询VIN:{}，APPD服务异常", carVin, e);
        }

        return null;
    }

    /**
     * 校验APPD接口 是否返回有效数据
     * 1. queryResult不为空，返回false
     * 2. 查询成功但响应数据为空，返回false
     * 3. 查询成功，且jlrSubscriptionId和expiresDate都不为空
     * 4. 返回true
     *
     * @param response
     * @return
     */
    private boolean checkAPPD(AppDSubscriptionResp response) {
        // 根据queryResult不为空，返回false
        if (StrUtil.isNotBlank(response.getQueryResult())) {
            log.warn("APPD通过getVinSubscriptions接口实时获取车辆订阅信息异常, response:{}", response);
            return false;
        }
        // 如果查询成功但响应数据为空，则返回 false
        if (CollectionUtil.isEmpty(response.getResult())) {
            log.warn("APPD通过getVinSubscriptions接口查询成功但响应数据为空, response:{}", response);
            return false;
        }
        // 判断订阅记录是否有效
        AppDSubscriptionData appDSubscriptionData = response.getResult().get(0);
        if (Objects.isNull(appDSubscriptionData.getJlrSubscriptionId()) || Objects.isNull(appDSubscriptionData.getExpiresDate())) {
            log.warn("APPD通过getVinSubscriptions接口查询成功但jlrSubscriptionId或expiresDate为空, response:{}", response);
            return false;
        }

        return true;
    }

    private LocalDateTime resolveUnicomExpireDate(String carVin, Environment environment) {
        // dev\test env skip real-time query
        if ("dev".equals(environment.getProperty(PROFILE)) ||
                "test".equals(environment.getProperty(PROFILE))) {
            log.warn("Current environment is {}, skipping real-time query for unicom expire date.", environment.getProperty(PROFILE));
            return null;
        } else {
            return qryUnicomExpireDate(carVin);
        }
    }

    private LocalDateTime qryUnicomExpireDate(String carVin) {
        try {
            UnicomRespVO unicomRespVO = piviUniconService.getSimCarInfoByCarVin(carVin);
            List<ProductBookInfo> productBookInfoList = unicomRespVO.getUnicomRespData().getProductBookInfoList();
            ProductBookInfo latestProductBookInfo = productBookInfoList.stream().filter(e -> UnicomBookStatusEnum.ACTIVE.getType().equals(e.getBookStatus()))
                    .max(Comparator.comparingLong(e -> Long.parseLong(e.getExpireTime()))).orElse(new ProductBookInfo());
            return DateUtil.parseLocalDateTime(latestProductBookInfo.getExpireTime(), "yyyyMMddHHmmSS");
        } catch (Exception e) {
            log.error("查询VIN:{}联通真实过期服务异常", carVin, e);
        }
        return null;
    }


    /**
     * 根据VIN查询过期时间
     * @param incontrolVehicleDO
     * @return SubscriptionSearchResultDTO
     */
    public SubscriptionSearchLocalDTO getExpireDateByVinLocal(IncontrolVehicleDO incontrolVehicleDO) {
        SubscriptionSearchLocalDTO result = new SubscriptionSearchLocalDTO();
        log.info("getExpireDateByVin service handing ~~~~~，{}", incontrolVehicleDO.getCarVin());
        List<SubscriptionServiceDO> list = subscriptionServiceMapper.selectListByCarVin(incontrolVehicleDO.getCarVin());
        LocalDateTime piviExpireDate;
        // 查 t_pivi_package表
        PIVIPackageDO piviPackageDO = piviPackageDOMapper.findICCIDByCarVin(incontrolVehicleDO.getCarVin());
        log.info("getExpireDateByVin service, piviPackageDO : {}", piviPackageDO);

        boolean noAppd = piviPackageDO == null || Objects.isNull(piviPackageDO.getJlrSubscriptionId());
        //0. 获取车辆5000号信息及品牌
        Vehicle5000InfoDTO vehicle5000Info = new Vehicle5000InfoDTO();
        buildVehicleInfoDTOForPC(incontrolVehicleDO, vehicle5000Info, incontrolVehicleDO.getCarSystemModel(), piviPackageDO);
        result.setCarSystemModel(incontrolVehicleDO.getCarSystemModel());
        result.setVehicle5000Info(vehicle5000Info);

        piviExpireDate = piviPackageDO == null ? null : piviPackageDO.getExpiryDate();

        if (CollectionUtils.isEmpty(list)) {
            result.setServiceList(new ArrayList<>());
            return result;
        }

        log.info("getExpireDateByVin service, vehicleDO : {}", incontrolVehicleDO);

        log.info("list size: {}", list.size());
        ArrayList<ServiceExpireInfoVO> serviceList = Lists.newArrayListWithExpectedSize(3);
        // 1. remote服务
        SubscriptionServiceDO remoteService = list.stream().filter(e -> Constants.SERVICE_TYPE.REMOTE.equals(e.getServiceType())).min(Comparator.comparing(SubscriptionServiceDO::getExpiryDate)).orElse(new SubscriptionServiceDO());
        serviceList.add(ServiceExpireInfoVO.builder().serviceName(REMOTE_SERVICE).expireDate(DateUtil.format(remoteService.getExpiryDate(), NORM_DATE_PATTERN)).build());
        serviceList.add(buildOnlineService(incontrolVehicleDO, piviPackageDO, vehicle5000Info, list, noAppd, piviExpireDate));

        // 3.车辆发票日期
        // a.t_pivi_package 通过vin 查 dms_invoice_date 查不到
        // b.再去 t_vehicle_dms 查 invoice_date
        boolean noInvoiceDateInPIVI = piviPackageDO == null || Objects.isNull(piviPackageDO.getDmsInvoiceDate());
        log.info("3.开始处理车辆发票日期，piviPackageDO：{}", piviPackageDO);
        if (noInvoiceDateInPIVI) {
            VehicleDmsDO vehicleDmsDO = vehicleDmsDOMapper.selectVehicleDmsDOByCarVin(incontrolVehicleDO.getCarVin());
            log.info("a.通过vin:{} 查t_pivi_package表 dms_invoice_date查不到；b.再去t_vehicle_dms查invoice_date -> vehicleDmsDO : {}", incontrolVehicleDO.getCarVin(), JSON.toJSONString(vehicleDmsDO));
            LocalDateTime invoiceDateInBaseTable = null;
            if (vehicleDmsDO != null) {
                invoiceDateInBaseTable = TimeFormatUtil.dmsToLocalDate(vehicleDmsDO.getInvoiceDate());
                log.info("TimeFormatUtil.dmsToLocalDate(vehicleDmsDO.getInvoiceDate()):{}", invoiceDateInBaseTable);
            }
            serviceList.add(ServiceExpireInfoVO.builder().serviceName(VEHICLE_INVOICE_DATE)
                    .expireDate(DateUtil.format(invoiceDateInBaseTable, NORM_DATE_PATTERN)).build());
        } else {
            serviceList.add(ServiceExpireInfoVO.builder().serviceName(VEHICLE_INVOICE_DATE)
                    .expireDate(DateUtil.format(piviPackageDO.getDmsInvoiceDate(), NORM_DATE_PATTERN)).build());
        }

        result.setServiceList(serviceList);

        return result;
    }

    /**
     * 构建InControl在线服务的到期信息
     *
     * @param incontrolVehicleDO 车辆信息DO
     * @param piviPackageDO PIVI套餐信息DO
     * @param vehicle5000Info 车辆5000信息DTO
     * @param list 订阅服务DO列表
     * @param noAppd 是否没有APPD服务的标志
     * @param piviExpireDate PIVI到期日期
     * @return 返回构建的ServiceExpireInfoVO对象，包含在线服务的到期信息
     */
    private ServiceExpireInfoVO buildOnlineService(IncontrolVehicleDO incontrolVehicleDO, PIVIPackageDO piviPackageDO, Vehicle5000InfoDTO vehicle5000Info, List<SubscriptionServiceDO> list, boolean noAppd, LocalDateTime piviExpireDate) {
        if (Objects.isNull(piviPackageDO) || !CarSystemModelEnum.PIVI.getCode().equals(incontrolVehicleDO.getCarSystemModel())) {
            return ServiceExpireInfoVO.builder().serviceName(ONLINE_SERVICE).expireDate(null).build();
        }

        Map<String, LocalDateTime> piviExpireMap = list.stream().filter(e -> Constants.SERVICE_TYPE.PIVI.equals(e.getServiceType())).collect(Collectors.toMap(SubscriptionServiceDO::getServicePackage, SubscriptionServiceDO::getExpiryDate, (e1, e2) -> e1));
        // 如果ecp没有PIVI服务，直接返回到期日为空
        if (CollUtil.isEmpty(piviExpireMap)) {
            return ServiceExpireInfoVO.builder().serviceName(ONLINE_SERVICE).expireDate(null).build();
        }
        //实名查询
        buildVehicle5000Info(incontrolVehicleDO, vehicle5000Info, piviPackageDO);

        LocalDateTime ecpAppdTime = piviExpireMap.get(ServicePackageEnum.ONLINE_PACK.getPackageName());
        LocalDateTime ecpUnicomTime = piviExpireMap.get(ServicePackageEnum.DATA_PLAN.getPackageName());

        // 2. 子服务
        ArrayList<ServiceExpireInfoVO> onlinePackChildren = Lists.newArrayListWithExpectedSize(3);

        // 2.a. appd 服务  取值优先级：服务表、pivi表 , pivi表无jlrSubscriptionId取值为空
        LocalDateTime appdTime = ObjectUtil.defaultIfNull(ecpAppdTime, noAppd ? null : piviExpireDate);
        ServiceExpireInfoVO appdExpireInfoVo = ServiceExpireInfoVO.builder().serviceName(ServicePackageEnum.ONLINE_PACK.getDescCN())
                .expireDate(DateUtil.format(appdTime, NORM_DATE_PATTERN)).build();
        onlinePackChildren.add(appdExpireInfoVo);

        // 2.b. amap服务   默认同ecp的appd时间，若无取ecp的联通时间，仍无说明未登录取 pivi表的时间
        LocalDateTime amapTime = ObjectUtil.defaultIfNull(ObjectUtil.defaultIfNull(ecpAppdTime, ecpUnicomTime), piviExpireDate);
        ServiceExpireInfoVO amapExpireInfoVo = ServiceExpireInfoVO.builder().serviceName(ServicePackageEnum.CONNECTED_NAVIGATION.getDescCN()).expireDate(DateUtil.format(amapTime, NORM_DATE_PATTERN)).build();
        onlinePackChildren.add(amapExpireInfoVo);

        // 2.c. unicom服务
        LocalDateTime unicomTime = piviExpireMap.get(ServicePackageEnum.DATA_PLAN.getPackageName());
        ServiceExpireInfoVO unicomExpireInfoVo = ServiceExpireInfoVO.builder().serviceName(ServicePackageEnum.DATA_PLAN.getDescCN())
                // 联通时间查实时接口
                .expireDate(DateUtil.format(unicomTime, NORM_DATE_PATTERN)).build();
        onlinePackChildren.add(unicomExpireInfoVo);

        // 2. onlinePack 服务时间  同amp时间取值逻辑
        ServiceExpireInfoVO onlinePackVO = ServiceExpireInfoVO.builder().serviceName(ONLINE_SERVICE)
                .expireDate(DateUtil.format(amapTime, NORM_DATE_PATTERN)).build();

        onlinePackVO.setChild(onlinePackChildren);
        return onlinePackVO;
    }

    /**
     * 根据环境配置和车辆信息构建Vehicle5000信息
     * 在不同环境下（dev、test、uat、prd），获取SIM卡信息的逻辑有所不同
     * 本方法旨在根据当前环境和车辆信息，填充Vehicle5000InfoDTO对象的SIM卡信息和实名认证标志
     *
     * @param incontrolVehicleDO 车辆信息对象，包含车辆的基本信息
     * @param vehicle5000Info 车辆5000信息DTO对象，用于存储构建后的车辆信息
     * @param piviPackageDO PIviPackage信息对象，包含PIviPackage的相关信息
     */
    private void buildVehicle5000Info(IncontrolVehicleDO incontrolVehicleDO, Vehicle5000InfoDTO vehicle5000Info, PIVIPackageDO piviPackageDO) {
        try {
            // dev \test \ uat env Assign a value to simCardInfo
            String env = environment.getProperty(Constants.PROFILE_ACTIVE);
            if ("dev".equals(env) || "test".equals(env)) {
                vehicle5000Info.setIccid(piviPackageDO.getIccid());
                vehicle5000Info.setRealName(UnicomRealnameFlagEnum.TRUE.getCode());
            } else {
                //uat 和prd实际直接调用
                EsimInfoVO esimInfoVO = piviUniconService.checkVinRNRInfo(incontrolVehicleDO.getCarVin());
                vehicle5000Info.setIccid(esimInfoVO.getIccid());
                vehicle5000Info.setRealName(esimInfoVO.getRealnameFlag());
            }
        } catch (Exception e) {
            vehicle5000Info.setIccid(piviPackageDO.getIccid());
            vehicle5000Info.setRealName(UnicomRealnameFlagEnum.FAIL.getCode());
            log.info("实名查询错误error:", e);
        }
    }

    /**
     * 从AppDCu续费记录中获取过期日期
     * 根据PIVIPackageDO对象中的信息，查询并计算最新的续费记录对应的过期日期
     * 如果续费记录不存在，则使用PIVIPackageDO中的过期日期
     *
     * @param piviPackageDO 包含车辆信息和过期日期的PIVI包对象
     * @return 续费后的过期日期，如果没有续费记录则返回原始过期日期
     */
    @Override
    public LocalDateTime getExpireDateFromAppDCuRenewRecords(PIVIPackageDO piviPackageDO) {
        if (Objects.isNull(piviPackageDO)) {
            return null;
        }
        // 根据vin查询手动续费成功的记录
        List<AppDCuRenewRecords> appDCuRenewRecords = appDCuRenewRecordsMapper.getByVinAndStatus(piviPackageDO.getVin(), AppDRenewStatusEnum.RENEW_SUCCESS.getStatus());

        // 获取最新的续费记录
        AppDCuRenewRecords latestRecord;
        // 有appD服务,appD手动续费记录不存在则用pivi_package中的到期日
        if (Objects.nonNull(piviPackageDO.getJlrSubscriptionId())) {
            // 取appDCuRenewRecords中最新的一条appd的续费记录
            latestRecord = appDCuRenewRecords.stream()
                    .filter(records -> RenewServiceTypeEnum.APPD.getServiceType().equals(records.getRenewServiceType()))
                    .max(Comparator.comparing(AppDCuRenewRecords::getId)).orElse(null);
        } else {
            // 没有appD服务,cu手动续费记录不存在则用pivi_package中的到期日
            // 取appDCuRenewRecords中最新的一条cu的续费记录
            latestRecord = appDCuRenewRecords.stream()
                    .filter(records -> RenewServiceTypeEnum.UNICOM.getServiceType().equals(records.getRenewServiceType()))
                    .max(Comparator.comparing(AppDCuRenewRecords::getId)).orElse(null);
        }

        return Objects.nonNull(latestRecord) ? latestRecord.getRenewAfterExpiryDate() : piviPackageDO.getExpiryDate();
    }
}
