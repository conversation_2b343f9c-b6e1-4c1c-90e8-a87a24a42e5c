package com.jlr.ecp.subscription.handle.impl;

import cn.hutool.core.util.StrUtil;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.handle.RedisDelayQueueHandle;
import com.jlr.ecp.subscription.service.vehicle.IncontrolVehicleService;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 创建商品定时上架处理类
 */
@Component
@Slf4j
public class EcpIcrTsdpQryRetryTask implements RedisDelayQueueHandle<String> {

    /**
     * 这是处理商品相关操作的服务
     */
    @Resource
    private IncontrolVehicleService incontrolVehicleService;

    @Resource
    private VinInitializeService vinInitializeService;

    /**
     * 调用商品上架方法
     *
     * @param value
     */
    @Override
    public void execute(String value) {
        log.info("Processing IcrTsdpQryRetry Task launch: {}", value);
        // 提取 inControlId
        if (StrUtil.isNotBlank(value)) {
            // 目前 value就是icr账号::jlrId
            // 调用服务方法
            //incontrolVehicleService.getAndStoreCarInfo(value);
            String[] inControlIdAndJlrId = value.split(Constants.CONCAT_SYMBOL);
            vinInitializeService.vinInitializeByLogin(inControlIdAndJlrId[0], inControlIdAndJlrId[1]);
        }
    }
}