package com.jlr.ecp.subscription.service.listener.dto;


import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 车辆绑定通知dto
 */
@Getter
@Setter
public class VinBindNotifyDto {
    /**
     * bindNo
     */
    private String bindNo;
    /**
     * jlrId
     */
    private String jlrId;
    /**
     * brand
     */
    private String brand;
    /**
     * vin
     */
    private String vin;
    /**
     * modelCode
     */
    private String modelCode;
    /**
     * dmsNameplate
     */
    private String dmsNameplate;
    /**
     * configCode
     */
    private String configCode;
    /**
     * firstOwner
     */
    private Integer firstOwner;
    /**
     * firstOwnerJlrId
     */
    private String firstOwnerJlrId;
    /**
     * firstOwnerBindTime
     */
    private String firstOwnerBindTime;
    /**
     * invoiceDate
     */
    private String invoiceDate;
    /**
     * relationShip
     */
    private String relationShip;
    /**
     * operateType
     */
    private String operateType;
    /**
     * operateTime
     */
    private String operateTime;
}
