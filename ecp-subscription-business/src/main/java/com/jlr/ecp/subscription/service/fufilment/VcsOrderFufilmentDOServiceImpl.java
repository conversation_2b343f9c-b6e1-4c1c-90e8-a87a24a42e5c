package com.jlr.ecp.subscription.service.fufilment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.kafka.producer.ProducerTool;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.framework.tenant.core.context.TenantContextHolder;
import com.jlr.ecp.order.api.order.OrderAppApi;
import com.jlr.ecp.order.api.order.dto.OrderItemCodeListDTO;
import com.jlr.ecp.order.api.order.vo.base.OrderItemBaseVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.CarVinAndServiceTypeDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.SubBrandListDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.dto.VinsAndServiceDateDTO;
import com.jlr.ecp.subscription.api.vcsorderfufilment.vo.*;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.constant.KafkaConstants;
import com.jlr.ecp.subscription.controller.admin.unicom.dto.EsimInfoVO;
import com.jlr.ecp.subscription.dal.dataobject.businesserror.InterBusiErrorLogDO;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentDO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderRollbackDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.businesserror.InterBusiErrorLogMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentDOMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFufilmentRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderRollbackDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.VehicleDmsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.dal.repository.ConsumerVehicleRepository;
import com.jlr.ecp.subscription.dal.repository.IncontrolVehicleRepository;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.enums.busierror.BusiErrorEnum;
import com.jlr.ecp.subscription.enums.fufil.CancelServiceStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.FufilmentServiceStatusEnum;
import com.jlr.ecp.subscription.enums.fufil.FulfilTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceStatusEnum;
import com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.enums.unicom.UnicomRealnameFlagEnum;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;
import com.jlr.ecp.subscription.kafka.message.cancel.CancelSuccessMessage;
import com.jlr.ecp.subscription.kafka.message.fufil.FufilmentSuccessMessage;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.pivi.PIVIUnicomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.apache.commons.lang3.tuple.Triple;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description 针对表【t_vcs_order_fufilment(t_vcs_order_fufilment)】的数据库操作Service实现
 * @createDate 2024-01-19 14:15:09
 */
@Service
@Slf4j
public class VcsOrderFufilmentDOServiceImpl extends ServiceImpl<VcsOrderFufilmentDOMapper, VcsOrderFufilmentDO>
        implements VcsOrderFufilmentDOService {

    @Resource
    private VcsOrderFufilmentDOMapper vcsOrderFufilmentDOMapper;

    @Resource
    private InterBusiErrorLogMapper interBusiErrorLogMapper;

    @Resource
    private VcsOrderRollbackDOMapper vcsOrderRollbackDOMapper;

    @Resource
    private SubscriptionServiceMapper subscriptionServiceMapper;

    @Resource
    private OrderAppApi orderAppApi;

    @Resource
    private PIVIAmaPService piviAmaPService;

    @Resource
    private IncontrolVehicleDOMapper incontrolVehicleDOMapper;

    @Resource
    private VehicleDmsDOMapper vehicleDmsDOMapper;

    @Resource
    private ProducerTool producerTool;

    @Resource
    RestTemplate restTemplate;

    @Value("${tsdp.subscriptionRenewUrl}")
    private String subscriptionRenewUrl;

    @Value("${tsdp.ifEcomToken}")
    private String ifEcomToken;

    @Resource
    private ConsumerVehicleRepository consumerVehicleRepository;

    @Resource
    private IncontrolVehicleRepository incontrolVehicleRepository;

    @Resource
    RedisService redisService;

    /**
     * carVin和serviceType的连接字符串
     */
    private static final String CONCAT_SYMBOL = "::";

    @Resource
    private PIVIUnicomService piviUnicomService;

    @Resource
    private VcsOrderFufilmentRecordsMapper vcsOrderFufilmentRecordsMapper;

    @Override
    public VcsOrderFulfilmentRespVO getOneByOrderItemCode(String orderItemCode) {
        // 查询最近一次的履约信息
        VcsOrderFufilmentDO vcsOrderFufilmentDO = vcsOrderFufilmentDOMapper.selectOne(new LambdaQueryWrapperX<VcsOrderFufilmentDO>()
                        .eq(VcsOrderFufilmentDO::getOrderItemCode, orderItemCode)
                        .eq(BaseDO::getIsDeleted, false)
                        //.eq(VcsOrderFufilmentDO::getServiceStatus, ServiceStatusEnum.ACTIVE.getCode())
                        .orderByDesc(VcsOrderFufilmentDO::getId)
                        .last("limit 1")
        );

        //通过fulfilment_id 查询相应的退单履约
        VcsOrderRollbackDO vcsOrderRollbackDO = null;
        if (Objects.nonNull(vcsOrderFufilmentDO)) {
            vcsOrderRollbackDO = vcsOrderRollbackDOMapper.selectOne(new LambdaQueryWrapperX<VcsOrderRollbackDO>()
                    .eq(VcsOrderRollbackDO::getFufilmentId, vcsOrderFufilmentDO.getFufilmentId())
                    .eq(BaseDO::getIsDeleted, false)
                    .orderByDesc(VcsOrderRollbackDO::getId)
                    .last("limit 1")
            );
        }

        //如果有退单履约，则取退单履约的service_status
        VcsOrderFulfilmentRespVO vcsOrderFulfilmentRespVO = new VcsOrderFulfilmentRespVO();

        if (Objects.nonNull(vcsOrderRollbackDO)){
            BeanUtils.copyProperties(vcsOrderRollbackDO, vcsOrderFulfilmentRespVO);
            vcsOrderFulfilmentRespVO.setServiceStatusDesc(CancelServiceStatusEnum.getDescriptionByCode(vcsOrderRollbackDO.getServiceStatus()));
        }else{
            if (vcsOrderFufilmentDO != null) {
                BeanUtils.copyProperties(vcsOrderFufilmentDO, vcsOrderFulfilmentRespVO);
                vcsOrderFulfilmentRespVO.setServiceStatusDesc(ServiceStatusEnum.getDescriptionByCode(vcsOrderFufilmentDO.getServiceStatus()));
            }
        }
        return vcsOrderFulfilmentRespVO;
    }

    /**
     * 废接口
     *
     * @param orderItemCodeList
     * @return
     */
    @Override
    public List<VcsOrderFulfilmentRespVO> getHistoryByOrderItemCodeList(String orderItemCodeList) {
        List<VcsOrderFulfilmentRespVO> list = vcsOrderFufilmentDOMapper.getHistoryByOrderItemCodeList(orderItemCodeList);

        return null;
    }


    /**
     * 一。 传过来的数据 Map<揽胜, Map<car_vin, series_code:series_name> > map
     * <p>
     * eg."揽胜": {
     * "SAL1A2FU3RA175768": "L461:揽胜运动版",
     * "SALKABFU1RA208559": "L460:揽胜",
     * "SALKABFU1RA208237": "L460:揽胜"
     * }
     * <p>
     * 二。我的订阅 tab列表下 1.揽胜 2.卫士 3.发现
     * <p>`
     * 一个人买
     * car1
     * item1
     * item2
     * car1
     * item3
     * item4
     * item5
     * tips：每个item都是 独特的 商品明细（order_item_code）
     * <p>
     * 属于揽胜的车 一个人买 car1 car2 ，订阅列表只查 所有List<car>下订阅过的商品，哪怕未激活
     * why? ---（支付后才会去订阅）
     *
     * @param subBrandListDTO
     * @return
     */
    @Override
    public List<OrderSubscriptionRespVO> getSubscriptionList(SubBrandListDTO subBrandListDTO) {
        // 准备返参容器List<OrderSubscriptionRespVO> list
        List<OrderSubscriptionRespVO> result = new ArrayList<>();

        // 1.从map中取出所有的List<car_vin>
        List<String> carVinList = extractCarVinList(subBrandListDTO);
        // tab下没有车 -》没有car_vin
        if (CollUtil.isEmpty(carVinList)) {
            return Collections.emptyList();
        }

        // 查询已激活和关闭激活的订单
        List<VcsOrderFufilmentDO> vcsOrderList = getVcsOrderList(carVinList, subBrandListDTO.getBrandCode());
        // 查到的履约订单为空
        if (CollUtil.isEmpty(vcsOrderList)) {
            log.info("已激活和关闭激活的履约订单为空, carVinList={}, brandCode={}", carVinList, subBrandListDTO.getBrandCode());
            return Collections.emptyList();
        }

        // 获取车辆下的订阅服务的到期时间 key=carVin::serviceType value=expiryDate
        Map<String, LocalDateTime> vinMapDate = getVinMapServiceDate(carVinList);
        // 订阅服务为空
        if (CollUtil.isEmpty(vinMapDate)) {
            log.info("订阅服务为空, carVinList={}, brandCode={}", carVinList, subBrandListDTO.getBrandCode());
            return Collections.emptyList();
        }
        // 3.vcsOrderFufilmentDOS 根据 carVin进行分组
        // 组装成Map<carVin::serviceType, List<VcsOrderFufilmentDO>>
        Map<String, List<VcsOrderFufilmentDO>> vcsOrderGroupedByCarVin = groupVcsOrdersByCarVin(vcsOrderList);

        //a.遍历Map<carVin::serviceType, List<VcsOrderFufilmentDO>>,找到 每个car_vin下的最新的一个vcs订单（通过Max_id的order_item_code,
        // left:orderItemCodeList middle:Map<carVin::serviceType,orderCode> right:Map<carVin::serviceType,orderItemCode>
        Triple<List<String>, Map<String, String>, Map<String, String>> latestData = findLatestData(vcsOrderGroupedByCarVin);

        // 组装成List<order_item_code>
        List<String> orderItemCodeList = latestData.getLeft();

        // 组装成Map<carVin::serviceType,orderCode>
        Map<String, String> carVinToOrderCodeMap = latestData.getMiddle();

        // 组装成Map<carVin::serviceType,orderItemCode>
        Map<String, String> carVinToOrderItemCodeMap = latestData.getRight();

        //b.远程调用订单服务，根据List<order_item_code> 查询到最新商品信息
        List<OrderItemBaseVO> latestProductList = getOrderItemList(orderItemCodeList);
        if(CollUtil.isEmpty(latestProductList)){
            return Collections.emptyList();
        }
        // 组装成Map<carVin::serviceType,OrderItemBaseVO对象>
        Map<String, OrderItemBaseVO> carVinToProductInfoMap = mapCarVinToLatestProductInfo(carVinToOrderItemCodeMap, latestProductList);

        //c.远程调用订单服务，根据List<order_code> 查询到售后处理中的订单，得到vinAndServiceType集合
        Set<String> afterSalesVinSet = getAfterSalesCarVin(carVinToOrderCodeMap);

        //d.遍历Map<carVin::serviceType, List<VcsOrderFufilmentDO>>, 得到Map<carVin::serviceType, List<SubDurationHistoryVO>> 时间段按降序排列，只取最近的三条
        Map<String, List<SubDurationHistoryVO>> carVinToHistoryMap = extractSubDurationHistory(vcsOrderGroupedByCarVin);

        // 4.遍历Map<揽胜, Map<car_vin, series_code:series_name> > map ,每遍历一次组装一个OrderSubscriptionRespVO对象，放入list中
        subBrandListDTO.getBrandVehicles().forEach((brand, carVinSeriesMap) -> {// 遍历ServiceTypeEnum枚举
            for (Map.Entry<String, String> entry : carVinSeriesMap.entrySet()) {
                String carVin = entry.getKey();
                String seriesInfo = entry.getValue();
                Arrays.stream(ServiceTypeEnum.values()).forEach(serviceTypeEnum -> {
                    Integer serviceType = serviceTypeEnum.getCode();
                    String vinAndServiceType = carVin + CONCAT_SYMBOL + serviceType;
                    // 4.1 组装OrderSubscriptionRespVO对象
                    OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();

                    // 4.1.1 组装车型信息
                    SubVehicleInfoVO vehicleInfo = createSubVehicleInfoVO(seriesInfo, carVin, serviceType, vinAndServiceType, afterSalesVinSet);
                    orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);

                    // 4.1.2 组装商品信息
                    SubProductItemInfoVO subProductItemInfoVO = new SubProductItemInfoVO();

                    OrderItemBaseVO productInfo = carVinToProductInfoMap.get(vinAndServiceType);
                    LocalDateTime endTime = vinMapDate.get(vinAndServiceType);
                    if (productInfo == null || endTime == null) {
                        return;
                    }
                    BeanUtils.copyProperties(productInfo, subProductItemInfoVO);
                    subProductItemInfoVO.setServiceEndDate(endTime);

                    // 设置服务状态
                    boolean isExpire = endTime.toLocalDate().isBefore(LocalDateTime.now().toLocalDate());
                    subProductItemInfoVO.setServiceStatusDesc(isExpire ? Constants.SUBSCRIPTION_EXPIRED : Constants.SUBSCRIBING);

                    orderSubscriptionRespVO.setProductInfo(subProductItemInfoVO);

                    // 4.1.3 组装历史订阅时间段
                    List<SubDurationHistoryVO> subDurationHistoryVOS = carVinToHistoryMap.get(vinAndServiceType);
                    orderSubscriptionRespVO.setDurationHistoryList(subDurationHistoryVOS);

                    result.add(orderSubscriptionRespVO);
                });
            }
        });

        return result;
    }

    @Override
    public Map<String, List<OrderSubscriptionRespVO>> getSubscriptionList(String jlrId, String clientId) {
        // 获取主动登录的车辆vin
        List<IncontrolVehicleDO> incontrolVehicleDOList = incontrolVehicleRepository.getBindIncontrolVehicle(jlrId, clientId);
        // 获取小程序绑定的车辆vin
        List<ConsumerVehicleDO> vehicleDOList = consumerVehicleRepository.getListByJlrId(jlrId);
        // 合并所有车辆VIN
        Set<String> allCarVinSet = Stream.concat(
                incontrolVehicleDOList.stream().map(IncontrolVehicleDO::getCarVin),
                vehicleDOList.stream().map(ConsumerVehicleDO::getCarVin)
        ).collect(Collectors.toSet());
        // jlrId下没有车
        if (CollUtil.isEmpty(allCarVinSet)) {
            log.info("主动登录和小程序绑定中没有绑定关系, jlrId={}", jlrId);
            return Collections.emptyMap();
        }
        // 准备返参容器List<OrderSubscriptionRespVO> list
        Map<String, List<OrderSubscriptionRespVO>> result = new HashMap<>();

        // 查询已激活和关闭激活的订单
        List<VcsOrderFufilmentDO> vcsOrderList = getVcsOrderList(new ArrayList<>(allCarVinSet), clientId);
        // 查到的履约订单为空
        if (CollUtil.isEmpty(vcsOrderList)) {
            log.info("已激活和关闭激活的履约订单为空, allCarVinSet={}, brandCode={}", allCarVinSet, clientId);
            return Collections.emptyMap();
        }

        List<String> carVinList = vcsOrderList.stream().map(VcsOrderFufilmentDO::getCarVin).distinct().collect(Collectors.toList());

        // 查询车型编码
        Map<String, Map<String, String>> brandNameToSeriesMapping = getBrandNameToSeriesMapping(carVinList, clientId);

        // 获取车辆下的订阅服务的到期时间 key=carVin::serviceType value=expiryDate
        Map<String, LocalDateTime> vinMapDate = getVinMapServiceDate(carVinList);
        // 订阅服务为空
        if (CollUtil.isEmpty(vinMapDate)) {
            log.info("订阅服务为空, carVinList={}, brandCode={}", carVinList, clientId);
            return Collections.emptyMap();
        }

        // 3.vcsOrderFufilmentDOS 根据 carVin进行分组
        // 组装成Map<carVin::serviceType, List<VcsOrderFufilmentDO>>
        Map<String, List<VcsOrderFufilmentDO>> vcsOrderGroupedByCarVin = groupVcsOrdersByCarVin(vcsOrderList);

        //a.遍历Map<carVin::serviceType, List<VcsOrderFufilmentDO>>,找到 每个car_vin下的最新的一个vcs订单（通过Max_id的order_item_code,
        // left:orderItemCodeList middle:Map<carVin::serviceType,orderCode> right:Map<carVin::serviceType,orderItemCode>
        Triple<List<String>, Map<String, String>, Map<String, String>> latestData = findLatestData(vcsOrderGroupedByCarVin);

        // 组装成List<order_item_code>
        List<String> orderItemCodeList = latestData.getLeft();

        // 组装成Map<carVin::serviceType,orderCode>
        Map<String, String> carVinToOrderCodeMap = latestData.getMiddle();

        // 组装成Map<carVin::serviceType,orderItemCode>
        Map<String, String> carVinToOrderItemCodeMap = latestData.getRight();

        //b.远程调用订单服务，根据List<order_item_code> 查询到最新商品信息
        List<OrderItemBaseVO> latestProductList = getOrderItemList(orderItemCodeList);
        if(CollUtil.isEmpty(latestProductList)){
            return Collections.emptyMap();
        }
        // 组装成Map<carVin::serviceType,OrderItemBaseVO对象>
        Map<String, OrderItemBaseVO> carVinToProductInfoMap = mapCarVinToLatestProductInfo(carVinToOrderItemCodeMap, latestProductList);

        //c.远程调用订单服务，根据List<order_code> 查询到售后处理中的订单，得到vinAndServiceType集合
        Set<String> afterSalesVinSet = getAfterSalesCarVin(carVinToOrderCodeMap);

        // 4.遍历Map<揽胜, Map<car_vin, series_code:series_name> > map ,每遍历一次组装一个OrderSubscriptionRespVO对象，放入list中
        brandNameToSeriesMapping.forEach((brand, carVinSeriesMap) -> {// 遍历ServiceTypeEnum枚举
            for (Map.Entry<String, String> entry : carVinSeriesMap.entrySet()) {
                String carVin = entry.getKey();
                String seriesInfo = entry.getValue();
                Arrays.stream(ServiceTypeEnum.values()).forEach(serviceTypeEnum -> {
                    Integer serviceType = serviceTypeEnum.getCode();
                    String vinAndServiceType = carVin + CONCAT_SYMBOL + serviceType;
                    // 4.1 组装OrderSubscriptionRespVO对象
                    OrderSubscriptionRespVO orderSubscriptionRespVO = new OrderSubscriptionRespVO();

                    // 4.1.1 组装车型信息
                    SubVehicleInfoVO vehicleInfo = createSubVehicleInfoVO(seriesInfo, carVin, serviceType, vinAndServiceType, afterSalesVinSet);
                    orderSubscriptionRespVO.setVehicleInfo(vehicleInfo);

                    // 4.1.2 组装商品信息
                    SubProductItemInfoVO subProductItemInfoVO = new SubProductItemInfoVO();

                    OrderItemBaseVO productInfo = carVinToProductInfoMap.get(vinAndServiceType);
                    LocalDateTime endTime = vinMapDate.get(vinAndServiceType);
                    if (productInfo == null || endTime == null) {
                        return;
                    }
                    BeanUtils.copyProperties(productInfo, subProductItemInfoVO);
                    subProductItemInfoVO.setServiceEndDate(endTime);

                    // 设置服务状态
                    boolean isExpire = endTime.toLocalDate().isBefore(LocalDateTime.now().toLocalDate());
                    subProductItemInfoVO.setServiceStatusDesc(isExpire ? Constants.SUBSCRIPTION_EXPIRED : Constants.SUBSCRIBING);

                    // 设置服务类型
                    subProductItemInfoVO.setServiceType(serviceTypeEnum.getDescription());

                    orderSubscriptionRespVO.setProductInfo(subProductItemInfoVO);

                    List<OrderSubscriptionRespVO> vinToResult = result.getOrDefault(carVin, new ArrayList<>());
                    vinToResult.add(orderSubscriptionRespVO);
                    result.put(carVin, vinToResult);
                });
            }
        });

        return result;
    }


    /**
     * 根据车辆识别码列表和品牌代码获取VCS订单履行信息列表。
     *
     * @param carVinList 车辆识别码列表
     * @param branCode 品牌代码
     * @return VCS订单履行信息列表
     */
    private List<VcsOrderFufilmentDO> getVcsOrderList(List<String> carVinList, String branCode) {
        // 2.根据List<car_vin> 查询t_vcs_order_fufilment 得到 这个人买的 大品牌下的车相关的 所有已激活的vcs订单
        List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS = vcsOrderFufilmentDOMapper.selectList(
                new LambdaQueryWrapperX<VcsOrderFufilmentDO>()
                        .in(VcsOrderFufilmentDO::getCarVin, carVinList)
                        .eq(VcsOrderFufilmentDO::getBrandCode, branCode)
                        .eq(VcsOrderFufilmentDO::getServiceStatus, ServiceStatusEnum.ACTIVE.getCode())
                        .eq(BaseDO::getIsDeleted, false));
        // 查到的履约订单为空
        if (CollUtil.isEmpty(vcsOrderFufilmentDOS)) {
            log.info("已激活的履约订单为空, carVinList={}, brandCode={}", carVinList, branCode);
            return Collections.emptyList();
        }
        List<String> fulfilmentIds = vcsOrderFufilmentDOS.stream().map(VcsOrderFufilmentDO::getFufilmentId).collect(Collectors.toList());
        // 查询退单vcs表的订单
        List<VcsOrderRollbackDO> vcsOrderRollbackList = vcsOrderRollbackDOMapper.selectList(new LambdaQueryWrapperX<VcsOrderRollbackDO>()
                .in(VcsOrderRollbackDO::getFufilmentId, fulfilmentIds)
                .eq(BaseDO::getIsDeleted, false));
        // 排除履约订单中关闭激活中和关闭激活失败的订单，并设置实际服务结束时间
        if (CollUtil.isEmpty(vcsOrderRollbackList)) {
            return vcsOrderFufilmentDOS;
        }
        // 非关闭激活的订单
        List<String> unactivatedList = vcsOrderRollbackList.stream()
                .filter(rollbackDO -> !CancelServiceStatusEnum.ACTIVATE_OFF.getStatus().equals(rollbackDO.getServiceStatus()))
                .map(VcsOrderRollbackDO::getFufilmentId)
                .collect(Collectors.toList());
        return vcsOrderFufilmentDOS.stream()
                // 排除关闭激活中和关闭激活失败的订单
                .filter(vcsOrderFufilmentDO -> !unactivatedList.contains(vcsOrderFufilmentDO.getFufilmentId()))
                .collect(Collectors.toList());
    }

    /**
     * 根据车辆VIN列表，获取每辆车的履约服务到期时间。
     * 这里同时考虑了REMOTE和PIVI两种服务类型，确保了服务类型的区分和时间的准确性。
     *
     * @param carVinList 车辆VIN列表，用于查询服务时间。
     * @return 返回一个Map，其中VIN和服务类型通过字符串连接作为键，履约服务到期时间作为值。
     */
    private Map<String, LocalDateTime> getVinMapServiceDate(List<String> carVinList) {
        Map<String, LocalDateTime> vinMapDate = new HashMap<>();
        // 2.1 查询车辆对应的履约服务时间 REMOTE
        List<VinsAndServiceDateDTO> serviceDateList = findVinAndServiceDate(carVinList);
        // 2.1 查询车辆对应的履约服务时间 PIVI
        List<VinsAndServiceDateDTO> piviServiceDateList = findVinAndServiceDateInPIVI(carVinList);
        log.info("查询车辆对应的履约服务时间, piviServiceDateList={}", piviServiceDateList);
        // 组装两个服务类型的serviceDateList
        if (CollUtil.isNotEmpty(serviceDateList)) {
            Map<String, LocalDateTime> remoteVinMapDate = serviceDateList.stream().collect(Collectors.toMap(dto->dto.getVin() + CONCAT_SYMBOL + ServiceTypeEnum.REMOTE.getCode(), VinsAndServiceDateDTO::getServiceDate));
            vinMapDate.putAll(remoteVinMapDate);
        }
        if (CollUtil.isNotEmpty(piviServiceDateList)) {
            Map<String, LocalDateTime> piviVinMapDate = piviServiceDateList.stream().collect(Collectors.toMap(dto->dto.getVin() + CONCAT_SYMBOL + ServiceTypeEnum.PIVI.getCode(), VinsAndServiceDateDTO::getServiceDate));
            vinMapDate.putAll(piviVinMapDate);
        }
        return vinMapDate;
    }

    /**
     * 查询最新商品信息
     *
     * @param orderItemCodeList 订单item编码
     * @return List<OrderItemBaseVO>
     */
    private List<OrderItemBaseVO> getOrderItemList(List<String> orderItemCodeList){
        OrderItemCodeListDTO orderItemCodeListDTO = new OrderItemCodeListDTO();
        orderItemCodeListDTO.setOrderItemCodeList(orderItemCodeList);
        CommonResult<List<OrderItemBaseVO>> orderItemInfo = null;
        try {
            orderItemInfo = orderAppApi.getOrderItemInfo(orderItemCodeListDTO);
        } catch (Exception e) {
            log.error("调用orderAppApi查询最新商品信息异常,{}", e.getMessage());
        }
        if (orderItemInfo == null || orderItemInfo.getData() == null || orderItemInfo.getData().isEmpty()) {
            return Collections.emptyList();
        }
        return orderItemInfo.getData();
    }

    /**
     * 查询售后处理中的car_vin
     *
     * @param carVinToOrderCodeMap carVin映射orderCode
     * @return List<OrderItemBaseVO>
     */
    private Set<String> getAfterSalesCarVin(Map<String, String> carVinToOrderCodeMap){
        List<String> orderCodeList = carVinToOrderCodeMap.values().stream().distinct().collect(Collectors.toList());
        CommonResult<Set<String>> orderCodeResult = null;
        try {
            orderCodeResult = orderAppApi.getAfterSalesOrderCode(orderCodeList);
        } catch (Exception e) {
            log.error("调用orderAppApi查询售后处理中的car_vin异常,{}", e.getMessage());
        }
        if (orderCodeResult == null || CollUtil.isEmpty(orderCodeResult.getData())) {
            return Collections.emptySet();
        }
        // 哪些car_vin::serviceType对应的订单是售后处理中的
        Set<String> vinAndServiceTypeSet = new HashSet<>();
        Set<String> orderCodeSet = orderCodeResult.getData();
        for (Map.Entry<String, String> entry : carVinToOrderCodeMap.entrySet()) {
            String orderCode = entry.getValue();
            if(orderCodeSet.contains(orderCode)){
                vinAndServiceTypeSet.add(entry.getKey());
            }
        }
        return vinAndServiceTypeSet;
    }

    /**
     * 履约订单详情API 获取每个orderItemCode根据MaxId对应的最新履约记录
     *
     * @param orderItemCodeList 订单item编码
     * @return List <VcsOrderFulfilmentRespVO>
     */
    @Override
    public List<VcsOrderFulfilmentRespVO> getLatestByOrderItemCodeList(List<String> orderItemCodeList) {
        if (CollUtil.isEmpty(orderItemCodeList)) {
            return Collections.emptyList();
        }

        // 从数据库中查询每个订单项代码对应的最新履约记录
        List<VcsOrderFufilmentDO> vcsOrderFulfilments = vcsOrderFufilmentDOMapper.getLatestByOrderItemCodeList(orderItemCodeList);
        if (CollUtil.isEmpty(vcsOrderFulfilments)) {
            return Collections.emptyList();
        }

        List<VcsOrderFulfilmentRespVO> result = vcsOrderFulfilments.stream()
                .map(this::convertToVcsOrderFulfilmentRespVO)
                .collect(Collectors.toList());

        return result;
    }

    /**
     * 通过carvinList查出t_subscription_service t_vcs_order_fufilment t_vcs_order_rollback表中的数据
     * 1.服务过期时间大于当前时间+一年 2.正在激活中 激活关闭中的服务不可购买 3.已下单、已支付、售后处理中的不可购买
     *
     * @param carVinAndServiceTypeList
     * @return
     */
    @Override
    public Boolean checkCanBuyService(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList) {
        // 1.正在激活中 激活关闭中的服务数量
        List<CarServiceStatusVO> carServiceStatusVOList = vcsOrderFufilmentDOMapper.checkActiveServiceByCarVinAndServiceType(carVinAndServiceTypeList);
        for (CarServiceStatusVO carServiceStatusVO : carServiceStatusVOList) {
            if (carServiceStatusVO.getActiveCount() > 0 || carServiceStatusVO.getRollbackActiveCount() > 0) {
                log.info("存在正在激活中或激活关闭中的服务, carVin={}", carServiceStatusVO.getCarVin());
                return false;
            }
        }
        return true;
    }

    /**
     * 通过carvinList查出t_subscription_service t_vcs_order_fufilment t_vcs_order_rollback表中的数据
     * 1.服务过期时间大于当前时间+一年 2.正在激活中 激活关闭中的服务不可购买 3.已下单、已支付、售后处理中的不可购买
     *
     * @param carVinAndServiceTypeList
     * @return
     */
    @Override
    public Boolean checkCanBuyServiceV2(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList) {
        // 1.正在激活中 激活关闭中的服务数量
        if (checkOnTheWayService(carVinAndServiceTypeList)) {
            return false;
        }

        // 2.校验车辆是不是PIVI车机
        Set<String> carVinSet = carVinAndServiceTypeList.stream().map(CarVinAndServiceTypeDTO::getCarVin)
                .collect(Collectors.toSet());
        // 查询t_incontrol_vehicle的发票日期
        List<IncontrolVehicleDO> vehicleDOList = incontrolVehicleDOMapper.selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .in(IncontrolVehicleDO::getCarVin, carVinSet)
                .eq(BaseDO::getIsDeleted, false));
        //查询t_incontrol_vehicle是否存在
        if (checkVehicleExist(carVinSet, vehicleDOList)){
            return false;
        }
        //车辆不是PIVI车机
        if (checkVinPivi(carVinSet, vehicleDOList)){
            return false;
        }

        // 3.查询ECP是否存在VIN的初始服务数据，不存在无法购买
        carVinAndServiceTypeList.forEach(carVinAndServiceTypeDTO -> {
            // 查询service表，pivi type要转成3
            if(Constants.SERVICE_TYPE.NOT_REMOTE.equals(carVinAndServiceTypeDTO.getServiceType())){
                carVinAndServiceTypeDTO.setServiceType(Constants.SERVICE_TYPE.PIVI);
            }
        });
        List<SubscriptionServiceDO> serviceDOList = subscriptionServiceMapper.queryByCarVinAndServiceType(carVinAndServiceTypeList);
        if(CollUtil.isEmpty(serviceDOList)){
            log.info("初始服务数据为空, carVin={}", JSON.toJSONString(carVinAndServiceTypeList));
            return false;
        }
        Map<String, LocalDateTime> serviceMap = new HashMap<>();
        // 高德实际过期时间
        Map<String, LocalDateTime> amapExpireDateMap = new HashMap<>();
        Map<String, LocalDateTime> ecpExpireDateMap = new HashMap<>();
        // 检查初始化数据是否完整
        if (!checkInitData(carVinAndServiceTypeList, serviceDOList, serviceMap, amapExpireDateMap, ecpExpireDateMap)){
            return false;
        }

        // 4.当t_subscription_service的过期时间-t_incontrol_vehicle的发票日期>15年，这种情况不能下单
        if (!checkInvoiceDate(serviceMap, vehicleDOList)){
            return false;
        }

        // 5.如果购买的是PIVI服务，当AMAP实际过期时间<ECP到期日，且TODAY<ECP到期日，这种情况不能下单
        return checkCanBuyAMap(amapExpireDateMap, ecpExpireDateMap);
    }

    /**
     * 检查vin是否是PIVI车技
     * @param carVinSet
     * @param vehicleDOList
     * @return  boolean
     */
    private boolean checkVinPivi(Set<String> carVinSet, List<IncontrolVehicleDO> vehicleDOList) {
        Map<String, String> modelMap = vehicleDOList.stream()
                .filter(v -> Objects.nonNull(v.getCarSystemModel()))
                .collect(Collectors.toMap(IncontrolVehicleDO::getCarVin, IncontrolVehicleDO::getCarSystemModel, (v1, v2) -> v1));
        // 检查下单的vin和serviceType列表中，是否都是PIVI车机
        for(String carVin : carVinSet){
            String model = modelMap.get(carVin);
            if(!Objects.equals(CarSystemModelEnum.PIVI.getCode(), model)){
                log.warn("车辆不是PIVI车机, carVin={}", carVin);
                return true;
            }
        }
        return false;
    }

    /**
     * //查询t_incontrol_vehicle是否存在
     * @param carVinSet vin合集
     * @param vehicleDOList 结果集
     * @return boolean
     */
    private  boolean checkVehicleExist(Set<String> carVinSet, List<IncontrolVehicleDO> vehicleDOList) {
        if (CollUtil.isEmpty(vehicleDOList)) {
            log.warn("车辆信息不存在, carVin={}", carVinSet);
            return true;
        }
        return false;
    }

    /** 正在激活中 激活关闭中的服务数量 */
    private boolean checkOnTheWayService(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList) {
        // 1.正在激活中 激活关闭中的服务数量
        List<CarServiceStatusVO> carServiceStatusVOList = vcsOrderFufilmentDOMapper.checkActiveServiceByCarVinAndServiceType(carVinAndServiceTypeList);
        for (CarServiceStatusVO carServiceStatusVO : carServiceStatusVOList) {
            if (carServiceStatusVO.getActiveCount() > 0 || carServiceStatusVO.getRollbackActiveCount() > 0) {
                log.info("存在正在激活中或激活关闭中的服务, carVin={}", carServiceStatusVO.getCarVin());
                return true;
            }
        }
        return false;
    }

    /**
     * 检查初始化数据是否完整。
     * 该方法用于验证给定的车辆VIN和服务类型是否在订阅服务列表中存在对应的初始化数据。
     * 如果存在任意一个VIN和服务类型组合在serviceMap中不存在，则认为初始化数据不完整。
     *
     * @param carVinAndServiceTypeList 车辆VIN和服务类型的数据列表，用于检查初始化数据的完整性。
     * @param serviceDOList 订阅服务的数据列表，包含车辆VIN、服务类型和过期日期等信息。
     * @param serviceMap 用于存储订阅服务信息的映射表，键为VIN和服务类型的组合，值为服务的过期日期。
     * @return 如果存在初始化数据不完整的VIN和服务类型组合，则返回false；否则返回true。
     */
    private boolean checkInitData(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList, List<SubscriptionServiceDO> serviceDOList,
                                  Map<String, LocalDateTime> serviceMap, Map<String, LocalDateTime> amapExpireDateMap,
                                  Map<String, LocalDateTime> ecpExpireDateMap) {
        Map<String, List<SubscriptionServiceDO>> groupByVin = serviceDOList.stream().collect(Collectors.groupingBy(SubscriptionServiceDO::getCarVin));
        for(Map.Entry<String, List<SubscriptionServiceDO>> entry : groupByVin.entrySet()){
            // remote取任意一条数据
            SubscriptionServiceDO remoteService = entry.getValue().stream().filter(serviceDO -> Constants.SERVICE_TYPE.REMOTE.equals(serviceDO.getServiceType()))
                    .findFirst().orElse(null);
            if(Objects.nonNull(remoteService)){
                String key = entry.getKey() + CONCAT_SYMBOL + remoteService.getServiceType();
                serviceMap.putIfAbsent(key, remoteService.getExpiryDate());
            }
            // subscription取online-pack的数据
            SubscriptionServiceDO piviService = entry.getValue().stream().filter(serviceDO -> Constants.SERVICE_TYPE.PIVI.equals(serviceDO.getServiceType()) &&
                            ServicePackageEnum.ONLINE_PACK.getPackageName().equals(serviceDO.getServicePackage()))
                    .findFirst().orElse(null);
            // online-pack为空取UNICOM
            if(Objects.isNull(piviService)){
                piviService = entry.getValue().stream().filter(serviceDO -> Constants.SERVICE_TYPE.PIVI.equals(serviceDO.getServiceType()) &&
                                ServicePackageEnum.DATA_PLAN.getPackageName().equals(serviceDO.getServicePackage()))
                        .findFirst().orElse(null);
            }
            if(Objects.nonNull(piviService)){
                String key = entry.getKey() + CONCAT_SYMBOL + piviService.getServiceType();
                serviceMap.putIfAbsent(key, piviService.getExpiryDate());
                // 保存ecp的过期时间
                ecpExpireDateMap.putIfAbsent(entry.getKey(), piviService.getExpiryDate());
            }
            // 保存amap的实际过期时间
            SubscriptionServiceDO aMapService = entry.getValue().stream().filter(serviceDO -> Constants.SERVICE_TYPE.PIVI.equals(serviceDO.getServiceType()) &&
                            ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage()))
                    .findFirst().orElse(null);
            if(Objects.nonNull(aMapService)){
                amapExpireDateMap.putIfAbsent(entry.getKey(), aMapService.getExpiryDate());
            }
        }
        // remote取任意一条数据，subscription取online-pack的数据
/*        serviceDOList.forEach(serviceDO -> {
            String key = serviceDO.getCarVin() + CONCAT_SYMBOL + serviceDO.getServiceType();
            if(Constants.SERVICE_TYPE.REMOTE.equals(serviceDO.getServiceType())){
                serviceMap.putIfAbsent(key, serviceDO.getExpiryDate());
            }else if(Constants.SERVICE_TYPE.PIVI.equals(serviceDO.getServiceType()) &&
                    ServicePackageEnum.ONLINE_PACK.getPackageName().equals(serviceDO.getServicePackage())){
                serviceMap.putIfAbsent(key, serviceDO.getExpiryDate());
                // 保存ecp的过期时间
                ecpExpireDateMap.putIfAbsent(serviceDO.getCarVin(), serviceDO.getExpiryDate());
            }else if(Constants.SERVICE_TYPE.PIVI.equals(serviceDO.getServiceType()) &&
                    ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName().equals(serviceDO.getServicePackage())){
                // 保存amap的实际过期时间
                amapExpireDateMap.putIfAbsent(serviceDO.getCarVin(), serviceDO.getExpiryDate());
            }
        });*/
        // 检查下单的vin和serviceType列表中，是否都有初始化数据
        for(CarVinAndServiceTypeDTO dto : carVinAndServiceTypeList){
            // vin不存在不能购买
            if(!serviceMap.containsKey(dto.getCarVin() + CONCAT_SYMBOL + dto.getServiceType())){
                log.info("获取不到VIN的初始服务数据, carVin={}, serviceType={}", dto.getCarVin(), dto.getServiceType());
                return false;
            }
        }
        return true;
    }

    /**
     * 检查是否可以购买高德地图。
     *
     * @param amapExpireDateMap 高德服务信息。
     * @param ecpExpireDateMap ecp服务信息。
     * @return 如果可以购买高德地图返回true，否则返回false。
     */
    private boolean checkCanBuyAMap(Map<String, LocalDateTime> amapExpireDateMap,
                                    Map<String, LocalDateTime> ecpExpireDateMap) {
        if(CollUtil.isEmpty(amapExpireDateMap) || CollUtil.isEmpty(ecpExpireDateMap)){
            return true;
        }
        for(Map.Entry<String, LocalDateTime> entry : ecpExpireDateMap.entrySet()){
            String vin = entry.getKey();
            LocalDateTime ecpExpiryDate = entry.getValue();
            LocalDateTime aMapExpiryDate = amapExpireDateMap.get(vin);
            if(Objects.isNull(aMapExpiryDate)){
                log.warn("存在AMAP实际到期日为空, carVin={}", vin);
                continue;
            }
            if (aMapExpiryDate.toLocalDate().isBefore(ecpExpiryDate.toLocalDate()) &&
                    LocalDateTime.now().toLocalDate().isBefore(ecpExpiryDate.toLocalDate())) {
                log.warn("存在AMAP<ECP到期日，且TODAY<ECP到期日, carVin={}", vin);
                return false;
            }
        }
        return true;
    }

    /**
     * 检查是否可以购买服务
     * 对于给定的车辆订阅服务列表和车辆VIN集合，本方法检查是否存在车辆的发票日期与订阅服务的到期日期之差超过15年的情况。
     * 如果不存在超过15年的情况，则返回true；如果存在，则返回false，并记录警告日志。
     *
     * @param serviceMap 服务列表。
     * @return 如果所有车辆的发票日期与到期日期之差都不超过15年，则返回true；否则返回false。
     */
    private boolean checkInvoiceDate(Map<String, LocalDateTime> serviceMap, List<IncontrolVehicleDO> vehicleDOList) {
        Map<String, LocalDateTime> dmsInvoiceDateMap = vehicleDOList.stream()
                .filter(v -> Objects.nonNull(v.getDmsInvoiceDate()))
                .collect(Collectors.toMap(IncontrolVehicleDO::getCarVin, IncontrolVehicleDO::getDmsInvoiceDate, (k1, k2) -> k1));
        for (Map.Entry<String, LocalDateTime> entry: serviceMap.entrySet()) {
            String carVin = entry.getKey().split(CONCAT_SYMBOL)[0];
            LocalDateTime invoiceDate = dmsInvoiceDateMap.get(carVin);
            if (Objects.isNull(invoiceDate)) {
                log.warn("车辆发票信息不存在, carVin={}", carVin);
                return false;
            }
            LocalDateTime expiryDate = entry.getValue();
            // 车辆服务过期时间已经超出invoice date + 15 year
            if (expiryDate.isAfter(invoiceDate.plusYears(15))) {
                log.warn("存在过期时间-发票日期>15年, carVin={}, serviceType={}", carVin, entry.getKey().split(CONCAT_SYMBOL)[1]);
                return false;
            }
        }
        return true;
    }

    /**
     * 根据CarVin去查询正在激活中的履约
     * @param carvinList 车vin码
     * @return List<String>
     */
    @Override
    public List<String> getFulfilmentListByCarVinList(List<String> carvinList) {
        List<String> fulfilmentIdList = new ArrayList<>();
        List<VcsOrderFufilmentDO> vcsOrderFufilmentList = vcsOrderFufilmentDOMapper.selectList(new LambdaQueryWrapperX<VcsOrderFufilmentDO>()
                .in(VcsOrderFufilmentDO::getCarVin, carvinList)
                .eq(BaseDO::getIsDeleted, false)
//                .eq(VcsOrderFufilmentDO::getServiceStatus, FufilmentServiceStatusEnum.UNACTIVATED.getStatus())
                .select(VcsOrderFufilmentDO::getCarVin,
                        VcsOrderFufilmentDO::getFufilmentId,
                        VcsOrderFufilmentDO::getServiceStatus)
        );
        if (CollUtil.isEmpty(vcsOrderFufilmentList)){
            return fulfilmentIdList;
        }
        List<String> result = vcsOrderFufilmentList.stream()
                .filter(e->FufilmentServiceStatusEnum.UNACTIVATED.getStatus().equals(e.getServiceStatus()))
                .map(VcsOrderFufilmentDO::getFufilmentId).collect(Collectors.toList());
        //过滤到查询到有激活中的
        if (CollUtil.isNotEmpty(result)){
            fulfilmentIdList.addAll(result);
            return fulfilmentIdList;
        }
        List<String> fulfilmentIds = vcsOrderFufilmentList.stream().map(VcsOrderFufilmentDO::getFufilmentId).collect(Collectors.toList());
        //查询激活关闭中
        List<VcsOrderRollbackDO> vcsOrderRollbackList = vcsOrderRollbackDOMapper.selectList(new LambdaQueryWrapperX<VcsOrderRollbackDO>()
                .in(VcsOrderRollbackDO::getFufilmentId, fulfilmentIds)
                .eq(BaseDO::getIsDeleted, false)
                .eq(VcsOrderRollbackDO::getServiceStatus, CancelServiceStatusEnum.UNACTIVATED.getStatus()));

        if(CollUtil.isNotEmpty(vcsOrderRollbackList)){
            List<String> collect = vcsOrderRollbackList.stream().map(VcsOrderRollbackDO::getFufilmentId).collect(Collectors.toList());
            fulfilmentIdList.addAll(collect);
            return fulfilmentIdList;
        }
        return result;
    }

    @Override
    public List<VinsAndServiceDateDTO> findVinAndServiceDate(List<String> carvinList) {

        return vcsOrderFufilmentDOMapper.findVinAndServiceDate(carvinList);
    }

    private List<VinsAndServiceDateDTO> findVinAndServiceDateInPIVI(List<String> carvinList) {
        // 查询ECP PIVI过期时间，等同于查ONLINE_PACK的过期时间
        List<SubscriptionServiceDO> serviceDOList = subscriptionServiceMapper.selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                .in(SubscriptionServiceDO::getCarVin, carvinList)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI)
                .eq(BaseDO::getIsDeleted, false));
        if(CollUtil.isEmpty(serviceDOList)){
            return Collections.emptyList();
        }
        Map<String, List<SubscriptionServiceDO>> groupByVin = serviceDOList.stream().collect(Collectors.groupingBy(SubscriptionServiceDO::getCarVin));
        List<VinsAndServiceDateDTO> serviceDateDTOS = new ArrayList<>();
        for(Map.Entry<String, List<SubscriptionServiceDO>> entry : groupByVin.entrySet()){
            SubscriptionServiceDO subscriptionService = entry.getValue().stream().filter(serviceDO -> ServicePackageEnum.ONLINE_PACK.getPackageName().equals(serviceDO.getServicePackage()))
                    .findFirst().orElse(null);
            // APPD为空取UNICOM
            if(Objects.isNull(subscriptionService)){
                subscriptionService = entry.getValue().stream().filter(serviceDO -> ServicePackageEnum.DATA_PLAN.getPackageName().equals(serviceDO.getServicePackage()))
                        .findFirst().orElse(new SubscriptionServiceDO());
            }
            VinsAndServiceDateDTO dto = new VinsAndServiceDateDTO();
            dto.setVin(entry.getKey());
            dto.setServiceDate(subscriptionService.getExpiryDate());
            serviceDateDTOS.add(dto);
        }
        return serviceDateDTOS;
    }

    /**
     * 将VcsOrderFufilmentDO对象转换为VcsOrderFulfilmentRespVO对象。
     *
     * @param vcsOrderFufilmentDO 履约记录数据对象
     * @return 转换后的履约记录响应对象
     */
    private VcsOrderFulfilmentRespVO convertToVcsOrderFulfilmentRespVO(VcsOrderFufilmentDO vcsOrderFufilmentDO) {
        VcsOrderFulfilmentRespVO respVO = new VcsOrderFulfilmentRespVO();
        BeanUtils.copyProperties(vcsOrderFufilmentDO, respVO);
        respVO.setServiceStatusDesc(ServiceStatusEnum.getDescriptionByCode(vcsOrderFufilmentDO.getServiceStatus()));
        return respVO;
    }

    /**
     * 从每个车辆VIN的订单历史中提取最近的三条订阅记录。
     *
     * @param vcsOrderGroupedByCarVin 按车辆VIN分组的履约订单列表
     * @return 每个车辆VIN对应的最近三条订阅历史
     */
    public Map<String, List<SubDurationHistoryVO>> extractSubDurationHistory(Map<String, List<VcsOrderFufilmentDO>> vcsOrderGroupedByCarVin) {
        Map<String, List<SubDurationHistoryVO>> carVinToHistoryMap = new HashMap<>();

        for (Map.Entry<String, List<VcsOrderFufilmentDO>> entry : vcsOrderGroupedByCarVin.entrySet()) {
            String carVinAndServiceType = entry.getKey();
            List<VcsOrderFufilmentDO> orders = entry.getValue();

            // 按照ID降序排列订单
            List<VcsOrderFufilmentDO> mutableOrders = new ArrayList<>(orders);
            mutableOrders.sort(Comparator.comparingLong(VcsOrderFufilmentDO::getId).reversed());
            List<SubDurationHistoryVO> historyList = new ArrayList<>();
            for (int i = 0; i < mutableOrders.size() && historyList.size() < 3; i++) {
                VcsOrderFufilmentDO order = mutableOrders.get(i);
                SubDurationHistoryVO history = new SubDurationHistoryVO();
                history.setHistoryStartTime(order.getServiceBeginDate());
                history.setHistoryEndTime(order.getServiceEndDate());
                historyList.add(history);
            }

            carVinToHistoryMap.put(carVinAndServiceType, historyList);
        }

        return carVinToHistoryMap;
    }

    /**
     * 将车辆VIN映射到其最新的商品信息。
     *
     * @param carVinToOrderItemCodeMap 每个车辆VIN对应的最新订单的orderItemCode映射
     * @param latestProductList        从商品服务获取的最新商品信息列表
     * @return 每个VIN对应的最新商品信息映射
     */
    public Map<String, OrderItemBaseVO> mapCarVinToLatestProductInfo(Map<String, String> carVinToOrderItemCodeMap, List<OrderItemBaseVO> latestProductList) {
        Map<String, OrderItemBaseVO> carVinToProductInfoMap = new HashMap<>();

        // 构建一个orderItemCode到OrderItemBaseVO的映射
        Map<String, OrderItemBaseVO> orderItemCodeToProductInfoMap = latestProductList.stream()
                .collect(Collectors.toMap(OrderItemBaseVO::getOrderItemCode, Function.identity()));

        for (Map.Entry<String, String> entry : carVinToOrderItemCodeMap.entrySet()) {
            String carVin = entry.getKey();
            String orderItemCode = entry.getValue();
            OrderItemBaseVO productInfo = orderItemCodeToProductInfoMap.get(orderItemCode);
            if (productInfo != null) {
                carVinToProductInfoMap.put(carVin, productInfo);
            }
        }

        return carVinToProductInfoMap;
    }

    /**
     * 从按车辆VIN分组的VcsOrderFufilmentDO列表中映射每个VIN到其对应的最新订单的orderItemCode。
     *
     * @param vcsOrderGroupedByCarVin 按车辆VIN分组的VcsOrderFufilmentDO列表映射
     * @return 每个VIN对应的最新订单的orderItemCode映射
     */

    public Map<String, String> mapCarVinToLatestOrderItemCode(Map<String, List<VcsOrderFufilmentDO>> vcsOrderGroupedByCarVin) {
        Map<String, String> carVinToOrderItemCodeMap = new HashMap<>();

        for (Map.Entry<String, List<VcsOrderFufilmentDO>> entry : vcsOrderGroupedByCarVin.entrySet()) {
            String carVin = entry.getKey();
            List<VcsOrderFufilmentDO> orders = entry.getValue();
            if (!orders.isEmpty()) {
                // 按照ID降序排列订单
                List<VcsOrderFufilmentDO> mutableOrders = new ArrayList<>(orders);
                mutableOrders.sort(Comparator.comparingLong(VcsOrderFufilmentDO::getId).reversed());
                // 获取最新的订单的orderItemCode并添加到映射中
                VcsOrderFufilmentDO latestOrder = mutableOrders.get(0);
                carVinToOrderItemCodeMap.put(carVin, latestOrder.getOrderItemCode());
            }
        }
        return carVinToOrderItemCodeMap;
    }




    /**
     * 查询每个carVin::serviceType最新一个订单的数据
     *
     * @param vcsOrderGroupedByCarVin   Map<carVin::serviceType, List<VcsOrderFufilmentDO>>
     * @return
     */
    private Triple<List<String>, Map<String, String>, Map<String, String>> findLatestData(Map<String, List<VcsOrderFufilmentDO>> vcsOrderGroupedByCarVin) {
        // 商品订单code列表
        List<String> orderItemCodeList = new ArrayList<>();
        // carVin::serviceType对应的orderCode 集合
        Map<String, String> carVinToOrderCodeMap = new HashMap<>();
        // carVin::serviceType对应的orderItemCode 集合
        Map<String, String> carVinToOrderItemCodeMap = new HashMap<>();

        for (Map.Entry<String, List<VcsOrderFufilmentDO>> entry : vcsOrderGroupedByCarVin.entrySet()) {
            // 键名为carVin::serviceType
            String key = entry.getKey();
            List<VcsOrderFufilmentDO> orders = entry.getValue();
            if(CollUtil.isEmpty(orders)){
                continue;
            }
            // 取最新一条履约订单
            orders.sort(Comparator.comparingLong(VcsOrderFufilmentDO::getId).reversed());
            VcsOrderFufilmentDO latestOrder = orders.get(0);
            orderItemCodeList.add(latestOrder.getOrderItemCode());
            carVinToOrderCodeMap.put(key, latestOrder.getOrderCode());
            carVinToOrderItemCodeMap.put(key, latestOrder.getOrderItemCode());
        }
        return new ImmutableTriple<>(orderItemCodeList, carVinToOrderCodeMap, carVinToOrderItemCodeMap);
    }

    /**
     * 辅助方法：组装车型信息
     *
     * @param seriesInfo
     * @param carVin
     * @return
     */
    private SubVehicleInfoVO createSubVehicleInfoVO(String seriesInfo, String carVin, Integer serviceType, String vinAndServiceType, Set<String> afterSalesVinSet) {
        String[] seriesParts = seriesInfo.split(":");
        String seriesCode = seriesParts.length > 0 ? seriesParts[0] : "";
        String seriesName = seriesParts.length > 1 ? seriesParts[1] : "";

        SubVehicleInfoVO vehicleInfo = new SubVehicleInfoVO();
        vehicleInfo.setSeriesCode(seriesCode);
        vehicleInfo.setSeriesName(seriesName);
        vehicleInfo.setCarVin(carVin);
        // 判断是否在售后中
        if(CollUtil.isNotEmpty(afterSalesVinSet) && afterSalesVinSet.contains(vinAndServiceType)){
            vehicleInfo.setReNew(false);
            return vehicleInfo;
        }
        //车可续订表示
        CarVinAndServiceTypeDTO vinAndServiceTypeDTO = new CarVinAndServiceTypeDTO();
        vinAndServiceTypeDTO.setCarVin(carVin);
        vinAndServiceTypeDTO.setServiceType(serviceType);
        vehicleInfo.setReNew(checkCanBuyServiceV2(List.of(vinAndServiceTypeDTO)));
        return vehicleInfo;
    }

    /**
     * 辅助方法：提取所有的car_vin
     *
     * @param subBrandListDTO
     * @return
     */
    private List<String> extractCarVinList(SubBrandListDTO subBrandListDTO) {
        return subBrandListDTO.getBrandVehicles().values().stream()
                .flatMap(map -> map.keySet().stream())
                .collect(Collectors.toList());
    }


    /**
     * 辅助方法：按carVin分组VcsOrderFufilmentDO对象
     *
     * @param vcsOrderFufilmentDOS
     * @return
     */
    private Map<String, List<VcsOrderFufilmentDO>> groupVcsOrdersByCarVin(List<VcsOrderFufilmentDO> vcsOrderFufilmentDOS) {
        return vcsOrderFufilmentDOS.stream().collect(Collectors.groupingBy(vcsOrderFufilmentDO ->
                        vcsOrderFufilmentDO.getCarVin() + CONCAT_SYMBOL + vcsOrderFufilmentDO.getServiceType()));
    }


    @Override
    public void executeOrderStatusSync(String orderCode,String vcsOrderCode){
        FufilmentSuccessMessage fufilmentSuccessMessage = new FufilmentSuccessMessage();
        fufilmentSuccessMessage.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        fufilmentSuccessMessage.setTenantId(TenantContextHolder.getTenantId());
        fufilmentSuccessMessage.setVcsOrderCode(vcsOrderCode);
        Boolean updateStatus = checkSyncFulfilmentStatus(orderCode, FulfilTypeEnum.ACTIVE_SERVICE.getType());
        fufilmentSuccessMessage.setUpdateStatus(updateStatus);
        if(updateStatus){
            log.info("订单正向履约全部激活，发送消息, fufilmentSuccessMessage：{}", fufilmentSuccessMessage);
        }else {
            log.info("订单正向履约没有全部激活，orderCode:{} , vcsOrderCode: {}",orderCode,vcsOrderCode);
        }
        producerTool.sendMsg(KafkaConstants.ORDER_COMPLETE_STATUS_UPDATES_TOPIC, "", JSON.toJSONString(fufilmentSuccessMessage));
    }

    /**
     * 同步执行订单状态。
     * 此方法接收一个订单履行信息列表，并对列表中的每个订单履行信息调用
     *
     * @param vcsOrderFufilmentDOList 订单履行信息列表，包含需要同步状态的订单信息。
     */
    @Override
    public void executeOrderStatusSync(List<VcsOrderFufilmentDO> vcsOrderFufilmentDOList) {
        for (VcsOrderFufilmentDO vcsOrderFufilmentDO : vcsOrderFufilmentDOList) {
            executeOrderStatusSync(vcsOrderFufilmentDO.getOrderCode(), vcsOrderFufilmentDO.getVcsOrderCode());
        }
    }

    @Override
    public void executeRefundOrderStatusSync(String orderCode,String refundOrderCode,String fufilmentId){

        Boolean updateStatus = checkSyncFulfilmentStatus(orderCode, FulfilTypeEnum.ROLLBACK_SERVICE.getType());
        CancelSuccessMessage cancelSuccessMessage = new CancelSuccessMessage();
        cancelSuccessMessage.setMessageId(UUID.randomUUID().toString().replace("-", ""));
        cancelSuccessMessage.setTenantId(TenantContextHolder.getTenantId());
        cancelSuccessMessage.setRefundOrderCode(refundOrderCode);
        cancelSuccessMessage.setFufilmentId(fufilmentId);
        cancelSuccessMessage.setUpdateStatus(updateStatus);
        if(updateStatus){
            log.info("订单逆向履约全部关闭，发送消息, cancelSuccessMessage：{}", cancelSuccessMessage);
        }else {
            log.info("订单逆向履约没有全部关闭，orderCode:{} , refundOrderCode: {}",orderCode,refundOrderCode);
        }
//        orderAppApi.tsdpRefundCallBack(refundOrderCode);
        producerTool.sendMsg(KafkaConstants.ORDER_ROLLBACK_STATUS_UPDATES_TOPIC, "", JSON.toJSONString(cancelSuccessMessage));

    }

    @Override
    public Integer processTsdpFailedTasks(Integer upcastDays) {
        List<InterBusiErrorLogDO> tsdpErrorLogs = interBusiErrorLogMapper.selectList(
                new LambdaQueryWrapperX<InterBusiErrorLogDO>()
                .eq(InterBusiErrorLogDO::getIsDeleted, false)
                .eq(InterBusiErrorLogDO::getBusinessType, BusiErrorEnum.ORD_TSDP_FAIL.getCode())
                        .gtIfPresent(InterBusiErrorLogDO::getUpdatedTime, LocalDateTime.now().minusDays(upcastDays))
        );
        ArrayList<Integer> successList = new ArrayList<>();
        for (InterBusiErrorLogDO tsdpErrorLog : tsdpErrorLogs) {
            String businessParams = tsdpErrorLog.getBusinesParams();
            if (StrUtil.isNotBlank(businessParams)) {
                boolean b = callTSDPService(JSON.parseObject(businessParams, OrdFufilmentBusiDTO.class));
                if (!b) {
                    successList.add(tsdpErrorLog.getId());
                }
            } else {
                log.error("tsdpErrorLog单据{}记录到的参数为空", tsdpErrorLog.getBusinessId());
            }
        }
        // 清除错误日志
        if (CollUtil.isNotEmpty(successList)){
            interBusiErrorLogMapper.deleteBatchIds(successList);
        }
        return successList.size();
    }

    /**
     *
     * @param param
     * @return true 标识调用失败  false标识调用🆗
     * 别疑惑为了保持和履约消费那儿一致
     */
    private boolean callTSDPService(OrdFufilmentBusiDTO param) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Accept", "application/json");
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", ifEcomToken);
        HttpEntity<String> requestEntity = new HttpEntity<>(JSON.toJSONString(param), headers);
        ResponseEntity<JSONObject> response;
        try {
            response = restTemplate.postForEntity(subscriptionRenewUrl, requestEntity, JSONObject.class);
        } catch (RestClientException e) {
            log.error("调用TSDP续期服务出错：", e);
            return true;
        }
        if (!HttpStatus.OK.equals(response.getStatusCode())
                && !HttpStatus.CREATED.equals(response.getStatusCode())) {
            return true;
        }
        return false;
    }

    private Boolean checkSyncFulfilmentStatus(String orderCode,Integer type){
        //查询订单下所有服务的履约状态
        List<FulfilmentServiceStatusVO> allServiceStatus = vcsOrderFufilmentDOMapper.findAllServiceStatus(orderCode);

        if (CollUtil.isEmpty(allServiceStatus)){
            log.info("checkSyncFulfilmentStatus 方法查询到履约服务为空,orderCode为{}",orderCode);
            return false;
        }
        if(FulfilTypeEnum.ACTIVE_SERVICE.getType().equals(type)){
        //激活服务判断状态
            return allServiceStatus.stream().allMatch(vo->FufilmentServiceStatusEnum.ACTIVATED.getStatus().equals(vo.getVofServiceStatus()));
        }else {
            //判断关闭服务
            return allServiceStatus.stream().allMatch(vo->CancelServiceStatusEnum.ACTIVATE_OFF.getStatus().equals(vo.getVorServiceStatus()));
        }
    }




    @Override
    public CommonResult<Boolean> checkCanBuyServiceForPC(List<CarVinAndServiceTypeDTO> carVinAndServiceTypeList){
        // 1.正在激活中 激活关闭中的服务数量
        if (checkOnTheWayService(carVinAndServiceTypeList)) {
            return CommonResult.error(ErrorCodeConstants.SERVICE_IS_ACTIVE);
        }

        // 2.校验车辆是不是PIVI车机
        Set<String> carVinSet = carVinAndServiceTypeList.stream().map(CarVinAndServiceTypeDTO::getCarVin)
                .collect(Collectors.toSet());
        // 查询t_incontrol_vehicle的发票日期
        List<IncontrolVehicleDO> vehicleDOList = incontrolVehicleDOMapper.selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .in(IncontrolVehicleDO::getCarVin, carVinSet)
                .eq(BaseDO::getIsDeleted, false));
        //查询t_incontrol_vehicle是否存在
        if (checkVehicleExist(carVinSet, vehicleDOList)){
            return CommonResult.error(ErrorCodeConstants.VEHICLE_NOT_EXIST);
        }
        //车辆不是PIVI车机
        if (checkVinPivi(carVinSet, vehicleDOList)){
            return CommonResult.error(ErrorCodeConstants.VEHICLE_NOT_PIVI);
        }

        // 3.查询ECP是否存在VIN的初始服务数据，不存在无法购买
        Set<String> carVinForPIVI = new HashSet<>();
        carVinAndServiceTypeList.forEach(carVinAndServiceTypeDTO -> {
            // 查询service表，pivi type要转成3
            if(Constants.SERVICE_TYPE.NOT_REMOTE.equals(carVinAndServiceTypeDTO.getServiceType())){
                carVinAndServiceTypeDTO.setServiceType(Constants.SERVICE_TYPE.PIVI);
                carVinForPIVI.add(carVinAndServiceTypeDTO.getCarVin());
            }
        });
        log.info("买了PIVI服务的车机的vin集合为:{}",carVinForPIVI);
        CommonResult<Boolean> RNR_CHECK_ERROR = rnrCheck(carVinForPIVI);
        if (RNR_CHECK_ERROR != null) return RNR_CHECK_ERROR;

        List<SubscriptionServiceDO> serviceDOList = subscriptionServiceMapper.queryByCarVinAndServiceType(carVinAndServiceTypeList);
        if(CollUtil.isEmpty(serviceDOList)){
            log.info("初始服务数据为空, carVin={}", JSON.toJSONString(carVinAndServiceTypeList));
        }
        Map<String, LocalDateTime> serviceMap = new HashMap<>();
        // 高德实际过期时间
        Map<String, LocalDateTime> amapExpireDateMap = new HashMap<>();
        Map<String, LocalDateTime> ecpExpireDateMap = new HashMap<>();
        // 检查初始化数据是否完整
        if (!checkInitData(carVinAndServiceTypeList, serviceDOList, serviceMap, amapExpireDateMap, ecpExpireDateMap)){
            return CommonResult.error(ErrorCodeConstants.INIT_DATA_ERROR);
        }

        // 4.当t_subscription_service的过期时间-t_incontrol_vehicle的发票日期>15年，这种情况不能下单
        Map<String, LocalDateTime> dmsInvoiceDateMap = vehicleDOList.stream()
                .filter(v -> Objects.nonNull(v.getDmsInvoiceDate()))
                .collect(Collectors.toMap(IncontrolVehicleDO::getCarVin, IncontrolVehicleDO::getDmsInvoiceDate, (k1, k2) -> k1));
        for (Map.Entry<String, LocalDateTime> entry: serviceMap.entrySet()) {
            String carVin = entry.getKey().split(CONCAT_SYMBOL)[0];
            LocalDateTime invoiceDate = dmsInvoiceDateMap.get(carVin);
            if (Objects.isNull(invoiceDate)) {
                log.warn("车辆发票信息不存在, carVin={}", carVin);
                return CommonResult.error(ErrorCodeConstants.INVOICE_DATE_IS_NULL);
            }
            LocalDateTime expiryDate = entry.getValue();
            // 车辆服务过期时间已经超出invoice date + 15 year
            if (expiryDate.isAfter(invoiceDate.plusYears(15))) {
                log.warn("存在过期时间-发票日期>15年, carVin={}, serviceType={}", carVin, entry.getKey().split(CONCAT_SYMBOL)[1]);
                return CommonResult.error(ErrorCodeConstants.INVOICE_EXPIRE_DATE_ERROR);
            }
        }
        // 5.如果购买的是PIVI服务，当AMAP实际过期时间<ECP到期日，且TODAY<ECP到期日，这种情况不能下单
        if(!checkCanBuyAMap(amapExpireDateMap, ecpExpireDateMap)){
            return CommonResult.error(ErrorCodeConstants.AMAP_EXPIRE_DATE_ERROR);
        }
        return CommonResult.success(true);
    }

    @Override
    public Boolean checkStatus(String orderCode) {
        // 查询履约信息
        List<VcsOrderFufilmentDO> fufilmentDOList = vcsOrderFufilmentDOMapper.selectList(new LambdaQueryWrapperX<VcsOrderFufilmentDO>()
                .eq(VcsOrderFufilmentDO::getOrderCode, orderCode)
                .eq(BaseDO::getIsDeleted, false));
        if (CollUtil.isEmpty(fufilmentDOList)) {
            return false;
        }
        // 存在激活失败的履约直接返回true
        if (fufilmentDOList.stream().anyMatch(vo -> FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getStatus().equals(vo.getServiceStatus()))) {
            log.info("存在激活失败的履约, orderCode={}", orderCode);
            return true;
        }
        return false;
    }

    @Override
    public void updateServiceStatusFail(String fufilmentId) {
        vcsOrderFufilmentDOMapper.update(null, new LambdaUpdateWrapper<VcsOrderFufilmentDO>()
                .eq(VcsOrderFufilmentDO::getFufilmentId, fufilmentId)
                .set(VcsOrderFufilmentDO::getServiceStatus, FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getStatus()));
    }

    @Override
    public Set<String> getFailOrders() {
        // 查询正向履约表中服务状态为激活失败的履约单
        List<VcsOrderFufilmentDO> fufilmentDOList = vcsOrderFufilmentDOMapper.selectList(new LambdaQueryWrapperX<VcsOrderFufilmentDO>()
                .eq(VcsOrderFufilmentDO::getServiceStatus, FufilmentServiceStatusEnum.ACTIVATE_FAILURE.getStatus())
                .eq(BaseDO::getIsDeleted, false));
        if (CollUtil.isEmpty(fufilmentDOList)) {
            return Collections.emptySet();
        }
        // 履约单号在逆向履约表中存在，需要排除
        List<String> rollbackFufilmentIdList = vcsOrderRollbackDOMapper.selectList(new LambdaQueryWrapperX<VcsOrderRollbackDO>()
                        .in(VcsOrderRollbackDO::getFufilmentId, fufilmentDOList.stream().map(VcsOrderFufilmentDO::getFufilmentId).collect(Collectors.toList()))
                        .eq(BaseDO::getIsDeleted, false))
                .stream().map(VcsOrderRollbackDO::getFufilmentId).collect(Collectors.toList());
        // 从fufilmentDOList中移除rollbackFufilmentIdList
        fufilmentDOList.removeIf(vo -> rollbackFufilmentIdList.contains(vo.getFufilmentId()));
        // 返回订单号
        return fufilmentDOList.stream().map(VcsOrderFufilmentDO::getOrderCode).collect(Collectors.toSet());
    }

    @Nullable
    private CommonResult<Boolean> rnrCheck(Set<String> carVinForPIVI) {
        //循环carVinForPIVI 然后进行RNR实名校验
        for (String carVin : carVinForPIVI) {
            try {
                EsimInfoVO esimInfoVO = piviUnicomService.checkVinRNRInfo(carVin);
                if(UnicomRealnameFlagEnum.FALSE.getCode().equals(esimInfoVO.getRealnameFlag())){
                    return CommonResult.error(ErrorCodeConstants.RNR_CHECK_ERROR);
                }
            }catch (Exception e){
                log.info("实名校验接口调用出错：{},carvin:{}",e.getMessage(),carVin);
            }

        }
        return null;
    }

    /**
     * 根据车辆VIN列表获取品牌名称到系列的映射关系
     *
     * @param carVinList 车辆VIN码列表
     * @param clientId 客户端ID
     * @return 返回品牌名称到系列信息的映射关系，格式为Map<品牌名称, Map<VIN, 系列信息>>
     */
    private Map<String, Map<String, String>> getBrandNameToSeriesMapping(List<String> carVinList, String clientId) {
        // 获取车辆信息和系列映射
        List<IncontrolVehicleDO> vehicleDOS = incontrolVehicleRepository.selectByCarVinListAndBrandCode(carVinList, clientId);
        Set<Object> seriesCodeSet = vehicleDOS.stream()
                .map(IncontrolVehicleDO::getSeriesCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if(CollUtil.isEmpty(seriesCodeSet)){
            return Collections.emptyMap();
        }

        List<String> multiCacheMapValue = redisService.getMultiCacheMapValue(Constants.REDIS_KEY.SERIES_CACHE_KEY, seriesCodeSet);
        Map<String, SeriesMappingVO> seriesMapping = multiCacheMapValue.stream()
                .map(s -> JSON.parseObject(s, SeriesMappingVO.class))
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(SeriesMappingVO::getSeriesCode, Function.identity(), (v1, v2) -> v1));

        // 构建vin到系列信息的映射
        return vehicleDOS.stream()
                .filter(vehicle -> isValidVehicleSeries(vehicle, seriesMapping))
                .collect(Collectors.groupingBy(
                        vehicle -> getBrandName(vehicle, seriesMapping),
                        Collectors.toMap(
                                IncontrolVehicleDO::getCarVin,
                                vehicle -> buildSeriesInfo(vehicle, seriesMapping)
                        )
                ));
    }

    /**
     * 验证车辆系列信息是否有效
     *
     * @param vehicle 车辆信息对象，包含系列代码等信息
     * @param seriesMapping 系列映射信息集合，key为系列代码，value为系列映射对象
     * @return true-车辆系列信息有效，false-车辆系列信息无效
     */
    private boolean isValidVehicleSeries(IncontrolVehicleDO vehicle, Map<String, SeriesMappingVO> seriesMapping) {
        SeriesMappingVO mapping = seriesMapping.get(vehicle.getSeriesCode());
        return mapping != null && mapping.getSeriesName() != null &&
                !"默认系列名称".equals(mapping.getSeriesName());
    }

    /**
     * 获取车辆品牌名称
     *
     * @param vehicle 车辆信息对象，用于获取车系代码
     * @param seriesMapping 车系映射关系Map，key为车系代码，value为车系映射信息
     * @return 返回对应车系的品牌名称视图
     */
    private String getBrandName(IncontrolVehicleDO vehicle, Map<String, SeriesMappingVO> seriesMapping) {
        return seriesMapping.get(vehicle.getSeriesCode()).getBrandNameView();
    }

    /**
     * 构建车辆系列信息字符串
     *
     * @param vehicle 车辆信息对象，包含系列代码等信息
     * @param seriesMapping 系列映射关系Map，key为系列代码，value为系列映射对象
     * @return 返回格式为"系列代码:系列名称"的字符串
     */
    private String buildSeriesInfo(IncontrolVehicleDO vehicle, Map<String, SeriesMappingVO> seriesMapping) {
        SeriesMappingVO mapping = seriesMapping.get(vehicle.getSeriesCode());
        return vehicle.getSeriesCode() + ":" + mapping.getSeriesName();
    }
}




