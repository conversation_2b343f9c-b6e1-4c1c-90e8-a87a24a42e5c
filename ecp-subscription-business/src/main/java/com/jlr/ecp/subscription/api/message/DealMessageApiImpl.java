package com.jlr.ecp.subscription.api.message;

import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.subscription.dal.dataobject.vehicle.VehicleBindMessageDO;
import com.jlr.ecp.subscription.dal.repository.VehicleBindMessageRepository;
import com.jlr.ecp.subscription.enums.message.MessageStatusEnum;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 车辆初始化任务实现类（重构版）
 *
 */
@RestController
@Validated
@Slf4j
public class DealMessageApiImpl implements DealMessageApi {


    @Resource
    private VinInitializeService vinInitializeService;

    @Resource
    private VehicleBindMessageRepository vehicleBindMessageRepository;

    @Override
    public CommonResult<List<Long>> getPreDealIdList(Integer status) {
        log.info("查询待处理消息id列表， status={}",status);
        // 1. 分页查询原始数据
        List<VehicleBindMessageDO> list = vehicleBindMessageRepository.getPreDealIdList(status);
        List<Long> idList = list.stream().map(VehicleBindMessageDO::getId).collect(Collectors.toList());
        return CommonResult.success(idList);
    }

    @Override
    public CommonResult<Boolean> dealMessage(Long id) {
        VehicleBindMessageDO messageDO = vehicleBindMessageRepository.getById(id);
        try {
            vinInitializeService.dealWithLocalMessage(messageDO);
            log.info("处理本地MQC消息成功，vin={}", messageDO.getCarVin());
            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("重试MQC车辆绑定初始化失败", e);
            messageDO.setMessageStatus(MessageStatusEnum.CONSUMED_ERROR.getType());
            messageDO.setRetryTimes(messageDO.getRetryTimes() + 1);
            messageDO.setUpdatedTime(LocalDateTime.now());
            vehicleBindMessageRepository.updateById(messageDO);
            return CommonResult.success(false);
        }
    }
}
