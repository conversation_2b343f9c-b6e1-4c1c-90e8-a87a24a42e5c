package com.jlr.ecp.subscription.controller.app.icrorder.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class ValidateICRDTO {
    /**
     *  carVinList
     * */
    private List<String> carVinList;

    /**
     *  inControlId
     * */
    private String inControlId;

    /**
     *  jlrId
     * */
    private String jlrId;
}
