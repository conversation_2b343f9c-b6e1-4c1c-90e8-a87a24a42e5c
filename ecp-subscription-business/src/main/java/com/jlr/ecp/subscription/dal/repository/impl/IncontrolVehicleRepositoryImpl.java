package com.jlr.ecp.subscription.dal.repository.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.incontrol.dto.InControlConsumerVehicleDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleDTO;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.repository.IncontrolVehicleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertList;

/**
 * 车辆信息Repository实现类
 *
 */
@Component
@Slf4j
public class IncontrolVehicleRepositoryImpl extends ServiceImpl<IncontrolVehicleDOMapper, IncontrolVehicleDO> implements IncontrolVehicleRepository {

    @Override
    public int deleteVehicleByIncontrol(String incontrolId) {
        log.info("根据InControl ID删除车辆, incontrolId: {}", incontrolId);
        return baseMapper.deleteVehicleByIncontrol(incontrolId);
    }

    @Override
    public List<IncontrolVehicleDO> getBindIncontrolVehicle(String consumerCode, String brandCode) {
        log.info("获取绑定的InControl车辆, consumerCode: {}, brandCode: {}", consumerCode, brandCode);
        return baseMapper.getBindIncontrolVehicle(consumerCode, brandCode);
    }

    @Override
    public List<IncontrolVehicleByCarDTO> selectIncontrolVehicleByCarVinList(List<String> carVinList) {
        log.info("根据车辆VIN列表查询InControl车辆, carVinList size: {}", carVinList.size());
        return baseMapper.selectIncontrolVehicleByCarVinList(carVinList);
    }

    @Override
    public List<Map<String, String>> getPhoneEncryptsByCarVinList(List<String> carVinList) {
        log.info("根据车辆VIN列表获取手机号加密信息, carVinList size: {}", carVinList.size());
        return baseMapper.getPhoneEncryptsByCarVinList(carVinList);
    }

    @Override
    public int deleteVehicleByCarVinList(List<String> carVinList) {
        log.info("根据车辆VIN列表删除车辆, carVinList size: {}", carVinList.size());
        return baseMapper.deleteVehicleByCarVinList(carVinList);
    }

    @Override
    public IncontrolVehicleDO selectOneByCarVin(String carVin) {
        log.info("根据车辆VIN查询单个车辆, carVin: {}", carVin);
        return getOne(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .eq(IncontrolVehicleDO::getCarVin, carVin)
                .eq(IncontrolVehicleDO::getIsDeleted, false)
                .orderByDesc(IncontrolVehicleDO::getId)
                .last(Constants.LIMIT_ONE));
    }

    @Override
    public List<IncontrolVehicleDO> selectListByICR(String icr) {
        log.info("根据ICR查询车辆列表, icr: {}", icr);
        return list(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .eq(IncontrolVehicleDO::getIncontrolId, icr)
                .eq(IncontrolVehicleDO::getIsDeleted, false)
                .orderByDesc(IncontrolVehicleDO::getId));
    }

    @Override
    public boolean insertBatch(List<IncontrolVehicleDO> vehicleList) {
        log.info("批量插入车辆, size: {}", vehicleList.size());
        try {
            saveBatch(vehicleList);
        } catch (Exception e) {
            log.info("批量插入车辆, 异常:", e);
            return false;
        }
        return true;
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<IncontrolVehicleDO> entities) {
        log.info("批量保存或更新车辆, size: {}", entities.size());
        return super.saveOrUpdateBatch(entities);
    }

    @Override
    public int insert(IncontrolVehicleDO vehicleDO) {
        log.info("插入单个车辆, carVin: {}", vehicleDO.getCarVin());
        return baseMapper.insert(vehicleDO);
    }

    @Override
    public List<IncontrolVehicleDO> selectByCarVinList(List<String> carVinList) {
        if (CollUtil.isEmpty(carVinList)) {
            log.info("根据车辆VIN列表查询车辆信息, carVinList为空");
            return new ArrayList<>();
        }
        LambdaQueryWrapperX<IncontrolVehicleDO> queryWrapper = new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .in(IncontrolVehicleDO::getCarVin, carVinList)
                .eq(IncontrolVehicleDO::getIsDeleted, false);
        List<IncontrolVehicleDO> res = list(queryWrapper);
        if (CollUtil.isEmpty(res)) {
            log.info("根据车辆VIN列表查询车辆信息, 查询结果为空, carVinList数量:{}", CollUtil.size(carVinList));
            return res;
        }
        log.info("根据车辆VIN列表查询车辆信息, carVinList数量: {}, res数量：{}", CollUtil.size(carVinList), CollUtil.size(res));
        return res;
    }

    @Override
    public Map<String, IncontrolVehicleDO> queryIncontrolVehicleDOMap(List<String> carVinList) {
        log.info("根据车辆VIN列表查询车辆Map, carVinList数量: {}", CollUtil.size(carVinList));
        List<IncontrolVehicleDO> incontrolVehicleDOS = selectByCarVinList(carVinList);
        if (CollUtil.isEmpty(incontrolVehicleDOS)) {
            log.info("根据车辆VIN列表查询车辆Map, 查询结果为空, carVinList数量: {}", CollUtil.size(carVinList));
            return new HashMap<>();
        }
        return incontrolVehicleDOS.stream()
                .filter(incontrolVehicleDO -> incontrolVehicleDO.getCarVin() != null)
                .collect(Collectors.toMap(IncontrolVehicleDO::getCarVin, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public void updateBatchWithNulls(List<IncontrolVehicleDO> list) {
        if (CollUtil.isNotEmpty(list)) {
            baseMapper.updateBatchWithNulls(list);
        }
    }

    @Override
    public List<IncontrolVehicleDO> findByVinList(List<String> carVinList) {
        if (CollUtil.isEmpty(carVinList)) {
            log.info("VIN集合为空，返回空列表");
            return Collections.emptyList();
        }
        log.info("根据VIN集合查询车辆信息, vinCollection的size: {}", carVinList.size());
        return list(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .in(IncontrolVehicleDO::getCarVin, carVinList)
                .eq(IncontrolVehicleDO::getIsDeleted, false));
    }

    @Override
    public Map<String, IncontrolVehicleDO> getMapByVinList(List<String> caVinList) {
        List<IncontrolVehicleDO> vehicleDOList = findByVinList(caVinList);
        if (CollUtil.isEmpty(vehicleDOList)) {
            return new HashMap<>();
        }
        log.info("获取车辆数据Map, caVinList数量:{}, vehicleDOList数量:{}",
                CollUtil.size(caVinList), CollUtil.size(vehicleDOList));
        return vehicleDOList.stream()
                .collect(Collectors.toMap(IncontrolVehicleDO::getCarVin, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public List<IncontrolVehicleDO> selectByCarVinListAndBrandCode(List<String> carVinList, String brandCode) {
        return list(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .in(IncontrolVehicleDO::getCarVin, carVinList)
                .eq(IncontrolVehicleDO::getBrandCode, brandCode)
                .eq(IncontrolVehicleDO::getIsDeleted, false));
    }

    @Override
    public List<IncontrolVehicleDO> getBindByJlrIdAndVinList(String jlrId, List<String> carVinList) {
        if (CollUtil.isEmpty(carVinList)) {
            return Collections.emptyList();
        }
        return baseMapper.getBindByJlrIdAndVinList(jlrId, carVinList);
    }




    @Override
    public List<IncontrolVehicleDTO> getIncontrolVehicleByConsumerCode(String consumerCode, String brandCode) {
        if (StrUtil.isBlank(consumerCode)) {
            return Collections.emptyList();
        }
        List<IncontrolVehicleDO> bindIncontrolVehicles = baseMapper.getBindIncontrolVehicle(consumerCode, brandCode);
        return convertList(bindIncontrolVehicles, e -> BeanUtil.copyProperties(e, IncontrolVehicleDTO.class));
    }

    @Override
    public List<InControlConsumerVehicleDTO> getIncontrolVehicleByConsumerCodes(List<String> consumerCodes, String brandCode) {
        if (CollUtil.isEmpty(consumerCodes)) {
            return Collections.emptyList();
        }
        return  baseMapper.getBindIncontrolVehicleByConsumerCodes(consumerCodes, brandCode);
    }
}
