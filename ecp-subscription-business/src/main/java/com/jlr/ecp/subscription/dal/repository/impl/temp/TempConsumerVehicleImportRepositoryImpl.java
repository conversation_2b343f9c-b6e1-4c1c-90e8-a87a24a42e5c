package com.jlr.ecp.subscription.dal.repository.impl.temp;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.temp.dto.ImportProcessResultDTO;
import com.jlr.ecp.subscription.dal.dataobject.temp.TempConsumerVehicleImportDO;
import com.jlr.ecp.subscription.dal.mysql.temp.TempConsumerVehicleImportDOMapper;
import com.jlr.ecp.subscription.dal.repository.temp.TempConsumerVehicleImportRepository;
import com.jlr.ecp.subscription.enums.temp.VinBindImportTempStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class TempConsumerVehicleImportRepositoryImpl
        extends ServiceImpl<TempConsumerVehicleImportDOMapper, TempConsumerVehicleImportDO>
        implements TempConsumerVehicleImportRepository {

    /**
     * 根据状态获取vin绑定临时表的ID列表
     *
     * @param status 状态值，如果为null则返回空列表
     * @return 符合条件的记录ID列表
     */
    @Override
    public List<TempConsumerVehicleImportDO> getImportListByStatus(Integer status) {
        if (Objects.isNull(status)) {
            log.info("根据状态获取vin绑定临时表的ID列表, status为空");
            return new ArrayList<>();
        }
        LambdaQueryWrapperX<TempConsumerVehicleImportDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(TempConsumerVehicleImportDO::getIsDeleted, false)
                .eq(TempConsumerVehicleImportDO::getImportStatus, status);
        List<TempConsumerVehicleImportDO> importDOList = list(queryWrapper);
        if (CollUtil.isEmpty(importDOList)) {
            log.info("根据状态获取vin绑定临时表的ID列表, 查询结果为空, status: {}", status);
            return new ArrayList<>();
        }
        log.info("根据状态获取vin绑定临时表的ID列表, status:{}, 数量: {}", status, CollUtil.size(importDOList));
        return importDOList;
    }

    /**
     * 根据ID列表查询临时消费者车辆导入数据
     *
     * @param idList ID列表
     * @return 临时消费者车辆导入数据列表，如果查询结果为空则返回空列表
     */
    @Override
    public List<TempConsumerVehicleImportDO> selectByIdList(List<Long> idList) {
        log.info("根据ID列表查询临时消费者车辆导入数据, idList数量: {}", CollUtil.size(idList));
        if (CollUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        List<TempConsumerVehicleImportDO> importDOList = listByIds(idList);
        if (CollUtil.isEmpty(importDOList)) {
            log.info("根据ID列表查询临时消费者车辆导入数据, 查询结果为空, idList: {}", idList);
            return new ArrayList<>();
        }
        return importDOList;
    }

    /**
     * 根据导入处理结果DTO列表更新状态
     *
     * @param importDTOList 导入处理结果DTO列表，包含需要更新状态的数据
     * @return boolean 更新成功返回true，失败返回false
     */
    @Override
    public boolean updateStatusByIdList(List<ImportProcessResultDTO> importDTOList) {
        log.info("根据导入处理结果DTO列表更新状态, 数量：{}", CollUtil.size(importDTOList));
        if (CollUtil.isEmpty(importDTOList)) {
            return false;
        }
        List<Long> idList = importDTOList.stream().map(ImportProcessResultDTO::getId).collect(Collectors.toList());
        Map<Long, ImportProcessResultDTO> idToResultMap = importDTOList.stream()
                .collect(Collectors.toMap(ImportProcessResultDTO::getId, Function.identity(), (v1, v2)->v1));
        List<TempConsumerVehicleImportDO> updateList = selectByIdList(idList);
        for (TempConsumerVehicleImportDO importDO : updateList) {
            ImportProcessResultDTO importResultDTO = idToResultMap.get(importDO.getId());
            if (Objects.isNull(importResultDTO)) {
                log.info("根据导入处理结果DTO列表更新状态, importResultDTO为空, importId:{}", importDO.getId());
                continue;
            }
            importDO.setImportStatus(importResultDTO.getStatus());
            if (!VinBindImportTempStatusEnum.SUCCESS.getStatus().equals(importResultDTO.getStatus())) {
                importDO.setErrorMessage(importResultDTO.getFailReason());
            } else {
                importDO.setErrorMessage("");
            }
            importDO.setUpdatedTime(LocalDateTime.now());
        }
        return updateBatchById(updateList);
    }

    /**
     * 根据导入状态查询车辆信息
     *
     * @param importStatus 导入状态标识
     * @return 返回符合指定导入状态的临时消费者车辆导入数据列表
     */
    @Override
    public List<TempConsumerVehicleImportDO> queryInVehicleByStatus(Integer importStatus) {
        log.info("根据导入状态查询车辆信息， importStatus：{}", importStatus);
        return baseMapper.selectExistsInVehicle(importStatus);
    }

    /**
     * 根据导入状态查询未在车辆表中存在的消费者车辆导入记录
     *
     * @param importStatus 导入状态标识，用于筛选特定状态的导入记录
     * @return 返回未在车辆表中存在的消费者车辆导入记录列表
     */
    @Override
    public List<TempConsumerVehicleImportDO> queryNotInVehicleByStatus(Integer importStatus) {
        log.info("根据导入状态查询未在车辆表中存在的消费者车辆导入记录, importStatus:{}", importStatus);
        return baseMapper.selectNotExistsInVehicle(importStatus);
    }
}
