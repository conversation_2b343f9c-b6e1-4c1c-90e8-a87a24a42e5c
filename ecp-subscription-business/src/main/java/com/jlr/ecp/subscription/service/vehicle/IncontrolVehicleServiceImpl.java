package com.jlr.ecp.subscription.service.vehicle;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.jlr.ecp.framework.mybatis.core.dataobject.BaseDO;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleDTO;
import com.jlr.ecp.subscription.api.model.vo.ServiceDetailVO;
import com.jlr.ecp.subscription.api.model.vo.UserCarServiceListVO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.subscripiton.dto.ServicePackageDTO;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.constant.Constants;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.SeriesBrandMappingDataDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.VehicleDmsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.RemotePackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.dal.repository.ConsumerVehicleRepository;
import com.jlr.ecp.subscription.dal.repository.IncontrolVehicleRepository;
import com.jlr.ecp.subscription.enums.appd.AppDRenewStatusEnum;
import com.jlr.ecp.subscription.enums.appd.RenewServiceTypeEnum;
import com.jlr.ecp.subscription.enums.fufil.AppDServiceNameEnum;
import com.jlr.ecp.subscription.enums.fufil.ServiceNameEnum;
import com.jlr.ecp.subscription.enums.fufil.ServicePackageEnum;
import com.jlr.ecp.subscription.enums.model.CarSystemModelEnum;
import com.jlr.ecp.subscription.enums.redis.RedisDelayQueueEnum;
import com.jlr.ecp.subscription.enums.subscribeservice.ServiceTypeEnum;
import com.jlr.ecp.subscription.service.icrorder.SeriesBrandMappingDataDOService;
import com.jlr.ecp.subscription.service.incontrol.IncontrolCustomerService;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.pivi.PIVIAmaPService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.remotepackage.RemotePackageDOService;
import com.jlr.ecp.subscription.util.TimeFormatUtil;
import com.jlr.ecp.subscription.util.redis.RedisDelayQueueUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.jlr.ecp.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.jlr.ecp.framework.common.util.collection.CollectionUtils.convertList;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.TSDP_REQUEST_FORBIDDEN;
import static com.jlr.ecp.subscription.enums.ErrorCodeConstants.TSDP_REQUEST_RATE_TOO_FAST;

/**
 * t_incontrol_vehicle服务接口实现
 *
 * <AUTHOR>
 * @description 由 Mybatisplus Code Generator 创建
 * @since 2023-12-20 14:45:58
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IncontrolVehicleServiceImpl implements IncontrolVehicleService {
    private final IncontrolVehicleDOMapper incontrolVehicleMapper;
    private final VehicleModelMasterDataService vehicleModelMasterDataService;
    private final IncontrolCustomerService incontrolCustomerService;
    private final RestTemplate restTemplate;
    private final SubscriptionServiceMapper subscriptionServiceMapper;
    private final RemotePackageDOMapper remotePackageDOMapper;
    private final RemotePackageDOService remotePackageDOService;
    private final PIVIPackageDOMapper piviPackageDOMapper;
    private final VehicleDmsDOMapper vehicleDmsDOMapper;
    private final AppDCuRenewRecordsMapper appDCuRenewRecordsMapper;
    private final RemoteCallService remoteCallService;
    @Resource
    private SeriesBrandMappingDataDOMapper mappingDataMapper;
    @Resource
    RedisService redisService;

    @Resource
    Snowflake ecpIdUtil;

    @Resource
    private RedisDelayQueueUtil redisDelayQueueUtil;

    @Resource
    private PIVIAmaPService piviAmaPService;

    @Value("${tsdp.icrSubscriptionUrl}")
    private String icrSubscriptionUrl;

    @Value("${tsdp.ifEcomToken}")
    private String ifEcomToken;

    @Value("${busyErrorLimit:5}")
    Integer busyErrorLimit;

    @Value("${busyDelaySecond:10}")
    Integer busyDelaySecond;

    @Resource
    private SeriesBrandMappingDataDOService seriesBrandMappingDataDOService;

    @Resource
    private VinInitializeService vinInitializeService;

    @Resource
    private ConsumerVehicleRepository consumerVehicleRepository;

    @Resource
    private IncontrolVehicleRepository incontrolVehicleRepository;





    @Override
    public List<IncontrolVehicleByCarDTO> getIncontrolVehicleByCarVinList(List<String> carVinList) {
        if (CollUtil.isEmpty(carVinList)) {
            return Collections.emptyList();
        }

        return incontrolVehicleMapper.selectIncontrolVehicleByCarVinList(carVinList);
    }

    /**
     * 根据车辆VIN列表和控制ID批量添加PIVI订阅服务信息。
     *
     * @param carVinList 车辆VIN列表，用于查询相应的PIVI订阅服务信息。
     * @param inControlId 控制ID，用于标识订阅服务的归属。
     * @param piviPackageDOMap 包含PIVI包信息的映射
     */
    public void serviceAddPIVIByCarVin(List<String> carVinList, String inControlId, Map<String, PIVIPackageDO> piviPackageDOMap) {
        log.info("carVinList:{}, inControlId={}", carVinList, inControlId);
        if (CollUtil.isEmpty(carVinList)) {
            return;
        }
        List<SubscriptionServiceDO> piviServiceDOList = selectPIVIServiceByCarVinList(carVinList);
        log.info("根据车辆VIN列表查询PIVI订阅服务信息, piviServiceDOList:{}", piviServiceDOList);
        List<String> addVinList = getAddVinList(carVinList, piviServiceDOList);
        log.info("获取piviPackageCarVinList:{}", addVinList);
        // 保存车辆PIVI订阅服务信息
        if (CollUtil.isNotEmpty(addVinList)) {
            // 根据vin查询手动续费成功的记录
            Map<String, List<AppDCuRenewRecords>> appDCuRenewRecordsMap = appDCuRenewRecordsMapper.getByVinListAndStatus(addVinList,
                            AppDRenewStatusEnum.RENEW_SUCCESS.getStatus(), null, null)
                    .stream().collect(Collectors.groupingBy(AppDCuRenewRecords::getCarVin));
            List<SubscriptionServiceDO> addServiceDOList = new ArrayList<>();
            for (String carVin : addVinList) {
                PIVIPackageDO piviPackageDO = piviPackageDOMap.get(carVin);
                if (Objects.isNull(piviPackageDO)) {
                    log.info("根据车辆VIN列表和控制ID批量添加PIVI订阅服务信息, PIVIPackage为空, carVin:{}", carVin);
                    continue;
                }
                addServiceDOList.addAll(buildPIVISubscriptionServiceDOList(piviPackageDO, inControlId, appDCuRenewRecordsMap.get(carVin)));
            }
            log.info("获取的addServiceDOList:{}", addServiceDOList);
            subscriptionServiceMapper.insertBatch(addServiceDOList);
        }
    }

    /**
     * 根据PIVI套餐信息和控制ID构建订阅服务数据对象列表。
     *
     * @param piviPackageDO PIVI套餐数据对象列表。
     * @param inControlId    控制ID。
     * @param appDCuRenewRecords    appDCu手动续费记录。
     * @return 返回订阅服务数据对象列表。如果输入的PIVI套餐列表为空，则返回空列表。
     */
    private List<SubscriptionServiceDO> buildPIVISubscriptionServiceDOList(PIVIPackageDO piviPackageDO,
                                                                           String inControlId, List<AppDCuRenewRecords> appDCuRenewRecords) {
        List<SubscriptionServiceDO> piviPackageDOList = new ArrayList<>();
        AppDCuRenewRecords appDRecord = null;
        AppDCuRenewRecords cuRecord = null;
        if (CollUtil.isNotEmpty(appDCuRenewRecords)) {
            // 取appDCuRenewRecords中最新的一条appd的续费记录和cu的续费记录
            appDRecord = appDCuRenewRecords.stream()
                    .filter(records -> RenewServiceTypeEnum.APPD.getServiceType().equals(records.getRenewServiceType()))
                    .max(Comparator.comparing(AppDCuRenewRecords::getId)).orElse(null);

            cuRecord = appDCuRenewRecords.stream()
                    .filter(records -> RenewServiceTypeEnum.UNICOM.getServiceType().equals(records.getRenewServiceType()))
                    .max(Comparator.comparing(AppDCuRenewRecords::getId)).orElse(null);
        }

        // appD订阅服务到期日取appD手动续费记录的到期日,appD手动续费记录不存在则用pivi_package中的到期日
        LocalDateTime appDExpiryDate = Objects.nonNull(appDRecord) ? appDRecord.getRenewAfterExpiryDate() : piviPackageDO.getExpiryDate();
        // cu订阅服务到期日取cu手动续费记录的到期日,cu手动续费记录不存在则用pivi_package中的到期日
        LocalDateTime cuExpiryDate = Objects.nonNull(cuRecord) ? cuRecord.getRenewAfterExpiryDate() : piviPackageDO.getExpiryDate();
        if (Objects.nonNull(piviPackageDO.getJlrSubscriptionId())) {
            if (Objects.isNull(appDExpiryDate)) {
                log.warn("构建订阅服务数据对象, appDExpiryDate为空, piviPackageDO:{}", piviPackageDO);
            } else {
                Arrays.stream(AppDServiceNameEnum.values())
                        .forEach(service -> piviPackageDOList.add(
                                buildPIVISubscriptionServiceDO(piviPackageDO, inControlId,
                                        service.getServiceName(), appDExpiryDate)
                        ));
            }
        }
        if (Objects.isNull(cuExpiryDate)) {
            log.warn("构建订阅服务数据对象, cuExpiryDate为空, piviPackageDO:{}", piviPackageDO);
        } else {
            piviPackageDOList.add(buildPIVISubscriptionDOByPackage(piviPackageDO, inControlId,
                    ServiceNameEnum.UNICOM.getServiceName(), ServicePackageEnum.DATA_PLAN.getPackageName(), cuExpiryDate));
        }

        // aMap直接取pivi_package中的到期日
        if (Objects.isNull(piviPackageDO.getAmaPExpireDate())) {
            log.warn("构建订阅服务数据对象, AmaPExpireDate为空, piviPackageDO:{}", piviPackageDO);
        } else {
            piviPackageDOList.add(buildPIVISubscriptionDOByPackage(piviPackageDO, inControlId,
                    ServiceNameEnum.AMAP.getServiceName(), ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName(), piviPackageDO.getAmaPExpireDate()));
        }
        return piviPackageDOList;
    }

    /**
     * 根据PIVI套餐信息和控制ID构建订阅服务数据对象。
     *
     * @param piviPackageDO PIVI套餐数据对象，包含套餐的详细信息。
     * @param inControlId 控制ID，用于标识订阅服务的控制单元。
     * @param serviceName 服务名称，描述订阅服务的类型或名称。
     * @param servicePackage 服务套餐，具体描述订阅服务的套餐内容或级别。
     * @param expiryDate 服务到期日
     * @return 返回构建完成的SubscriptionServiceDO对象，包含所有的订阅服务信息。
     */
    private SubscriptionServiceDO buildPIVISubscriptionDOByPackage(PIVIPackageDO piviPackageDO, String inControlId,
                                                                   String serviceName, String servicePackage, LocalDateTime expiryDate) {
        log.info("根据PIVI套餐信息和控制ID构建订阅服务数据对象, piviPackageDO:{}, inControlId:{}, serviceName：{}, expiryDate：{}" +
                " servicePackage:{}", piviPackageDO, inControlId, serviceName, servicePackage, expiryDate);
        SubscriptionServiceDO subscriptionService = new SubscriptionServiceDO();
        subscriptionService.setCarVin(piviPackageDO.getVin());
        subscriptionService.setServiceName(serviceName);
        subscriptionService.setIccid(piviPackageDO.getIccid());
        subscriptionService.setSubscriptionId(ecpIdUtil.nextIdStr());
        subscriptionService.setJlrSubscriptionId(piviPackageDO.getJlrSubscriptionId());
        subscriptionService.setServicePackage(servicePackage);
        subscriptionService.setExpiryDate(expiryDate);
        subscriptionService.setExpireDateUtc0(expiryDate.minusHours(8));
        subscriptionService.setServiceType(Constants.SERVICE_TYPE.PIVI);
        return subscriptionService;
    }

    /**
     * 根据PIVI套餐信息和inControl ID构建订阅服务数据对象。
     *
     * @param piviPackageDO PIVI套餐数据对象，包含套餐详情。
     * @param inControlId inControl的ID，用于标识订阅服务的控制器。
     * @param serviceName 服务名称，描述订阅服务的类型或名称。
     * @param expiryDate 服务到期日
     * @return 返回构建完成的订阅服务数据对象。
     */
    private SubscriptionServiceDO buildPIVISubscriptionServiceDO(PIVIPackageDO piviPackageDO, String inControlId,
                                                                 String serviceName, LocalDateTime expiryDate) {
        SubscriptionServiceDO subscriptionService = new SubscriptionServiceDO();
        subscriptionService.setSubscriptionId(ecpIdUtil.nextIdStr());
        subscriptionService.setCarVin(piviPackageDO.getVin());
        subscriptionService.setServiceName(serviceName);
        subscriptionService.setServicePackage(piviPackageDO.getPackageCode());
        subscriptionService.setExpiryDate(expiryDate);
        subscriptionService.setExpireDateUtc0(expiryDate.minusHours(8));
        subscriptionService.setServiceType(Constants.SERVICE_TYPE.PIVI);
        subscriptionService.setJlrSubscriptionId(piviPackageDO.getJlrSubscriptionId());
        subscriptionService.setIccid(piviPackageDO.getIccid());
        return subscriptionService;
    }

    /**
     * 获取需要新增的 VIN 列表。
     * 并将需要更新的订阅服务加入列表。
     *
     * @param carVinList VIN 列表
     * @param serviceDOList 已有的订阅服务列表
     * @return 返回包含需要查询 VIN 列表和需要更新订阅服务列表的 Pair
     */
    private List<String> getAddVinList(List<String> carVinList, List<SubscriptionServiceDO> serviceDOList) {
        if (CollUtil.isEmpty(serviceDOList)) {
            return new ArrayList<>(new HashSet<>(carVinList)); // 去重后返回
        }

        // 提取已有 VIN 的集合
        Set<String> existingVins = serviceDOList.stream()
                .map(SubscriptionServiceDO::getCarVin)
                .collect(Collectors.toSet());

        // 找出不在已有集合中的 VIN
        return carVinList.stream()
                .distinct()
                .filter(carVin -> !existingVins.contains(carVin))
                .collect(Collectors.toList());
    }

    /**
     * 根据车辆VIN列表查询PIVI订阅服务信息。
     *
     * @param carVinList 车辆VIN列表，用于查询订阅服务的条件之一。
     * @return 返回匹配的订阅服务信息列表。如果输入的VIN列表为空，返回空列表。
     */
    private List<SubscriptionServiceDO> selectPIVIServiceByCarVinList(List<String> carVinList) {
        if (CollUtil.isEmpty(carVinList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SubscriptionServiceDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SubscriptionServiceDO::getCarVin, carVinList)
                .eq(SubscriptionServiceDO::getIsDeleted, false)
                .eq(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.PIVI);
        return subscriptionServiceMapper.selectList(queryWrapper);
    }

    @Nullable
    public List<VinsAndServiceDTO> queryAndStoreVinsAndServiceInfo(String inControlId) {
        List<VinsAndServiceDTO> vinsAndServiceDTO = getVinsAndServiceByIncontronId(inControlId);
        if (CollUtil.isEmpty(vinsAndServiceDTO)) {
            log.info("TSDP未查询到{}的车机数据", inControlId);
            // 将车辆与icr关系表更新, icr字段置为空
            vinInitializeService.updateInControlIdNull(inControlId);
            return null;
        }
        getSelf().saveSubscriptionServiceInfo(inControlId, vinsAndServiceDTO);
        return vinsAndServiceDTO;
    }

    @Transactional
    public void saveSubscriptionServiceInfo(String inControlId, List<VinsAndServiceDTO> vinsAndServiceDTO) {
        List<SubscriptionServiceDO> saveOrUpdateList = new ArrayList<>();
        List<String> packageCodes = vinsAndServiceDTO.stream()
                .flatMap(car -> car.getServicePackage().stream())
                .map(ServicePackageDTO::getServicePackageName)
                .collect(Collectors.toList());
        List<String> existPackageCode = remotePackageDOService.getExistPackageCode(packageCodes);
        for (VinsAndServiceDTO andServiceDTO : vinsAndServiceDTO) {
            // 查旧车
            List<SubscriptionServiceDO> oldServiceList = subscriptionServiceMapper.selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                    .in(SubscriptionServiceDO::getCarVin, andServiceDTO.getVin())
                    .in(SubscriptionServiceDO::getServiceType, Constants.SERVICE_TYPE.REMOTE, Constants.SERVICE_TYPE.NOT_REMOTE)
            );
            Map<String, SubscriptionServiceDO> oldServiceMap = oldServiceList.stream().collect(Collectors.toMap(k -> k.getServicePackage() + Constants.DEFAULT_CONCAT_STR + k.getServiceName(), v -> v));
            for (ServicePackageDTO servicePackage : andServiceDTO.getServicePackage()) {
                // 比对
                SubscriptionServiceDO oldService = oldServiceMap.get(servicePackage.getServicePackageName() + Constants.DEFAULT_CONCAT_STR + servicePackage.getServiceName());
                if (oldService == null) {
                    SubscriptionServiceDO insertService = new SubscriptionServiceDO();
                    insertService.setCarVin(andServiceDTO.getVin());
                    insertService.setServiceName(servicePackage.getServiceName());
                    insertService.setServicePackage(servicePackage.getServicePackageName());
                    insertService.setExpireDateUtc0(servicePackage.getExpireDateUTC0());
                    insertService.setExpiryDate(servicePackage.getExpireDateUTC0().plusHours(8));
                    insertService.setServiceType(existPackageCode.contains(insertService.getServicePackage())
                            ? Constants.SERVICE_TYPE.REMOTE
                            : Constants.SERVICE_TYPE.NOT_REMOTE);
                    insertService.setSubscriptionId(ecpIdUtil.nextIdStr());
                    saveOrUpdateList.add(insertService);
                    // 只有到期日不相等才做更新
                } else if (!servicePackage.getExpireDateUTC0().isEqual(oldService.getExpireDateUtc0())) {
                    oldService.setExpireDateUtc0(servicePackage.getExpireDateUTC0());
                    oldService.setExpiryDate(servicePackage.getExpireDateUTC0().plusHours(8));
                    oldService.setUpdatedTime(LocalDateTime.now());
                    saveOrUpdateList.add(oldService);
                }
            }
        }
        // 同一车下的服务包和服务名称去重
        Map<String, SubscriptionServiceDO> oldServiceMap = saveOrUpdateList.stream().collect(Collectors.toMap(k -> k.getCarVin() + Constants.DEFAULT_CONCAT_STR + k.getServicePackage() + Constants.DEFAULT_CONCAT_STR + k.getServiceName(), v -> v, (v1, v2) -> v1));
        log.info("{}账号下要保存的subscriptionService数量为{}条", inControlId, oldServiceMap.size());
        subscriptionServiceMapper.saveOrUpdateBatch(oldServiceMap.values());
    }

    /**
     * 构建并保存车辆信息
     *
     * @param inControlId ICR账号ID
     * @param carVinList carVinList
     * @param piviPackageDOMap 包含PIVI套餐信息的映射
     */
    public void buildAndSaveVehicle(String inControlId, List<String> carVinList, Map<String, PIVIPackageDO> piviPackageDOMap) {
        // 如果pivi发票日期为null，发票日期要查询dms表
        List<VehicleDmsDO> vehicleDmsDOS = vehicleDmsDOMapper.selectVehicleDmsDOByCarVinList(carVinList);
        Map<String, String> dmsMap = vehicleDmsDOS.stream().collect(Collectors.toMap(VehicleDmsDO::getCarVin, VehicleDmsDO::getInvoiceDate, (v1, v2) -> v1));
        // 根据vin查询绑定关系
        List<IncontrolVehicleDO> vehicleList = incontrolVehicleMapper.selectList(new LambdaQueryWrapperX<IncontrolVehicleDO>()
                .in(IncontrolVehicleDO::getCarVin, carVinList)
                .eq(IncontrolVehicleDO::getIsDeleted, false));
        // 系统不存在的vin列表，需要查询dp
        List<String> callDpVinList = new ArrayList<>();
        // 待新增的数据列表
        List<IncontrolVehicleDO> needInsertDoList = new ArrayList<>();
        // 为空的话，全部需要查询DP
        if (CollUtil.isEmpty(vehicleList)) {
            callDpVinList = carVinList;
        } else {
            checkAddOrUpdate(inControlId, vehicleList, carVinList, callDpVinList, needInsertDoList, piviPackageDOMap, dmsMap);
        }
        if (CollUtil.isNotEmpty(callDpVinList)) {
            // 调用dp查询vin信息
            // * 5、通过List<VIN>查询DP系统，获得这些VIN对应的完整的品牌、车型、配置、年款信息，通过其车型、年款与ECP主数据t_vehicle_model_master_data数据对比，获得该VIN车辆对应的车机系统car_system_model
            List<UserDPResultVO> dpList = vehicleModelMasterDataService.findDpList(callDpVinList);
            if (CollUtil.isEmpty(dpList)) {
                log.info("subscription未查询到{}名下车辆相关DP数据", inControlId);
                // 存入 redis队列
                save2DelayQue(inControlId);
                throw new RuntimeException("subscription未查询到名下车辆相关DP数据");
            }
            //seriseCode判断
            Map<String, UserDPResultVO> vinMap = dpList.stream()
                    .collect(Collectors.toMap(UserDPResultVO::getVin, v -> v, (o, n) -> o));
            // 将dp查询到的新数据都加入到列表中
            List<IncontrolVehicleDO> vehicleDOList = callDpVinList.stream().filter(vinMap::containsKey).map(vin -> {
                UserDPResultVO dpResultVO = vinMap.get(vin);
                seriesBrandMappingDataDOService.createSeriesBrandMappingData(dpResultVO);
                IncontrolVehicleDO vehicle = BeanUtil.copyProperties(dpResultVO, IncontrolVehicleDO.class);
                vehicle.setIncontrolId(inControlId);
                vehicle.setCarVin(vin);
                vehicle.setDmsInvoiceDate(getDmsInvoiceDate(piviPackageDOMap, vin, dmsMap));
                vehicle.setBindTime(LocalDateTime.now());
                return vehicle;
            }).collect(Collectors.toList());
            needInsertDoList.addAll(vehicleDOList);
        }
        List<String> existCarVinList = needInsertDoList.stream().map(IncontrolVehicleDO::getCarVin).collect(Collectors.toList());

        // 根据icr查询绑定关系
        List<IncontrolVehicleDO> vehicleListByIcr = incontrolVehicleMapper.selectListByICR(inControlId);

        // 将不在existCarVinList中的vin过滤出来，将incontrolId置为空
        List<IncontrolVehicleDO> needUpdateDoList = vehicleListByIcr.stream()
                .filter(vehicleDO -> !existCarVinList.contains(vehicleDO.getCarVin()))
                .peek(vehicleDO -> {
                    vehicleDO.setIncontrolId(null);
                    vehicleDO.setBindTime(LocalDateTime.now());
                    vehicleDO.setCreatedTime(LocalDateTime.now());
                    vehicleDO.setUpdatedTime(LocalDateTime.now());
                })
                .collect(Collectors.toList());
        needInsertDoList.addAll(needUpdateDoList);
        log.info("记录到数据库的Vehicle数量为{}条, inControlId:{}", needInsertDoList.size(), inControlId);

        // 先删除ICR账号的记录
        incontrolVehicleMapper.deleteVehicleByIncontrol(inControlId);
        // 根据vin删除记录
        incontrolVehicleMapper.deleteVehicleByCarVinList(carVinList);
        if (CollUtil.isNotEmpty(needInsertDoList)) {
            incontrolVehicleMapper.insertBatch(needInsertDoList);
        }
    }

    /**
     * 检查并更新车辆信息列表
     * 该方法用于处理车辆信息的添加或更新逻辑
     * 首先，它会根据车辆的VIN号将现有的车辆信息列表转换为一个映射，以便快速查找
     * 然后，遍历待处理的车辆VIN号列表，对于每个VIN号，如果它不在现有的车辆信息列表中，则将其添加到需要调用DP接口的VIN号列表中
     * 如果存在，但是关联的inControlId不相等，则更新inControlId，并将当前时间设置为绑定时间和更新时间
     * 最后，将处理后的车辆信息添加到需要插入的车辆信息列表中
     *
     * @param inControlId 控制系统ID，用于关联车辆信息
     * @param vehicleList 现有的车辆信息列表
     * @param carVinList 待处理的车辆VIN号列表
     * @param callDpVinList 需要调用DP接口的VIN号列表
     * @param needInsertDoList 需要插入的车辆信息列表
     */
    private void checkAddOrUpdate(String inControlId, List<IncontrolVehicleDO> vehicleList, List<String> carVinList, List<String> callDpVinList,
                                  List<IncontrolVehicleDO> needInsertDoList, Map<String, PIVIPackageDO> piviPackageDOMap, Map<String, String> dmsMap) {
        Map<String, IncontrolVehicleDO> vinMap = vehicleList.stream()
                .collect(Collectors.toMap(IncontrolVehicleDO::getCarVin, v -> v, (o, n) -> o));
        // 遍历carVinList，如果vin不在vehicleList里面，则加入newVinList，如果存在，但是inControlId不相等，则修改inControlId
        for (String vin : carVinList) {
            IncontrolVehicleDO vehicleDO = vinMap.get(vin);
            if (Objects.isNull(vehicleDO)) {
                callDpVinList.add(vin);
            } else {
                // vin存在，但是inControlId不相等，则修改inControlId
                if (!Objects.equals(vehicleDO.getIncontrolId(), inControlId)) {
                    vehicleDO.setIncontrolId(inControlId);
                    vehicleDO.setBindTime(LocalDateTime.now());
                }
                vehicleDO.setCreatedTime(LocalDateTime.now());
                vehicleDO.setUpdatedTime(LocalDateTime.now());
                vehicleDO.setDmsInvoiceDate(getDmsInvoiceDate(piviPackageDOMap, vin, dmsMap));
                needInsertDoList.add(vehicleDO);
            }
        }
    }

    /**
     * 根据车辆VIN码获取发票日期。
     *
     * @param packageDOMap 车辆信息的映射表，键为VIN码，值为车辆详细信息对象。
     * @param carVin 车辆的VIN码。
     * @return 返回格式化后的发票日期，如果车辆信息映射表为空、VIN码为空或车辆信息中没有发票日期，则返回null。
     */
    private LocalDateTime getDmsInvoiceDate(Map<String, PIVIPackageDO> packageDOMap, String carVin, Map<String, String> dmsMap) {
        if (StringUtils.isBlank(carVin)) {
            return null;
        }
        if (CollUtil.isEmpty(packageDOMap)) {
            if (CollUtil.isEmpty(dmsMap)) {
                return null;
            }
            return TimeFormatUtil.dmsToLocalDate(dmsMap.get(carVin));
        }
        PIVIPackageDO piviPackageDO = packageDOMap.get(carVin);
        if (Objects.nonNull(piviPackageDO)) {
            return piviPackageDO.getDmsInvoiceDate();
        }
        if (CollUtil.isEmpty(dmsMap)) {
            return null;
        }
        return TimeFormatUtil.dmsToLocalDate(dmsMap.get(carVin));
    }

    public List<VinsAndServiceDTO> getVinsAndServiceByIncontronId(String inControlId) {

        // 从TSDP获取改账号下车辆和服务数据
        ArrayList<VinsAndServiceDTO> resultList;

        resultList = getSubscriptionsFromTSDP(inControlId);
        if (CollUtil.isEmpty(resultList)) {
            log.info("{}账号未从TSDP查询到车辆服务信息", inControlId);
            // 存入 redis队列
            save2DelayQue(inControlId);
        }

        //  其他环境mock处理

        // mock 返回
        return resultList;
    }

    private void save2DelayQue(String inControlId) {
        Integer increment = redisService.getCacheObject(StrUtil.format(Constants.BUSINESS_LOCK_KEY.ICR_TSDP_QRY_FAIL, inControlId));
        if (increment == null || increment <= busyErrorLimit) {
            redisService.increment(StrUtil.format(Constants.BUSINESS_LOCK_KEY.ICR_TSDP_QRY_FAIL, inControlId));
            redisDelayQueueUtil.remove(inControlId, RedisDelayQueueEnum.ECP_ICR_TSDP_QRY_RETRY_TASK.getCode());
            redisDelayQueueUtil.addDelayQueue(inControlId, busyDelaySecond, TimeUnit.SECONDS, RedisDelayQueueEnum.ECP_ICR_TSDP_QRY_RETRY_TASK.getCode());
        }
    }

    @Nullable
    private ArrayList<VinsAndServiceDTO> getSubscriptionsFromTSDP(String inControlId) {
        String url = icrSubscriptionUrl;
        // 设置header数据
        HttpHeaders headers = new HttpHeaders();
        headers.add("Accept", "application/json");
        headers.add("Authorization", ifEcomToken);
        // 创建HttpEntity对象，包含header和body数据
        HttpEntity<String> entity = new HttpEntity<>(headers);
        url = url + inControlId;
        // 发送GET请求
        ResponseEntity<JSONObject> response;
        try {
            log.info("TSDP订阅查询URL:{}", url);
            response = restTemplate.exchange(url, HttpMethod.GET, entity, JSONObject.class);
        } catch (RestClientException e) {
            log.info("TSDP查询账号下订阅失败：", e);
            // 存入 redis队列
            save2DelayQue(inControlId);
            if (e instanceof HttpClientErrorException.Forbidden) {
                log.error("403错误", e);
                throw exception(TSDP_REQUEST_FORBIDDEN);
            }
            if (e instanceof HttpClientErrorException.TooManyRequests) {
                log.error("429错误", e);
                throw exception(TSDP_REQUEST_RATE_TOO_FAST);
            }
            throw e;
        }
        if (HttpStatus.OK.equals(response.getStatusCode())) {
            JSONObject body = response.getBody();
            log.info("{}账号下TSDP订阅查询body===>\r\n{}", inControlId, body);
            log.info("Current Server TimeZone: " + TimeZone.getDefault().getID() + "name " + TimeZone.getDefault().getDisplayName());

            return parseResult(inControlId, body);
        }
        return null;
    }

    @NotNull
    private ArrayList<VinsAndServiceDTO> parseResult(String inControlId, JSONObject body) {
        JSONArray subscriptions = body.getJSONArray("subscriptions");
        ArrayList<VinsAndServiceDTO> list = Lists.newArrayList();
        if (CollUtil.isNotEmpty(subscriptions)) {
            for (Object subscription : subscriptions) {
                VinsAndServiceDTO serviceDTO = new VinsAndServiceDTO();
                JSONObject subscriptionJson = new JSONObject((Map) subscription);
                serviceDTO.setVin(subscriptionJson.getString("vin"));
                JSONArray packages = subscriptionJson.getJSONArray("packages");
                if (CollUtil.isNotEmpty(packages)) {
                    ArrayList<ServicePackageDTO> packageList = Lists.newArrayList();
                    for (Object aPackage : packages) {
                        JSONObject packageJson = new JSONObject((Map) aPackage);
                        JSONArray servicesJson = packageJson.getJSONArray("services");
                        if (CollUtil.isNotEmpty(servicesJson)) {
                            for (Object service : servicesJson) {
                                ServicePackageDTO packageDTO = new ServicePackageDTO();
                                packageDTO.setServicePackageName(packageJson.getString("packageName"));
                                Date expiryDate = packageJson.getDate("expiryDate");
                                if (expiryDate == null) {
                                    continue;
                                }
                                packageDTO.setExpireDate(LocalDateTime.ofInstant(expiryDate.toInstant(), ZoneId.of("UTC")).plusHours(8));
                                packageDTO.setExpireDateUTC0(LocalDateTime.ofInstant(expiryDate.toInstant(), ZoneId.of("UTC")));
                                packageDTO.setServiceName(String.valueOf(service));
                                packageList.add(packageDTO);
                            }
                        }
                    }
                    serviceDTO.setServicePackage(packageList);
                }
                list.add(serviceDTO);
            }
            log.info("{}账号下TSDP查询结果组装list===>\r\n{}", inControlId, JSON.toJSON(list));
        }
        return list;
    }


    @Override
    public List<UserCarServiceListVO> getUserCarServiceList(String consumerCode, String brandCode, String clientId) {
        List<UserCarServiceListVO> collect = new ArrayList<>();
        //调用api获取t_incontrol_vehicle PIVI车机
        List<IncontrolVehicleDTO> icrData = incontrolVehicleRepository.getIncontrolVehicleByConsumerCode(consumerCode, clientId);

        // 根据jlrid查询绑车VIN LIST
        List<ConsumerVehicleDO> consumerVehicleDOS = consumerVehicleRepository.getListByJlrId(consumerCode);
        // 合并所有车辆VIN
        List<String> allCarVinList = Stream.concat(
                icrData.stream().map(IncontrolVehicleDTO::getCarVin),
                consumerVehicleDOS.stream().map(ConsumerVehicleDO::getCarVin)
        ).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(allCarVinList)) {
            // 分品牌
            List<IncontrolVehicleDTO> data = incontrolVehicleMapper.selectCarInfoByCarVinListAndBrandCode(allCarVinList,clientId);

            if(CollUtil.isEmpty(data)){
                log.info("selectCarInfoByCarVinListAndBrandCode查询未找到对应的IncontrolVehicle,入参allCarVinList:{},clientId:{}",allCarVinList,clientId);
                return collect;
            }
            List<SeriesBrandMappingDataDO> mapList = mappingDataMapper.selectList(new LambdaQueryWrapperX<SeriesBrandMappingDataDO>()
                    .eq(BaseDO::getIsDeleted, false));

            List<String> carVinList = data.stream().map(IncontrolVehicleDTO::getCarVin).collect(Collectors.toList());
            //根据carVinList 查询 t_subscription_service 服务包
            List<SubscriptionServiceDO> subscriptionServiceList = subscriptionServiceMapper.selectList(new LambdaQueryWrapperX<SubscriptionServiceDO>()
                    .in(SubscriptionServiceDO::getCarVin, carVinList)
                    .eq(BaseDO::getIsDeleted, false));
            // 过滤出PIVI的服务
            List<SubscriptionServiceDO> piviServiceList = subscriptionServiceList.stream()
                    .filter(e -> ServiceTypeEnum.PIVI_SUBSCRIPTION.getCode().equals(e.getServiceType()))
                    .collect(Collectors.toList());
            // 过滤出REMOTE的服务
            List<SubscriptionServiceDO> remoteServiceList = subscriptionServiceList.stream()
                    .filter(e -> ServiceTypeEnum.REMOTE_VCS.getCode().equals(e.getServiceType()))
                    .collect(Collectors.toList());
            //筛选符合车型
            collect = data.stream()
                    .map(incontrolVehicleDTO -> {
                        //组装返回数据
                        UserCarServiceListVO userCarServiceListVO = new UserCarServiceListVO();
                        // 组装car的返回值和是否可以购买REMOTE
                        buildUserCar(incontrolVehicleDTO, remoteServiceList, userCarServiceListVO);
                        // 是否可以购买PIVI
                        judgePiviInvoiceDate(incontrolVehicleDTO, userCarServiceListVO, piviServiceList);
                        return userCarServiceListVO;
                    }).collect(Collectors.toList());
            collect = collect.stream().peek(vo -> {
                SeriesBrandMappingDataDO seriesBrand = mapList.stream()
                        .filter(dataDO -> dataDO.getSeriesCode().equals(vo.getSeriesCode())).findFirst().orElse(null);
                if (seriesBrand != null) {
                    vo.setBrandName(StringUtils.isNotBlank(seriesBrand.getBrandNameView()) ? seriesBrand.getBrandNameView() : vo.getBrandName());
                    vo.setSeriesName(StringUtils.isNotBlank(seriesBrand.getSeriesName()) ? seriesBrand.getSeriesName() : vo.getSeriesName());
                }
            }).collect(Collectors.toList());
        }
        log.info("{}账号下用户车辆信息组装结果===>{}", consumerCode, collect);
        return collect;
    }

    private void judgePiviInvoiceDate(IncontrolVehicleDTO incontrolVehicleDTO, UserCarServiceListVO userCarServiceListVO,
                                      List<SubscriptionServiceDO> piviServiceList) {
        String carVin = incontrolVehicleDTO.getCarVin();
        ServiceDetailVO detailVO = new ServiceDetailVO();
        // 默认不能购买
        detailVO.setEnable(false);
        detailVO.setServiceNameCn(Constants.PIVI_SERVICE_NAME);
        Map<Integer, ServiceDetailVO> serviceMap = userCarServiceListVO.getServiceMap();
        // 车辆不是PIVI车机
        if (!CarSystemModelEnum.PIVI.getCode().equals(incontrolVehicleDTO.getCarSystemModel())) {
            log.warn("不是PIVI车机, carVin={}", incontrolVehicleDTO.getCarVin());
            detailVO.setNotifyContent(Constants.CAN_NOT_BUY);
            detailVO.setButtonContent(Constants.I_KNOW);
            serviceMap.put(com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum.PIVI.getCode(), detailVO);
            userCarServiceListVO.setServiceMap(serviceMap);
            return;
        }
        detailVO.setNotifyContent(Constants.CAN_NOT_BUY_CONTACT_CONSUMER);
        detailVO.setButtonContent(Constants.CONTACT_CONSUMER);
        // 找出匹配vin的服务，按照包名分组
        Map<String, LocalDateTime> packageMap = piviServiceList.stream()
                .filter(piviService -> userCarServiceListVO.getVin().equals(piviService.getCarVin()))
                .collect(Collectors.toMap(SubscriptionServiceDO::getServicePackage, SubscriptionServiceDO::getExpiryDate, (a, b) -> a));
        if (CollUtil.isNotEmpty(packageMap)) {
            log.info("packageMap={}, carVin={}", packageMap, carVin);
            // ecp过期时间
            LocalDateTime ecpExpiryDate = packageMap.get(ServicePackageEnum.ONLINE_PACK.getPackageName());
            // appd为null，用unicom的过期时间
            if (Objects.isNull(ecpExpiryDate)) {
                ecpExpiryDate = packageMap.get(ServicePackageEnum.DATA_PLAN.getPackageName());
            }
            // amap实际过期时间
            LocalDateTime aMapExpiryDate = packageMap.get(ServicePackageEnum.CONNECTED_NAVIGATION.getPackageName());
            detailVO.setExpiryDate(ecpExpiryDate);
            // 判断发票时间
            LocalDateTime invoiceDate = incontrolVehicleDTO.getDmsInvoiceDate();
            if (invoiceDate == null) {
                log.warn("发票时间为空, 不能下单, carVin={}, incontrolId={}", carVin, incontrolVehicleDTO.getIncontrolId());
            } else if (ecpExpiryDate.isAfter(invoiceDate.plusYears(15))) {
                detailVO.setNotifyContent(Constants.CAN_NOT_BUY);
                detailVO.setButtonContent(Constants.I_KNOW);
                log.warn("SUBSCRIPTION过期时间-发票日期>15年, 不能下单 carVin={}, incontrolId={}", carVin, incontrolVehicleDTO.getIncontrolId());
                // 当AMAP实际过期时间<ECP到期日，且TODAY<ECP到期日，这种情况不能下单
            } else if (aMapExpiryDate.toLocalDate().isBefore(ecpExpiryDate.toLocalDate()) &&
                    LocalDateTime.now().toLocalDate().isBefore(ecpExpiryDate.toLocalDate())) {
                log.warn("车辆AMAP< ECP(Invoice Date+36months), 且Today< ECP(Invoice date+36months), 不能下单, carVin={}, incontrolId={}", carVin, incontrolVehicleDTO.getIncontrolId());
            } else {
                detailVO.setEnable(true);
                detailVO.setNotifyContent(null);
                detailVO.setButtonContent(null);
            }
        } else {
            log.warn("该车辆没有PIVI服务包, carVin={}, incontrolId={}", carVin, incontrolVehicleDTO.getIncontrolId());
        }
        serviceMap.put(com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum.PIVI.getCode(), detailVO);
        userCarServiceListVO.setServiceMap(serviceMap);
    }

    private void buildUserCar(IncontrolVehicleDTO incontrolVehicleDTO, List<SubscriptionServiceDO> remoteServiceList, UserCarServiceListVO userCarServiceListVO) {
        BeanUtils.copyProperties(incontrolVehicleDTO, userCarServiceListVO);
        userCarServiceListVO.setVin(incontrolVehicleDTO.getCarVin());
        ServiceDetailVO detailVO = new ServiceDetailVO();
        detailVO.setServiceNameCn(Constants.REMOTE_SERVICE_NAME);
        detailVO.setEnable(false);
        detailVO.setNotifyContent(Constants.CAN_NOT_BUY);
        detailVO.setButtonContent(Constants.I_KNOW);
        Map<Integer, ServiceDetailVO> serviceMap = new HashMap<>();
        // 必须是PIVI车机
        if (CarSystemModelEnum.PIVI.getCode().equals(incontrolVehicleDTO.getCarSystemModel())) {
            detailVO.setNotifyContent(Constants.CAN_NOT_BUY_CONTACT_CONSUMER);
            detailVO.setButtonContent(Constants.CONTACT_CONSUMER);
            //从合格的服务包里面比对vin号取值第一条
            Optional<SubscriptionServiceDO> service = remoteServiceList.stream()
                    .filter(subscriptionService -> userCarServiceListVO.getVin().equals(subscriptionService.getCarVin()))
                    .findFirst();
            if (service.isPresent()) {
                //组装List<UserCarServiceListVO>返回,全部PIVI车辆,标识符合购买的车辆
                LocalDateTime expiryDate = service.get().getExpiryDate();
                detailVO.setExpiryDate(expiryDate);
                // 判断发票时间
                LocalDateTime invoiceDate = incontrolVehicleDTO.getDmsInvoiceDate();
                // 如果发票时间不存在,则不允许下单
                if (invoiceDate == null) {
                    log.warn("发票时间为空, carVin={}, incontrolId={}", incontrolVehicleDTO.getCarVin(), incontrolVehicleDTO.getIncontrolId());
                } else if (expiryDate.isAfter(invoiceDate.plusYears(15))) {
                    log.warn("REMOTE过期时间-发票日期>15年, carVin={}, incontrolId={}", incontrolVehicleDTO.getCarVin(), incontrolVehicleDTO.getIncontrolId());
                    detailVO.setNotifyContent(Constants.CAN_NOT_BUY);
                    detailVO.setButtonContent(Constants.I_KNOW);
                } else {
                    detailVO.setEnable(true);
                    detailVO.setNotifyContent(null);
                    detailVO.setButtonContent(null);
                }
            } else {
                log.warn("没有合格的服务包, carVin={}, incontrolId={}", incontrolVehicleDTO.getCarVin(), incontrolVehicleDTO.getIncontrolId());
            }
        } else {
            log.warn("不是PIVI车机, carVin={}", incontrolVehicleDTO.getCarVin());
        }
        serviceMap.put(com.jlr.ecp.subscription.enums.fulfilment.ServiceTypeEnum.REMOTE.getCode(), detailVO);
        userCarServiceListVO.setServiceMap(serviceMap);
    }

    private IncontrolVehicleServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }
}
