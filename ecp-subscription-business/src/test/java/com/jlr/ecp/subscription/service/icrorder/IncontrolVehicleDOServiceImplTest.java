package com.jlr.ecp.subscription.service.icrorder;

import com.jlr.ecp.consumer.api.consumer.ConsumerApi;
import com.jlr.ecp.consumer.api.consumer.dto.ConsumerChannelDTO;
import com.jlr.ecp.framework.common.pojo.CommonResult;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleListRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.IcrVehicleRespVO;
import com.jlr.ecp.subscription.api.icrvehicle.vo.SeriesMappingVO;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.controller.admin.dto.SubscriptionSearchLocalDTO;
import com.jlr.ecp.subscription.controller.admin.dto.vehicle.ConsumerInfoVO;
import com.jlr.ecp.subscription.dal.dataobject.consumer.ConsumerIncontrolDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.mysql.consumer.ConsumerIncontrolMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.subscription.SubscriptionService;
import com.jlr.ecp.subscription.service.vehicle.IncontrolVehicleService;
import com.jlr.ecp.subscription.service.vehicle.VinInitializeService;
import com.jlr.ecp.subscription.util.PIPLDataUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class IncontrolVehicleDOServiceImplTest {

    @Mock
    private IncontrolVehicleDOMapper mockIncontrolVehicleDOMapper;
    @Mock
    private IncontrolVehicleService mockIncontrolVehicleService;
    @Mock
    private SubscriptionService mockSubscriptionService;
    @Mock
    private ConsumerIncontrolMapper mockConsumerIncontrolMapper;
    @Mock
    private ConsumerApi mockConsumerApi;
    @Mock
    private PIPLDataUtil mockPiplDataUtil;
    @Mock
    private SeriesBrandMappingDataDOService mockSeriesBrandMappingDataDOService;
    @Mock
    private VinInitializeService mockVinInitializeService;
    @Mock
    private RemoteCallService mockRemoteCallService;

    @InjectMocks
    private IncontrolVehicleDOServiceImpl incontrolVehicleDOServiceImplUnderTest;

    @Test
    public void testGetOneByCarVin() {
        // Setup
        final IcrVehicleRespVO expectedResult = IcrVehicleRespVO.builder().build();
        expectedResult.setCarVin("carVin");

        // Configure IncontrolVehicleDOMapper.selectOne(...).
        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .id(0L)
                .carVin("carVin")
                .build();
        when(mockIncontrolVehicleDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDO);

        // Run the test
        final IcrVehicleRespVO result = incontrolVehicleDOServiceImplUnderTest.getOneByCarVin("carVin");

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetOneByCarVin_IncontrolVehicleDOMapperReturnsNull() {
        // Setup
        when(mockIncontrolVehicleDOMapper.selectOne(any(LambdaQueryWrapperX.class))).thenReturn(null);

        // Run the test
        final IcrVehicleRespVO result = incontrolVehicleDOServiceImplUnderTest.getOneByCarVin("carVin");

        // Verify the results
        assertThat(result).isNull();
    }

    @Test
    public void testGetCarVinInfo() {
        // Setup
        final List<IcrVehicleListRespVO> expectedResult = List.of(IcrVehicleListRespVO.builder()
                .carVin("carVin")
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .build());

        // Configure IncontrolVehicleDOMapper.selectList(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = List.of(IncontrolVehicleDO.builder()
                .id(0L)
                .carVin("carVin")
                .incontrolId("icr")
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .incontrolPhone("incontrolPhone")
                .build());
        when(mockIncontrolVehicleDOMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDOS);

        // Run the test
        final List<IcrVehicleListRespVO> result = incontrolVehicleDOServiceImplUnderTest.getCarVinInfo(
                List.of("value"));

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testGetCarVinInfo_IncontrolVehicleDOMapperReturnsNoItems() {
        // Setup
        when(mockIncontrolVehicleDOMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<IcrVehicleListRespVO> result = incontrolVehicleDOServiceImplUnderTest.getCarVinInfo(
                List.of("value"));

        // Verify the results
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testSearchVinInfo_WithValidVin_ShouldCallGetConsumerInfoVOByVin() {
        // Setup
        String validVin = "QWEQWE12345678901";
        String icr = "<EMAIL>";

        // Configure mocks for VIN path
        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .id(1L)
                .carVin(validVin)
                .incontrolId(icr)
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .incontrolPhone("incontrolPhone")
                .build();
        when(mockIncontrolVehicleDOMapper.selectOneByCarVin(validVin)).thenReturn(incontrolVehicleDO);
        when(mockVinInitializeService.vinInitializeByQueryVin(validVin)).thenReturn(null);

        // Configure other required mocks
        final ConsumerIncontrolDO consumerIncontrolDO = new ConsumerIncontrolDO();
        consumerIncontrolDO.setConsumerCode("consumerCode");
        consumerIncontrolDO.setIncontrolId(icr);
        when(mockConsumerIncontrolMapper.selectNewByIncontrolId(icr)).thenReturn(consumerIncontrolDO);

        when(mockPiplDataUtil.getDecodeText("incontrolPhone")).thenReturn("result");
        when(mockPiplDataUtil.getEncryptText(icr)).thenReturn(icr);

        final ConsumerChannelDTO consumerChannelDTO = new ConsumerChannelDTO();
        consumerChannelDTO.setConsumerCode("consumerCode");
        final CommonResult<ConsumerChannelDTO> consumerChannelResult = CommonResult.success(consumerChannelDTO);
        when(mockConsumerApi.getConsumerChannelByCode("consumerCode")).thenReturn(consumerChannelResult);

        final Map<String, SeriesMappingVO> stringSeriesMappingVOMap = Map.ofEntries(
                Map.entry("seriesCode", SeriesMappingVO.builder()
                        .seriesName("seriesName")
                        .build()));
        when(mockSeriesBrandMappingDataDOService.getSeriesMapping()).thenReturn(stringSeriesMappingVOMap);

        final SubscriptionSearchLocalDTO subscriptionSearchLocalDTO = new SubscriptionSearchLocalDTO();
        subscriptionSearchLocalDTO.setCarSystemModel("carSystemModel");
        when(mockSubscriptionService.getExpireDateByVinLocal(any(IncontrolVehicleDO.class))).thenReturn(subscriptionSearchLocalDTO);

        // Run the test
        final CommonResult<ConsumerInfoVO> result = incontrolVehicleDOServiceImplUnderTest.searchVinInfo(validVin, icr);

        // Verify the results
        assertTrue(result.isSuccess());
        verify(mockVinInitializeService).vinInitializeByQueryVin(validVin);
        verify(mockIncontrolVehicleDOMapper).selectOneByCarVin(validVin);
        // Should not call ICR-related methods since VIN takes precedence
        verify(mockRemoteCallService, never()).getByICR(anyString());
    }

    @Test
    public void testSearchVinInfo_WithValidIcrOnly_ShouldCallGetConsumerInfoVOByICR() {
        // Setup
        String icr = "<EMAIL>";

        // Configure mocks for ICR path
        final VinsAndServiceDTO vinsAndServiceDTO = new VinsAndServiceDTO();
        vinsAndServiceDTO.setVin("QWEQWE12345678901");
        final List<VinsAndServiceDTO> vinList = List.of(vinsAndServiceDTO);
        when(mockRemoteCallService.getByICR(icr)).thenReturn(vinList);

        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .id(1L)
                .carVin("QWEQWE12345678901")
                .incontrolId(icr)
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .incontrolPhone("incontrolPhone")
                .build();
        when(mockIncontrolVehicleDOMapper.selectListByICR(anyString())).thenReturn(List.of(incontrolVehicleDO));

        // Configure other required mocks
        final ConsumerIncontrolDO consumerIncontrolDO = new ConsumerIncontrolDO();
        consumerIncontrolDO.setConsumerCode("consumerCode");
        consumerIncontrolDO.setIncontrolId(icr);
        when(mockConsumerIncontrolMapper.selectNewByIncontrolId(icr)).thenReturn(consumerIncontrolDO);

        when(mockPiplDataUtil.getDecodeText("incontrolPhone")).thenReturn("result");
        when(mockPiplDataUtil.getEncryptText(icr)).thenReturn(icr);

        final ConsumerChannelDTO consumerChannelDTO = new ConsumerChannelDTO();
        consumerChannelDTO.setConsumerCode("consumerCode");
        final CommonResult<ConsumerChannelDTO> consumerChannelResult = CommonResult.success(consumerChannelDTO);
        when(mockConsumerApi.getConsumerChannelByCode("consumerCode")).thenReturn(consumerChannelResult);

        final Map<String, SeriesMappingVO> stringSeriesMappingVOMap = Map.ofEntries(
                Map.entry("seriesCode", SeriesMappingVO.builder()
                        .seriesName("seriesName")
                        .build()));
        when(mockSeriesBrandMappingDataDOService.getSeriesMapping()).thenReturn(stringSeriesMappingVOMap);

        final SubscriptionSearchLocalDTO subscriptionSearchLocalDTO = new SubscriptionSearchLocalDTO();
        subscriptionSearchLocalDTO.setCarSystemModel("carSystemModel");
        when(mockSubscriptionService.getExpireDateByVinLocal(any(IncontrolVehicleDO.class))).thenReturn(subscriptionSearchLocalDTO);

        // Run the test
        final CommonResult<ConsumerInfoVO> result = incontrolVehicleDOServiceImplUnderTest.searchVinInfo(null, icr);

        // Verify the results
        assertTrue(result.isSuccess());
        verify(mockRemoteCallService).getByICR(icr);
        verify(mockVinInitializeService).vinInitializeByQueryICR(icr, vinList);
    }

    @Test
    public void testSearchVinInfo_WithBothVinAndIcr_ShouldPrioritizeVin() {
        // Setup
        String validVin = "QWEQWE12345678901";
        String icr = "<EMAIL>";

        // Configure mocks for VIN path (should be called)
        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .id(1L)
                .carVin(validVin)
                .incontrolId(icr)
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .incontrolPhone("incontrolPhone")
                .build();
        when(mockIncontrolVehicleDOMapper.selectOneByCarVin(validVin)).thenReturn(incontrolVehicleDO);
        when(mockVinInitializeService.vinInitializeByQueryVin(validVin)).thenReturn(null);

        // Configure other required mocks
        final ConsumerIncontrolDO consumerIncontrolDO = new ConsumerIncontrolDO();
        consumerIncontrolDO.setConsumerCode("consumerCode");
        consumerIncontrolDO.setIncontrolId(icr);
        when(mockConsumerIncontrolMapper.selectNewByIncontrolId(icr)).thenReturn(consumerIncontrolDO);

        when(mockPiplDataUtil.getDecodeText("incontrolPhone")).thenReturn("result");
        when(mockPiplDataUtil.getEncryptText(icr)).thenReturn(icr);

        final ConsumerChannelDTO consumerChannelDTO = new ConsumerChannelDTO();
        consumerChannelDTO.setConsumerCode("consumerCode");
        final CommonResult<ConsumerChannelDTO> consumerChannelResult = CommonResult.success(consumerChannelDTO);
        when(mockConsumerApi.getConsumerChannelByCode("consumerCode")).thenReturn(consumerChannelResult);

        final Map<String, SeriesMappingVO> stringSeriesMappingVOMap = Map.ofEntries(
                Map.entry("seriesCode", SeriesMappingVO.builder()
                        .seriesName("seriesName")
                        .build()));
        when(mockSeriesBrandMappingDataDOService.getSeriesMapping()).thenReturn(stringSeriesMappingVOMap);

        final SubscriptionSearchLocalDTO subscriptionSearchLocalDTO = new SubscriptionSearchLocalDTO();
        subscriptionSearchLocalDTO.setCarSystemModel("carSystemModel");
        when(mockSubscriptionService.getExpireDateByVinLocal(any(IncontrolVehicleDO.class))).thenReturn(subscriptionSearchLocalDTO);

        // Run the test
        final CommonResult<ConsumerInfoVO> result = incontrolVehicleDOServiceImplUnderTest.searchVinInfo(validVin, icr);

        // Verify the results
        assertTrue(result.isSuccess());
        verify(mockVinInitializeService).vinInitializeByQueryVin(validVin);
        verify(mockIncontrolVehicleDOMapper).selectOneByCarVin(validVin);
        // Should not call ICR-related methods since VIN takes precedence
        verify(mockRemoteCallService, never()).getByICR(anyString());
        verify(mockVinInitializeService, never()).vinInitializeByQueryICR(anyString(), any());
    }

    @Test
    public void testSearchVinInfo_WithNullOrEmptyParameters_ShouldReturnError() {
        // Test with both null
        CommonResult<ConsumerInfoVO> result1 = incontrolVehicleDOServiceImplUnderTest.searchVinInfo(null, null);
        assertFalse(result1.isSuccess());
        assertEquals(ErrorCodeConstants.VIN_ICR_NOT_NULL.getCode(), result1.getCode());

        // Test with both empty strings
        CommonResult<ConsumerInfoVO> result2 = incontrolVehicleDOServiceImplUnderTest.searchVinInfo("", "");
        assertFalse(result2.isSuccess());
        assertEquals(ErrorCodeConstants.VIN_ICR_NOT_NULL.getCode(), result2.getCode());

        // Test with both blank strings
        CommonResult<ConsumerInfoVO> result3 = incontrolVehicleDOServiceImplUnderTest.searchVinInfo("   ", "   ");
        assertFalse(result3.isSuccess());
        assertEquals(ErrorCodeConstants.VIN_ICR_NOT_NULL.getCode(), result3.getCode());
    }

    @Test
    public void testSearchVinInfo_WithInvalidVinFormat_ShouldReturnError() {
        // Setup - invalid VIN format
        String invalidVin = "INVALID_VIN";
        String icr = "<EMAIL>";

        // Run the test
        final CommonResult<ConsumerInfoVO> result = incontrolVehicleDOServiceImplUnderTest.searchVinInfo(invalidVin, icr);

        // Verify the results
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.CAR_VIN_FORMAT_ERROR.getCode(), result.getCode());

        // Verify that no initialization methods were called for invalid VIN
        verify(mockVinInitializeService, never()).vinInitializeByQueryVin(anyString());
        verify(mockIncontrolVehicleDOMapper, never()).selectOneByCarVin(anyString());
    }

    @Test
    public void testSearchVinInfo_WithValidVinButNotFoundInDatabase_ShouldReturnError() {
        // Setup
        String validVin = "QWEQWE12345678901";
        String icr = "<EMAIL>";

        // Configure mocks - VIN initialization succeeds but vehicle not found in database
        when(mockVinInitializeService.vinInitializeByQueryVin(validVin)).thenReturn(null);
        when(mockIncontrolVehicleDOMapper.selectOneByCarVin(validVin)).thenReturn(null);

        // Run the test
        final CommonResult<ConsumerInfoVO> result = incontrolVehicleDOServiceImplUnderTest.searchVinInfo(validVin, icr);

        // Verify the results
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.ICR_NOT_FOUND.getCode(), result.getCode());

        // Verify that initialization was attempted
        verify(mockVinInitializeService).vinInitializeByQueryVin(validVin);
        verify(mockIncontrolVehicleDOMapper).selectOneByCarVin(validVin);
    }

    @Test
    public void testSearchVinInfo_WithEmptyVinAndValidIcr_ShouldCallICRPath() {
        // Setup
        String icr = "<EMAIL>";

        // Configure mocks for ICR path
        final VinsAndServiceDTO vinsAndServiceDTO = new VinsAndServiceDTO();
        vinsAndServiceDTO.setVin("QWEQWE12345678901");
        final List<VinsAndServiceDTO> vinList = List.of(vinsAndServiceDTO);
        when(mockRemoteCallService.getByICR(icr)).thenReturn(vinList);

        final IncontrolVehicleDO incontrolVehicleDO = IncontrolVehicleDO.builder()
                .id(1L)
                .carVin("QWEQWE12345678901")
                .incontrolId(icr)
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .incontrolPhone("incontrolPhone")
                .build();
        when(mockIncontrolVehicleDOMapper.selectListByICR(anyString())).thenReturn(List.of(incontrolVehicleDO));

        // Configure other required mocks
        final ConsumerIncontrolDO consumerIncontrolDO = new ConsumerIncontrolDO();
        consumerIncontrolDO.setConsumerCode("consumerCode");
        consumerIncontrolDO.setIncontrolId(icr);
        when(mockConsumerIncontrolMapper.selectNewByIncontrolId(icr)).thenReturn(consumerIncontrolDO);

        when(mockPiplDataUtil.getDecodeText("incontrolPhone")).thenReturn("result");
        when(mockPiplDataUtil.getEncryptText(icr)).thenReturn(icr);

        final ConsumerChannelDTO consumerChannelDTO = new ConsumerChannelDTO();
        consumerChannelDTO.setConsumerCode("consumerCode");
        final CommonResult<ConsumerChannelDTO> consumerChannelResult = CommonResult.success(consumerChannelDTO);
        when(mockConsumerApi.getConsumerChannelByCode("consumerCode")).thenReturn(consumerChannelResult);

        final Map<String, SeriesMappingVO> stringSeriesMappingVOMap = Map.ofEntries(
                Map.entry("seriesCode", SeriesMappingVO.builder()
                        .seriesName("seriesName")
                        .build()));
        when(mockSeriesBrandMappingDataDOService.getSeriesMapping()).thenReturn(stringSeriesMappingVOMap);

        final SubscriptionSearchLocalDTO subscriptionSearchLocalDTO = new SubscriptionSearchLocalDTO();
        subscriptionSearchLocalDTO.setCarSystemModel("carSystemModel");
        when(mockSubscriptionService.getExpireDateByVinLocal(any(IncontrolVehicleDO.class))).thenReturn(subscriptionSearchLocalDTO);

        // Run the test with empty VIN
        final CommonResult<ConsumerInfoVO> result = incontrolVehicleDOServiceImplUnderTest.searchVinInfo("", icr);

        // Verify the results
        assertTrue(result.isSuccess());
        verify(mockRemoteCallService).getByICR(icr);
        verify(mockVinInitializeService).vinInitializeByQueryICR(icr, vinList);
        // Should not call VIN-related methods since VIN is empty
        verify(mockVinInitializeService, never()).vinInitializeByQueryVin(anyString());
    }

    @Test
    public void testSearchVinInfo_WithICRButTSDPReturnsEmpty_ShouldReturnError() {
        // Setup
        String icr = "<EMAIL>";

        // Configure mocks - TSDP returns empty list
        when(mockRemoteCallService.getByICR(icr)).thenReturn(Collections.emptyList());

        // Run the test
        final CommonResult<ConsumerInfoVO> result = incontrolVehicleDOServiceImplUnderTest.searchVinInfo(null, icr);

        // Verify the results
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.ICR_FOUND_ERROR.getCode(), result.getCode());

        // Verify that TSDP was called and updateInControlIdNull was called
        verify(mockRemoteCallService).getByICR(icr);
        verify(mockVinInitializeService).updateInControlIdNull(icr);
    }

    @Test
    public void testSearchVinInfo_WithICRButTSDPThrowsException_ShouldReturnError() {
        // Setup
        String icr = "<EMAIL>";

        // Configure mocks - TSDP throws exception
        when(mockRemoteCallService.getByICR(icr)).thenThrow(new RuntimeException("TSDP service error"));

        // Run the test
        final CommonResult<ConsumerInfoVO> result = incontrolVehicleDOServiceImplUnderTest.searchVinInfo(null, icr);

        // Verify the results
        assertFalse(result.isSuccess());
        assertEquals(ErrorCodeConstants.ICR_FOUND_EMPTY.getCode(), result.getCode());

        // Verify that TSDP was called
        verify(mockRemoteCallService).getByICR(icr);
    }
}
