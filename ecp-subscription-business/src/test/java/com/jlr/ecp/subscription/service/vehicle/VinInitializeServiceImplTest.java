package com.jlr.ecp.subscription.service.vehicle;

import cn.hutool.core.lang.Snowflake;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.subscripiton.dto.ServicePackageDTO;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteQueryByVinRespDTO;
import com.jlr.ecp.subscription.dal.dataobject.appd.AppDCuRenewRecords;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.repository.*;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.service.icrorder.SeriesBrandMappingDataDOService;
import com.jlr.ecp.subscription.service.incontrol.IncontrolCustomerService;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.remotepackage.RemotePackageDOService;
import com.jlr.ecp.subscription.util.PIPLDataUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class VinInitializeServiceImplTest {

    @Mock
    private RemoteCallService mockRemoteCallService;
    @Mock
    private VehicleDmsRepository mockVehicleDmsRepository;
    @Mock
    private IncontrolVehicleRepository mockIncontrolVehicleRepository;
    @Mock
    private SeriesBrandMappingDataDOService mockSeriesBrandMappingDataDOService;
    @Mock
    private VehicleModelMasterDataService mockVehicleModelMasterDataService;
    @Mock
    private PIVIPackageRepository mockPiviPackageRepository;
    @Mock
    private SubscriptionServiceRepository mockSubscriptionServiceRepository;
    @Mock
    private RemotePackageDOService mockRemotePackageDOService;
    @Mock
    private AppDCuRenewRecordsRepository mockAppDCuRenewRecordsRepository;
    @Mock
    private IncontrolCustomerService mockIncontrolCustomerService;
    @Mock
    private Snowflake mockEcpIdUtil;
    @Mock
    private PIPLDataUtil mockPiplDataUtil;
    @Mock
    private ApplicationContext mockApplicationContext;

    private VinInitializeServiceImpl vinInitializeServiceImplUnderTest;

    @Before
    public void setUp() {
        vinInitializeServiceImplUnderTest = new VinInitializeServiceImpl();
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "remoteCallService", mockRemoteCallService);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "vehicleDmsRepository", mockVehicleDmsRepository);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "incontrolVehicleRepository", mockIncontrolVehicleRepository);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "seriesBrandMappingDataDOService", mockSeriesBrandMappingDataDOService);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "vehicleModelMasterDataService", mockVehicleModelMasterDataService);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "piviPackageRepository", mockPiviPackageRepository);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "subscriptionServiceRepository", mockSubscriptionServiceRepository);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "remotePackageDOService", mockRemotePackageDOService);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "appDCuRenewRecordsRepository", mockAppDCuRenewRecordsRepository);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "incontrolCustomerService", mockIncontrolCustomerService);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "ecpIdUtil", mockEcpIdUtil);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "piplDataUtil", mockPiplDataUtil);
        ReflectionTestUtils.setField(vinInitializeServiceImplUnderTest, "applicationContext", mockApplicationContext);
    }

    @Test
    public void testVinInitializeByLogin_Success() {
        // Setup
        String inControlId = "<EMAIL>";
        String jlrId = "jlrId";
        List<VinsAndServiceDTO> mockTSDPData = createMockVinsAndServiceDTOList();
        
        when(mockRemoteCallService.getByICR(inControlId)).thenReturn(mockTSDPData);
        when(mockApplicationContext.getBean(VinInitializeServiceImpl.class)).thenReturn(vinInitializeServiceImplUnderTest);

        // Run the test
        vinInitializeServiceImplUnderTest.vinInitializeByLogin(inControlId, jlrId);

        // Verify
        verify(mockRemoteCallService).getByICR(inControlId);
        verify(mockApplicationContext).getBean(VinInitializeServiceImpl.class);
    }

    @Test
    public void testVinInitializeByLogin_EmptyTSDPData() {
        // Setup
        String inControlId = "<EMAIL>";
        String jlrId = "jlrId";
        List<IncontrolVehicleDO> mockVehicleList = createMockIncontrolVehicleDOList();
        
        when(mockRemoteCallService.getByICR(inControlId)).thenReturn(Collections.emptyList());
        when(mockIncontrolVehicleRepository.selectListByICR(inControlId)).thenReturn(mockVehicleList);

        // Run the test
        vinInitializeServiceImplUnderTest.vinInitializeByLogin(inControlId, jlrId);

        // Verify
        verify(mockRemoteCallService).getByICR(inControlId);
        verify(mockIncontrolVehicleRepository).selectListByICR(inControlId);
        verify(mockIncontrolVehicleRepository).updateBatchWithNulls(anyList());
    }

    @Test
    public void testVinInitializeByLogin_ServiceException() {
        // Setup
        String inControlId = "<EMAIL>";
        String jlrId = "jlrId";

        when(mockRemoteCallService.getByICR(inControlId)).thenThrow(new ServiceException(ErrorCodeConstants.TSDP_REQUEST_RATE_TOO_FAST));

        // Run the test
        vinInitializeServiceImplUnderTest.vinInitializeByLogin(inControlId, jlrId);

        // Verify
        verify(mockRemoteCallService).getByICR(inControlId);
        verify(mockRemoteCallService).save2DelayQue(anyString());
    }

    @Test
    public void testVinInitializeByLogin_RestClientException() {
        // Setup
        String inControlId = "<EMAIL>";
        String jlrId = "jlrId";
        
        when(mockRemoteCallService.getByICR(inControlId)).thenThrow(new RestClientException("Connection failed"));

        // Run the test
        vinInitializeServiceImplUnderTest.vinInitializeByLogin(inControlId, jlrId);

        // Verify
        verify(mockRemoteCallService).getByICR(inControlId);
        verify(mockRemoteCallService).save2DelayQue(anyString());
    }

    @Test
    public void testVinInitializeByQueryICR_Success() {
        // Setup
        String inControlId = "<EMAIL>";
        List<VinsAndServiceDTO> infoListFromTSDP = createMockVinsAndServiceDTOList();
        List<PIVIPackageDO> mockPiviPackages = createMockPIVIPackageDOList();
        List<VehicleDmsDO> mockDmsData = createMockVehicleDmsDOList();
        List<IncontrolVehicleDO> mockExistingVehicles = Collections.emptyList();
        List<UserDPResultVO> mockDpResults = createMockUserDPResultVOList();
        List<SubscriptionServiceDO> mockServices = createMockSubscriptionServiceDOList();
        
        when(mockPiviPackageRepository.selectPIVIPackageDOByCarVinList(anyList())).thenReturn(mockPiviPackages);
        when(mockVehicleDmsRepository.findByCarVinList(anyList())).thenReturn(mockDmsData);
        when(mockIncontrolVehicleRepository.selectByCarVinList(anyList())).thenReturn(mockExistingVehicles);
        when(mockVehicleModelMasterDataService.findDpListByVinInit(anyList())).thenReturn(mockDpResults);
        when(mockIncontrolVehicleRepository.selectListByICR(inControlId)).thenReturn(Collections.emptyList());
        when(mockEcpIdUtil.nextIdStr()).thenReturn("123456789");

        // Run the test
        vinInitializeServiceImplUnderTest.vinInitializeByQueryICR(inControlId, infoListFromTSDP);

        // Verify
        verify(mockPiviPackageRepository).selectPIVIPackageDOByCarVinList(anyList());
        verify(mockIncontrolVehicleRepository).saveBatch(anyList());
        verify(mockSubscriptionServiceRepository).saveBatch(anyList());
    }

    @Test
    public void testVinInitializeByQueryVin_NewVehicle() {
        // Setup
        String carVin = "TESTVIN1234567890";
        RemoteQueryByVinRespDTO mockResponse = createMockRemoteQueryByVinRespDTO();
        PIVIPackageDO mockPiviPackage = createMockPIVIPackageDO();
        UserDPResultVO mockDpResult = createMockUserDPResultVO();
        Map<String, String> mockEncryptMap = Map.of("phone", "encryptedPhone");
        
        when(mockRemoteCallService.getByVin(carVin)).thenReturn(mockResponse);
        when(mockPiplDataUtil.getEncryptListText(anySet())).thenReturn(mockEncryptMap);
        when(mockPiviPackageRepository.selectOneByCarVin(carVin)).thenReturn(mockPiviPackage);
        when(mockIncontrolVehicleRepository.selectOneByCarVin(carVin)).thenReturn(null);
        when(mockVehicleModelMasterDataService.findDp(carVin)).thenReturn(mockDpResult);

        // Run the test
        String inControlId = vinInitializeServiceImplUnderTest.vinInitializeByQueryVin(carVin);

        // Verify
        verify(mockRemoteCallService).getByVin(carVin);
        verify(mockIncontrolVehicleRepository).insert(any(IncontrolVehicleDO.class));
        verify(mockIncontrolCustomerService).saveOrUpdateIncontrolCustomerByQueryVin(anyString(), anyString(), anyString(), anyString());
        assertTrue(Objects.nonNull(inControlId));
    }

    @Test
    public void testVinInitializeByQueryVin_ExistingVehicle() {
        // Setup
        String carVin = "TESTVIN1234567890";
        RemoteQueryByVinRespDTO mockResponse = createMockRemoteQueryByVinRespDTO();
        PIVIPackageDO mockPiviPackage = createMockPIVIPackageDO();
        IncontrolVehicleDO mockExistingVehicle = createMockIncontrolVehicleDO();
        mockExistingVehicle.setRevision(1);
        Map<String, String> mockEncryptMap = Map.of("phone", "encryptedPhone");
        
        when(mockRemoteCallService.getByVin(carVin)).thenReturn(mockResponse);
        when(mockPiplDataUtil.getEncryptListText(anySet())).thenReturn(mockEncryptMap);
        when(mockPiviPackageRepository.selectOneByCarVin(carVin)).thenReturn(mockPiviPackage);
        when(mockIncontrolVehicleRepository.selectOneByCarVin(carVin)).thenReturn(mockExistingVehicle);

        // Run the test
        String inControlId = vinInitializeServiceImplUnderTest.vinInitializeByQueryVin(carVin);

        // Verify
        verify(mockRemoteCallService).getByVin(carVin);
        verify(mockIncontrolVehicleRepository).updateBatchWithNulls(anyList());
        verify(mockIncontrolCustomerService).saveOrUpdateIncontrolCustomerByQueryVin(anyString(), anyString(), anyString(), anyString());
        assertFalse(Objects.isNull(inControlId));
    }

    @Test
    public void testVinInitializeByQueryVin_NoCustomer() {
        // Setup
        String carVin = "TESTVIN1234567890";
        RemoteQueryByVinRespDTO mockResponse = new RemoteQueryByVinRespDTO();
        mockResponse.setBoundToCustomer(null);
        mockResponse.setSubscriptions(Collections.emptyList());
        PIVIPackageDO mockPiviPackage = createMockPIVIPackageDO();
        UserDPResultVO mockDpResult = createMockUserDPResultVO();
        Map<String, String> mockEncryptMap = Collections.emptyMap();
        
        when(mockRemoteCallService.getByVin(carVin)).thenReturn(mockResponse);
        when(mockPiplDataUtil.getEncryptListText(anySet())).thenReturn(mockEncryptMap);
        when(mockPiviPackageRepository.selectOneByCarVin(carVin)).thenReturn(mockPiviPackage);
        when(mockIncontrolVehicleRepository.selectOneByCarVin(carVin)).thenReturn(null);
        when(mockVehicleModelMasterDataService.findDp(carVin)).thenReturn(mockDpResult);

        // Run the test
        String inControlId = vinInitializeServiceImplUnderTest.vinInitializeByQueryVin(carVin);

        // Verify
        verify(mockRemoteCallService).getByVin(carVin);
        verify(mockIncontrolVehicleRepository).insert(any(IncontrolVehicleDO.class));
        assertTrue(Objects.isNull(inControlId));
    }

    @Test
    public void testGetUpdateVehicleDO_DifferentInControlId() {
        // Setup
        String carVin = "TESTVIN1234567890";
        IncontrolVehicleDO vehicleDO = createMockIncontrolVehicleDO();
        vehicleDO.setIncontrolId("<EMAIL>");
        vehicleDO.setRevision(1);
        String newInControlId = "<EMAIL>";
        String encryptPhone = "encryptedPhone";
        PIVIPackageDO piviPackageDO = createMockPIVIPackageDO();
        
        // Run the test
        vinInitializeServiceImplUnderTest.getUpdateVehicleDO(carVin, vehicleDO, newInControlId, encryptPhone, piviPackageDO);

        // Verify
        assertThat(vehicleDO.getIncontrolId()).isEqualTo(newInControlId);
        assertThat(vehicleDO.getIncontrolPhone()).isEqualTo(encryptPhone);
        assertNotNull(vehicleDO.getBindTime());
    }

    @Test
    public void testGetInsertVehicleDO_Success() {
        // Setup
        String carVin = "TESTVIN1234567890";
        String inControlId = "<EMAIL>";
        PIVIPackageDO piviPackageDO = createMockPIVIPackageDO();
        String encryptPhone = "encryptedPhone";
        UserDPResultVO dpResultVO = createMockUserDPResultVO();
        
        // Run the test
        IncontrolVehicleDO result = vinInitializeServiceImplUnderTest.getInsertVehicleDO(carVin, inControlId, piviPackageDO, encryptPhone, dpResultVO);

        // Verify
        verify(mockSeriesBrandMappingDataDOService).createSeriesBrandMappingData(dpResultVO);
        assertThat(result.getCarVin()).isEqualTo(carVin);
        assertThat(result.getIncontrolId()).isEqualTo(inControlId);
        assertThat(result.getIncontrolPhone()).isEqualTo(encryptPhone);
        assertNotNull(result.getBindTime());
    }

    @Test
    public void testUpdateInControlIdNull_Success() {
        // Setup
        String inControlId = "<EMAIL>";
        List<IncontrolVehicleDO> mockVehicleList = createMockIncontrolVehicleDOList();
        
        when(mockIncontrolVehicleRepository.selectListByICR(inControlId)).thenReturn(mockVehicleList);

        // Run the test
        vinInitializeServiceImplUnderTest.updateInControlIdNull(inControlId);

        // Verify
        verify(mockIncontrolVehicleRepository).selectListByICR(inControlId);
        verify(mockIncontrolVehicleRepository).updateBatchWithNulls(anyList());
    }

    @Test
    public void testUpdateInControlIdNull_EmptyList() {
        // Setup
        String inControlId = "<EMAIL>";
        
        when(mockIncontrolVehicleRepository.selectListByICR(inControlId)).thenReturn(Collections.emptyList());

        // Run the test
        vinInitializeServiceImplUnderTest.updateInControlIdNull(inControlId);

        // Verify
        verify(mockIncontrolVehicleRepository).selectListByICR(inControlId);
        verify(mockIncontrolVehicleRepository, never()).updateBatchWithNulls(anyList());
    }

    // Helper methods for creating mock data
    private List<VinsAndServiceDTO> createMockVinsAndServiceDTOList() {
        VinsAndServiceDTO dto = new VinsAndServiceDTO();
        dto.setVin("TESTVIN1234567890");
        
        ServicePackageDTO servicePackage = new ServicePackageDTO();
        servicePackage.setServicePackageName("TestPackage");
        servicePackage.setServiceName("TestService");
        servicePackage.setExpireDate(LocalDateTime.of(2025, 12, 31, 23, 59, 59));
        servicePackage.setExpireDateUTC0(LocalDateTime.of(2025, 12, 31, 15, 59, 59));

        dto.setServicePackage(Arrays.asList(servicePackage));
        return Arrays.asList(dto);
    }

    private List<PIVIPackageDO> createMockPIVIPackageDOList() {
        PIVIPackageDO piviPackage = createMockPIVIPackageDO();
        return Arrays.asList(piviPackage);
    }

    private PIVIPackageDO createMockPIVIPackageDO() {
        return PIVIPackageDO.builder()
                .packageCode("TestPackage")
                .jlrSubscriptionId(123456L)
                .iccid("1234567890123456789")
                .vin("TESTVIN1234567890")
                .amaPExpireDate(LocalDateTime.of(2025, 12, 31, 23, 59, 59))
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2025, 12, 31, 23, 59, 59))
                .build();
    }

    private List<VehicleDmsDO> createMockVehicleDmsDOList() {
        VehicleDmsDO dms = createMockVehicleDmsDO();
        return Arrays.asList(dms);
    }

    private VehicleDmsDO createMockVehicleDmsDO() {
        return VehicleDmsDO.builder()
                .carVin("TESTVIN1234567890")
                .invoiceDate("2020-01-01")
                .build();
    }

    private List<IncontrolVehicleDO> createMockIncontrolVehicleDOList() {
        IncontrolVehicleDO vehicle = createMockIncontrolVehicleDO();
        vehicle.setRevision(1);
        return Arrays.asList(vehicle);
    }

    private IncontrolVehicleDO createMockIncontrolVehicleDO() {
        return IncontrolVehicleDO.builder()
                .carVin("TESTVIN1234567890")
                .incontrolId("<EMAIL>")
                .incontrolPhone("encryptedPhone")
                .brandCode("JLR")
                .brandName("Jaguar Land Rover")
                .configCode("CONFIG123")
                .configName("Test Config")
                .seriesCode("SERIES123")
                .seriesName("Test Series")
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .bindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build();
    }

    private List<UserDPResultVO> createMockUserDPResultVOList() {
        UserDPResultVO dp = createMockUserDPResultVO();
        return Arrays.asList(dp);
    }

    private UserDPResultVO createMockUserDPResultVO() {
        UserDPResultVO dpResult = new UserDPResultVO();
        dpResult.setVin("TESTVIN1234567890");
        dpResult.setBrandCode("JLR");
        dpResult.setBrandName("Jaguar Land Rover");
        dpResult.setConfigCode("CONFIG123");
        dpResult.setConfigName("Test Config");
        dpResult.setSeriesCode("SERIES123");
        dpResult.setSeriesName("Test Series");
        dpResult.setCarSystemModel("PIVI");
        dpResult.setQueryResult("");
        return dpResult;
    }

    private List<SubscriptionServiceDO> createMockSubscriptionServiceDOList() {
        SubscriptionServiceDO service = SubscriptionServiceDO.builder()
                .carVin("TESTVIN1234567890")
                .serviceType(1)
                .serviceName("TestService")
                .servicePackage("TestPackage")
                .expireDateUtc0(LocalDateTime.of(2025, 12, 31, 23, 59, 59))
                .build();
        return Arrays.asList(service);
    }

    private RemoteQueryByVinRespDTO createMockRemoteQueryByVinRespDTO() {
        RemoteQueryByVinRespDTO response = new RemoteQueryByVinRespDTO();
        response.setVin("TESTVIN1234567890");

        RemoteQueryByVinRespDTO.BoundToCustomer customer = new RemoteQueryByVinRespDTO.BoundToCustomer();
        customer.setEmail("<EMAIL>");
        customer.setPhone("1234567890");
        customer.setFirstName("John");
        customer.setSurname("Doe");
        response.setBoundToCustomer(customer);

        RemoteQueryByVinRespDTO.Subscription subscription = new RemoteQueryByVinRespDTO.Subscription();
        subscription.setServicePackage("TestPackage");
        subscription.setExpiryDate(LocalDateTime.of(2025, 12, 31, 23, 59, 59));
        subscription.setServices(Arrays.asList("TestService"));
        response.setSubscriptions(Arrays.asList(subscription));

        return response;
    }

    @Test
    public void testQueryDPByVin_Success() throws Exception {
        // Setup
        String carVin = "TESTVIN1234567890";
        UserDPResultVO mockDpResult = createMockUserDPResultVO();
        mockDpResult.setQueryResult("");
        mockDpResult.setConfigCode("CONFIG123");

        when(mockVehicleModelMasterDataService.findDp(carVin)).thenReturn(mockDpResult);

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("queryDPByVin", String.class);
        method.setAccessible(true);

        // Run the test
        UserDPResultVO result = (UserDPResultVO) method.invoke(vinInitializeServiceImplUnderTest, carVin);

        // Verify
        verify(mockVehicleModelMasterDataService).findDp(carVin);
        assertThat(result).isNotNull();
        assertThat(result.getConfigCode()).isEqualTo("CONFIG123");
    }

    @Test
    public void testQueryDPByVin_WithQueryResult() throws Exception {
        // Setup
        String carVin = "TESTVIN1234567890";
        UserDPResultVO mockDpResult = createMockUserDPResultVO();
        mockDpResult.setQueryResult("ERROR");

        when(mockVehicleModelMasterDataService.findDp(carVin)).thenReturn(mockDpResult);

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("queryDPByVin", String.class);
        method.setAccessible(true);

        // Run the test and expect exception
        assertThatThrownBy(() -> method.invoke(vinInitializeServiceImplUnderTest, carVin))
                .hasCauseInstanceOf(ServiceException.class);

        // Verify
        verify(mockVehicleModelMasterDataService).findDp(carVin);
    }

    @Test
    public void testQueryDPByVin_EmptyConfigCode() throws Exception {
        // Setup
        String carVin = "TESTVIN1234567890";
        UserDPResultVO mockDpResult = createMockUserDPResultVO();
        mockDpResult.setQueryResult("");
        mockDpResult.setConfigCode("");

        when(mockVehicleModelMasterDataService.findDp(carVin)).thenReturn(mockDpResult);

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("queryDPByVin", String.class);
        method.setAccessible(true);

        // Run the test and expect exception
        assertThatThrownBy(() -> method.invoke(vinInitializeServiceImplUnderTest, carVin))
                .hasCauseInstanceOf(ServiceException.class);

        // Verify
        verify(mockVehicleModelMasterDataService).findDp(carVin);
    }

    @Test
    public void testBuildAndSaveVehicle_WithExistingVehicles() {
        // Setup
        String inControlId = "<EMAIL>";
        List<String> carVinList = Arrays.asList("TESTVIN1234567890");
        Map<String, PIVIPackageDO> piviPackageDOMap = Map.of("TESTVIN1234567890", createMockPIVIPackageDO());

        List<VehicleDmsDO> mockDmsData = createMockVehicleDmsDOList();
        List<IncontrolVehicleDO> mockExistingVehicles = createMockIncontrolVehicleDOList();

        when(mockVehicleDmsRepository.findByCarVinList(carVinList)).thenReturn(mockDmsData);
        when(mockIncontrolVehicleRepository.selectByCarVinList(carVinList)).thenReturn(mockExistingVehicles);
        when(mockIncontrolVehicleRepository.selectListByICR(inControlId)).thenReturn(Collections.emptyList());

        // Run the test
        vinInitializeServiceImplUnderTest.buildAndSaveVehicle(inControlId, carVinList, piviPackageDOMap);

        // Verify
        verify(mockVehicleDmsRepository).findByCarVinList(carVinList);
        verify(mockIncontrolVehicleRepository).selectByCarVinList(carVinList);
        verify(mockIncontrolVehicleRepository).updateBatchWithNulls(anyList());
    }

    @Test
    public void testBuildAndSaveVehicle_WithNewVehicles() {
        // Setup
        String inControlId = "<EMAIL>";
        List<String> carVinList = Arrays.asList("TESTVIN1234567890");
        Map<String, PIVIPackageDO> piviPackageDOMap = Map.of("TESTVIN1234567890", createMockPIVIPackageDO());

        List<VehicleDmsDO> mockDmsData = createMockVehicleDmsDOList();
        List<UserDPResultVO> mockDpResults = createMockUserDPResultVOList();

        when(mockVehicleDmsRepository.findByCarVinList(carVinList)).thenReturn(mockDmsData);
        when(mockIncontrolVehicleRepository.selectByCarVinList(carVinList)).thenReturn(Collections.emptyList());
        when(mockVehicleModelMasterDataService.findDpListByVinInit(carVinList)).thenReturn(mockDpResults);
        when(mockIncontrolVehicleRepository.selectListByICR(inControlId)).thenReturn(Collections.emptyList());

        // Run the test
        vinInitializeServiceImplUnderTest.buildAndSaveVehicle(inControlId, carVinList, piviPackageDOMap);

        // Verify
        verify(mockVehicleDmsRepository).findByCarVinList(carVinList);
        verify(mockIncontrolVehicleRepository).selectByCarVinList(carVinList);
        verify(mockVehicleModelMasterDataService).findDpListByVinInit(carVinList);
        verify(mockIncontrolVehicleRepository).saveBatch(anyList());
    }

    @Test
    public void testBuildAndSaveVehicle_WithUnbindVehicles() {
        // Setup
        String inControlId = "<EMAIL>";
        List<String> carVinList = Arrays.asList("TESTVIN1234567890");
        Map<String, PIVIPackageDO> piviPackageDOMap = Map.of("TESTVIN1234567890", createMockPIVIPackageDO());

        List<VehicleDmsDO> mockDmsData = createMockVehicleDmsDOList();
        List<IncontrolVehicleDO> mockBoundVehicles = createMockIncontrolVehicleDOList();
        mockBoundVehicles.get(0).setCarVin("OTHERVINTOUNBIND");

        when(mockVehicleDmsRepository.findByCarVinList(carVinList)).thenReturn(mockDmsData);
        when(mockIncontrolVehicleRepository.selectByCarVinList(carVinList)).thenReturn(Collections.emptyList());
        when(mockIncontrolVehicleRepository.selectListByICR(inControlId)).thenReturn(mockBoundVehicles);

        // Run the test
        vinInitializeServiceImplUnderTest.buildAndSaveVehicle(inControlId, carVinList, piviPackageDOMap);

        // Verify
        verify(mockVehicleDmsRepository).findByCarVinList(carVinList);
        verify(mockIncontrolVehicleRepository).selectByCarVinList(carVinList);
        verify(mockIncontrolVehicleRepository).selectListByICR(inControlId);
        verify(mockIncontrolVehicleRepository).updateBatchWithNulls(anyList());
    }

    @Test
    public void testBuildAndSaveOnlineServices_WithNewVins() {
        // Setup
        List<String> carVinList = Arrays.asList("TESTVIN1234567890");
        String inControlId = "<EMAIL>";
        Map<String, PIVIPackageDO> piviPackageDOMap = Map.of("TESTVIN1234567890", createMockPIVIPackageDO());
        List<SubscriptionServiceDO> onlineServices = Collections.emptyList();
        List<AppDCuRenewRecords> renewRecords = Collections.emptyList();

        when(mockAppDCuRenewRecordsRepository.getByVinListAndStatus(anyList(), anyInt(), any(), any()))
                .thenReturn(renewRecords);
        when(mockEcpIdUtil.nextIdStr()).thenReturn("123456789");

        // Run the test
        vinInitializeServiceImplUnderTest.buildAndSaveOnlineServices(carVinList, inControlId, piviPackageDOMap, onlineServices);

        // Verify
        verify(mockAppDCuRenewRecordsRepository).getByVinListAndStatus(anyList(), anyInt(), any(), any());
        verify(mockSubscriptionServiceRepository).saveBatch(anyList());
    }

    @Test
    public void testBuildAndSaveOnlineServices_NoNewVins() {
        // Setup
        List<String> carVinList = Arrays.asList("TESTVIN1234567890");
        String inControlId = "<EMAIL>";
        Map<String, PIVIPackageDO> piviPackageDOMap = Map.of("TESTVIN1234567890", createMockPIVIPackageDO());

        SubscriptionServiceDO existingService = SubscriptionServiceDO.builder()
                .carVin("TESTVIN1234567890")
                .serviceType(3) // PIVI service type
                .build();
        List<SubscriptionServiceDO> onlineServices = Arrays.asList(existingService);

        // Run the test
        vinInitializeServiceImplUnderTest.buildAndSaveOnlineServices(carVinList, inControlId, piviPackageDOMap, onlineServices);

        // Verify - should not call save methods since no new VINs
        verify(mockSubscriptionServiceRepository, never()).saveBatch(anyList());
    }

    @Test
    public void testGetUpdateVehicleDO_SameInControlId() {
        // Setup
        String carVin = "TESTVIN1234567890";
        IncontrolVehicleDO vehicleDO = createMockIncontrolVehicleDO();
        vehicleDO.setIncontrolId("<EMAIL>");
        vehicleDO.setRevision(1);
        String sameInControlId = "<EMAIL>";
        String encryptPhone = "encryptedPhone";
        PIVIPackageDO piviPackageDO = createMockPIVIPackageDO();
        LocalDateTime originalBindTime = vehicleDO.getBindTime();

        // Run the test
        vinInitializeServiceImplUnderTest.getUpdateVehicleDO(carVin, vehicleDO, sameInControlId, encryptPhone, piviPackageDO);

        // Verify - InControlId should not change, bindTime should remain the same
        assertThat(vehicleDO.getIncontrolId()).isEqualTo(sameInControlId);
        assertThat(vehicleDO.getIncontrolPhone()).isEqualTo(encryptPhone);
        assertThat(vehicleDO.getBindTime()).isEqualTo(originalBindTime);
    }

    @Test
    public void testGetUpdateVehicleDO_NullInControlId() {
        // Setup
        String carVin = "TESTVIN1234567890";
        IncontrolVehicleDO vehicleDO = createMockIncontrolVehicleDO();
        vehicleDO.setIncontrolId(null);
        vehicleDO.setRevision(1);
        String newInControlId = "<EMAIL>";
        String encryptPhone = "encryptedPhone";
        PIVIPackageDO piviPackageDO = createMockPIVIPackageDO();

        // Run the test
        vinInitializeServiceImplUnderTest.getUpdateVehicleDO(carVin, vehicleDO, newInControlId, encryptPhone, piviPackageDO);

        // Verify
        assertThat(vehicleDO.getIncontrolId()).isEqualTo(newInControlId);
        assertThat(vehicleDO.getIncontrolPhone()).isEqualTo(encryptPhone);
        assertNotNull(vehicleDO.getBindTime());
    }

    @Test
    public void testSaveOrUpdateSubscriptionServices_WithExistingServices() {
        // Setup
        String carVin = "TESTVIN1234567890";
        String inControlId = "<EMAIL>";
        RemoteQueryByVinRespDTO vinRespDTO = createMockRemoteQueryByVinRespDTO();
        PIVIPackageDO piviPackageDO = createMockPIVIPackageDO();

        List<SubscriptionServiceDO> existingServices = createMockSubscriptionServiceDOList();
        List<String> existingPackageCodes = Arrays.asList("TestPackage");
        List<AppDCuRenewRecords> renewRecords = Collections.emptyList();

        when(mockSubscriptionServiceRepository.selectByCarVin(carVin)).thenReturn(existingServices);
        when(mockRemotePackageDOService.getExistPackageCode(anyList())).thenReturn(existingPackageCodes);
        when(mockAppDCuRenewRecordsRepository.getByVinAndStatus(eq(carVin), anyInt())).thenReturn(renewRecords);
        when(mockEcpIdUtil.nextIdStr()).thenReturn("123456789");

        // Use reflection to call private method
        try {
            java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod(
                    "saveOrUpdateSubscriptionServices", String.class, String.class, RemoteQueryByVinRespDTO.class, PIVIPackageDO.class);
            method.setAccessible(true);
            method.invoke(vinInitializeServiceImplUnderTest, carVin, inControlId, vinRespDTO, piviPackageDO);
        } catch (Exception e) {
            // Handle reflection exceptions
        }

        // Verify
        verify(mockSubscriptionServiceRepository).saveOrUpdateBatch(anyList());
    }

    @Test
    public void testBuildAndSaveRemoteServices_Success() {
        // Setup
        String inControlId = "<EMAIL>";
        List<VinsAndServiceDTO> infoListFromTSDP = createMockVinsAndServiceDTOList();
        List<SubscriptionServiceDO> remoteServices = Collections.emptyList();
        List<String> existingPackageCodes = Arrays.asList("TestPackage");

        when(mockRemotePackageDOService.getExistPackageCode(anyList())).thenReturn(existingPackageCodes);
        when(mockEcpIdUtil.nextIdStr()).thenReturn("123456789");

        // Run the test
        vinInitializeServiceImplUnderTest.buildAndSaveRemoteServices(inControlId, infoListFromTSDP, remoteServices);

        // Verify
        verify(mockRemotePackageDOService).getExistPackageCode(anyList());
    }

    @Test
    public void testVinInitializeByQueryICR_WithEmptyDpResults() {
        // Setup
        String inControlId = "<EMAIL>";
        List<VinsAndServiceDTO> infoListFromTSDP = createMockVinsAndServiceDTOList();
        List<PIVIPackageDO> mockPiviPackages = createMockPIVIPackageDOList();
        List<VehicleDmsDO> mockDmsData = createMockVehicleDmsDOList();
        List<IncontrolVehicleDO> mockExistingVehicles = Collections.emptyList();
        List<UserDPResultVO> emptyDpResults = Collections.emptyList();
        List<SubscriptionServiceDO> mockServices = createMockSubscriptionServiceDOList();

        when(mockPiviPackageRepository.selectPIVIPackageDOByCarVinList(anyList())).thenReturn(mockPiviPackages);
        when(mockVehicleDmsRepository.findByCarVinList(anyList())).thenReturn(mockDmsData);
        when(mockIncontrolVehicleRepository.selectByCarVinList(anyList())).thenReturn(mockExistingVehicles);
        when(mockVehicleModelMasterDataService.findDpListByVinInit(anyList())).thenReturn(emptyDpResults);
        when(mockIncontrolVehicleRepository.selectListByICR(inControlId)).thenReturn(Collections.emptyList());

        // Run the test
        vinInitializeServiceImplUnderTest.vinInitializeByQueryICR(inControlId, infoListFromTSDP);

        // Verify
        verify(mockVehicleModelMasterDataService).findDpListByVinInit(anyList());
    }

    @Test
    public void testVinInitializeByQueryVin_DpError() {
        // Setup
        String carVin = "TESTVIN1234567890";
        RemoteQueryByVinRespDTO mockResponse = createMockRemoteQueryByVinRespDTO();
        PIVIPackageDO mockPiviPackage = createMockPIVIPackageDO();
        UserDPResultVO mockDpResult = createMockUserDPResultVO();
        mockDpResult.setQueryResult("ERROR");
        Map<String, String> mockEncryptMap = Map.of("phone", "encryptedPhone");

        when(mockRemoteCallService.getByVin(carVin)).thenReturn(mockResponse);
        when(mockPiplDataUtil.getEncryptListText(anySet())).thenReturn(mockEncryptMap);
        when(mockPiviPackageRepository.selectOneByCarVin(carVin)).thenReturn(mockPiviPackage);
        when(mockIncontrolVehicleRepository.selectOneByCarVin(carVin)).thenReturn(null);
        when(mockVehicleModelMasterDataService.findDp(carVin)).thenReturn(mockDpResult);

        // Run the test and expect exception
        assertThatThrownBy(() -> vinInitializeServiceImplUnderTest.vinInitializeByQueryVin(carVin))
                .isInstanceOf(ServiceException.class);

        // Verify
        verify(mockRemoteCallService).getByVin(carVin);
        verify(mockVehicleModelMasterDataService).findDp(carVin);
    }

    @Test
    public void testVinInitializeByQueryVin_EmptyConfigCode() {
        // Setup
        String carVin = "TESTVIN1234567890";
        RemoteQueryByVinRespDTO mockResponse = createMockRemoteQueryByVinRespDTO();
        PIVIPackageDO mockPiviPackage = createMockPIVIPackageDO();
        UserDPResultVO mockDpResult = createMockUserDPResultVO();
        mockDpResult.setQueryResult("");
        mockDpResult.setConfigCode("");
        Map<String, String> mockEncryptMap = Map.of("phone", "encryptedPhone");

        when(mockRemoteCallService.getByVin(carVin)).thenReturn(mockResponse);
        when(mockPiplDataUtil.getEncryptListText(anySet())).thenReturn(mockEncryptMap);
        when(mockPiviPackageRepository.selectOneByCarVin(carVin)).thenReturn(mockPiviPackage);
        when(mockIncontrolVehicleRepository.selectOneByCarVin(carVin)).thenReturn(null);
        when(mockVehicleModelMasterDataService.findDp(carVin)).thenReturn(mockDpResult);

        // Run the test and expect exception
        assertThatThrownBy(() -> vinInitializeServiceImplUnderTest.vinInitializeByQueryVin(carVin))
                .isInstanceOf(ServiceException.class);

        // Verify
        verify(mockRemoteCallService).getByVin(carVin);
        verify(mockVehicleModelMasterDataService).findDp(carVin);
    }

    @Test
    public void testGetInsertVehicleDO_WithNullPiviPackage() {
        // Setup
        String carVin = "TESTVIN1234567890";
        String inControlId = "<EMAIL>";
        PIVIPackageDO piviPackageDO = null;
        String encryptPhone = "encryptedPhone";
        UserDPResultVO dpResultVO = createMockUserDPResultVO();

        when(mockVehicleDmsRepository.findByCarVin(carVin)).thenReturn(null);

        // Run the test
        IncontrolVehicleDO result = vinInitializeServiceImplUnderTest.getInsertVehicleDO(carVin, inControlId, piviPackageDO, encryptPhone, dpResultVO);

        // Verify
        verify(mockSeriesBrandMappingDataDOService).createSeriesBrandMappingData(dpResultVO);
        assertThat(result.getCarVin()).isEqualTo(carVin);
        assertThat(result.getIncontrolId()).isEqualTo(inControlId);
        assertThat(result.getIncontrolPhone()).isEqualTo(encryptPhone);
        assertNull(result.getDmsInvoiceDate());
    }

    @Test
    public void testGetDmsInvoiceDate_WithPiviPackage() throws Exception {
        // Setup
        String carVin = "TESTVIN1234567890";
        PIVIPackageDO piviPackageDO = createMockPIVIPackageDO();

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("getDmsInvoiceDate", String.class, PIVIPackageDO.class);
        method.setAccessible(true);

        // Run the test
        LocalDateTime result = (LocalDateTime) method.invoke(vinInitializeServiceImplUnderTest, carVin, piviPackageDO);

        // Verify
        assertThat(result).isEqualTo(piviPackageDO.getDmsInvoiceDate());
    }

    @Test
    public void testGetDmsInvoiceDate_WithoutPiviPackage() throws Exception {
        // Setup
        String carVin = "TESTVIN1234567890";
        PIVIPackageDO piviPackageDO = null;
        VehicleDmsDO mockDmsData = createMockVehicleDmsDO();

        when(mockVehicleDmsRepository.findByCarVin(carVin)).thenReturn(mockDmsData);

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("getDmsInvoiceDate", String.class, PIVIPackageDO.class);
        method.setAccessible(true);

        // Run the test
        LocalDateTime result = (LocalDateTime) method.invoke(vinInitializeServiceImplUnderTest, carVin, piviPackageDO);

        // Verify
        verify(mockVehicleDmsRepository).findByCarVin(carVin);
    }

    @Test
    public void testGetDmsInvoiceDate_NoDmsData() throws Exception {
        // Setup
        String carVin = "TESTVIN1234567890";
        PIVIPackageDO piviPackageDO = null;

        when(mockVehicleDmsRepository.findByCarVin(carVin)).thenReturn(null);

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("getDmsInvoiceDate", String.class, PIVIPackageDO.class);
        method.setAccessible(true);

        // Run the test
        LocalDateTime result = (LocalDateTime) method.invoke(vinInitializeServiceImplUnderTest, carVin, piviPackageDO);

        // Verify
        verify(mockVehicleDmsRepository).findByCarVin(carVin);
        assertNull(result);
    }

    @Test
    public void testBuildPIVISubscriptionServiceDOList_Success() throws Exception {
        // Setup
        PIVIPackageDO piviPackageDO = createMockPIVIPackageDO();
        String inControlId = "<EMAIL>";
        List<AppDCuRenewRecords> renewRecords = Collections.emptyList();

        when(mockEcpIdUtil.nextIdStr()).thenReturn("123456789");

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod(
                "buildPIVISubscriptionServiceDOList", PIVIPackageDO.class, List.class);
        method.setAccessible(true);

        // Run the test
        @SuppressWarnings("unchecked")
        List<SubscriptionServiceDO> result = (List<SubscriptionServiceDO>) method.invoke(
                vinInitializeServiceImplUnderTest, piviPackageDO, renewRecords);

        // Verify
        assertThat(result).isNotEmpty();
        verify(mockEcpIdUtil, atLeast(1)).nextIdStr();
    }

    @Test
    public void testToVehicleMap_Success() throws Exception {
        // Setup
        List<IncontrolVehicleDO> vehicleList = createMockIncontrolVehicleDOList();

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("toVehicleMap", List.class);
        method.setAccessible(true);

        // Run the test
        @SuppressWarnings("unchecked")
        Map<String, IncontrolVehicleDO> result = (Map<String, IncontrolVehicleDO>) method.invoke(
                vinInitializeServiceImplUnderTest, vehicleList);

        // Verify
        assertThat(result).isNotEmpty();
        assertThat(result).containsKey("TESTVIN1234567890");
    }

    @Test
    public void testGetNeedCallDpVinList_Success() throws Exception {
        // Setup
        List<String> carVinList = Arrays.asList("TESTVIN1234567890", "TESTVIN0987654321");
        Map<String, IncontrolVehicleDO> existingVehicleMap = Map.of("TESTVIN1234567890", createMockIncontrolVehicleDO());

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("getNeedCallDpVinList", List.class, Map.class);
        method.setAccessible(true);

        // Run the test
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) method.invoke(vinInitializeServiceImplUnderTest, carVinList, existingVehicleMap);

        // Verify
        assertThat(result).hasSize(1);
        assertThat(result).contains("TESTVIN0987654321");
    }

    @Test
    public void testFindVehiclesToUnbind_Success() throws Exception {
        // Setup
        List<IncontrolVehicleDO> vehiclesBoundToUser = Arrays.asList(
                createMockIncontrolVehicleDO(),
                IncontrolVehicleDO.builder().carVin("UNBINDVIN123456789").build()
        );
        Set<String> currentVinSet = Set.of("TESTVIN1234567890");

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("findVehiclesToUnbind", List.class, Set.class);
        method.setAccessible(true);

        // Run the test
        @SuppressWarnings("unchecked")
        List<IncontrolVehicleDO> result = (List<IncontrolVehicleDO>) method.invoke(
                vinInitializeServiceImplUnderTest, vehiclesBoundToUser, currentVinSet);

        // Verify
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getCarVin()).isEqualTo("UNBINDVIN123456789");
    }

    @Test
    public void testUnbindVehiclesFromUser_Success() throws Exception {
        // Setup
        List<IncontrolVehicleDO> toUnbindList = createMockIncontrolVehicleDOList();

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("unbindVehiclesFromUser", List.class);
        method.setAccessible(true);

        // Run the test
        method.invoke(vinInitializeServiceImplUnderTest, toUnbindList);

        // Verify - check that vehicles are unbound
        assertThat(toUnbindList.get(0).getIncontrolId()).isNull();
        assertThat(toUnbindList.get(0).getIncontrolPhone()).isEqualTo("encryptedPhone");
        assertNotNull(toUnbindList.get(0).getBindTime());
    }

    @Test
    public void testGetAddVinList_Success() throws Exception {
        // Setup
        List<String> carVinList = Arrays.asList("TESTVIN1234567890", "TESTVIN0987654321");
        List<SubscriptionServiceDO> onlineServices = Arrays.asList(
                SubscriptionServiceDO.builder().carVin("TESTVIN1234567890").build()
        );

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod("getAddVinList", List.class, List.class);
        method.setAccessible(true);

        // Run the test
        @SuppressWarnings("unchecked")
        List<String> result = (List<String>) method.invoke(vinInitializeServiceImplUnderTest, carVinList, onlineServices);

        // Verify
        assertThat(result).hasSize(1);
        assertThat(result).contains("TESTVIN0987654321");
    }

    @Test
    public void testBuildInsertList_Success() throws Exception {
        // Setup
        String inControlId = "<EMAIL>";
        List<UserDPResultVO> dpList = createMockUserDPResultVOList();
        Map<String, PIVIPackageDO> piviPackageDOMap = Map.of("TESTVIN1234567890", createMockPIVIPackageDO());
        Map<String, String> dmsInvoiceMap = Map.of("TESTVIN1234567890", "2020-01-01");
        List<String> needCallDpVinList = Arrays.asList("TESTVIN1234567890");

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod(
                "buildInsertList", String.class, List.class, Map.class, Map.class, List.class);
        method.setAccessible(true);

        // Run the test
        @SuppressWarnings("unchecked")
        List<IncontrolVehicleDO> result = (List<IncontrolVehicleDO>) method.invoke(
                vinInitializeServiceImplUnderTest, inControlId, dpList, piviPackageDOMap, dmsInvoiceMap, needCallDpVinList);

        // Verify
        assertThat(result).isNotEmpty();
        verify(mockSeriesBrandMappingDataDOService, atLeast(1)).createSeriesBrandMappingData(any(UserDPResultVO.class));
    }

    @Test
    public void testUpdateExistingVehicles_Success() throws Exception {
        // Setup
        String inControlId = "<EMAIL>";
        IncontrolVehicleDO vehicleDO = createMockIncontrolVehicleDO();
        vehicleDO.setRevision(1);
        Map<String, IncontrolVehicleDO> existingVehicleMap = Map.of("TESTVIN1234567890", vehicleDO);
        Map<String, PIVIPackageDO> piviPackageDOMap = Map.of("TESTVIN1234567890", createMockPIVIPackageDO());
        Map<String, String> dmsInvoiceMap = Map.of("TESTVIN1234567890", "2020-01-01");
        List<IncontrolVehicleDO> needUpdateDoList = new ArrayList<>();

        // Use reflection to call private method
        java.lang.reflect.Method method = VinInitializeServiceImpl.class.getDeclaredMethod(
                "updateExistingVehicles", String.class, Map.class, Map.class, Map.class, List.class);
        method.setAccessible(true);

        // Run the test
        method.invoke(vinInitializeServiceImplUnderTest, inControlId, existingVehicleMap, piviPackageDOMap, dmsInvoiceMap, needUpdateDoList);

        // Verify
        assertThat(needUpdateDoList).isNotEmpty();
    }
}
