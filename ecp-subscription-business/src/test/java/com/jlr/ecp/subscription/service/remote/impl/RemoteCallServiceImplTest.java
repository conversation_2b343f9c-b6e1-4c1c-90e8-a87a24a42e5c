package com.jlr.ecp.subscription.service.remote.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jlr.ecp.framework.common.exception.ServiceException;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteModifyRespDTO;
import com.jlr.ecp.subscription.controller.admin.dto.remote.RemoteQueryByVinRespDTO;
import com.jlr.ecp.subscription.dal.dataobject.fufilment.VcsOrderFufilmentCall;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.fufilment.VcsOrderFulfilmentCallMapper;
import com.jlr.ecp.subscription.enums.ErrorCodeConstants;
import com.jlr.ecp.subscription.exception.TooManyRequestException;
import com.jlr.ecp.subscription.kafka.listener.dto.OrdFufilmentBusiDTO;
import com.jlr.ecp.subscription.kafka.listener.dto.TSDPSubscriptionDTO;
import com.jlr.ecp.subscription.kafka.message.BaseMessage;
import com.jlr.ecp.subscription.util.RLockUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.http.*;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RemoteCallServiceImplTest {

    @Mock
    private RestTemplate mockRestTemplate;
    @Mock
    private Redisson mockRedisson;

    @InjectMocks
    private RemoteCallServiceImpl remoteCallServiceImplUnderTest;

    @Mock
    private RLock mockLock;

    @Mock
    private VcsOrderFulfilmentCallMapper mockCallMapper;

    @Before
    public void setUp() throws Exception {
        ReflectionTestUtils.setField(remoteCallServiceImplUnderTest, "subscriptionRenewUrl", "subscriptionRenewUrl");
        ReflectionTestUtils.setField(remoteCallServiceImplUnderTest, "ifEcomToken", "ifEcomToken");
        remoteCallServiceImplUnderTest.restTemplate = mockRestTemplate;
        MockitoAnnotations.openMocks(this);
        when(mockRedisson.getLock(anyString())).thenReturn(mockLock);
        // 设置私有字段
        setPrivateField("subscriptionRenewUrl", "http://test.com/renew");
        setPrivateField("ifEcomToken", "test-token");
        setPrivateField("getByVinUrl", "http://test.com/vin/");
        setPrivateField("icrSubscriptionUrl", "http://test.com/icr/");
        setPrivateField("busyErrorLimit", 5);
        setPrivateField("busyDelaySecond", 10);
    }

    private void setPrivateField(String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = RemoteCallServiceImpl.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(remoteCallServiceImplUnderTest, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set private field: " + fieldName, e);
        }
    }

    @Test
    public void testCallTSDPRenewService() {
        // Setup
        final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
        param.setVin("vin");
        param.setUser("user");
        param.setTransactionId("transactionId");
        final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
        tsdpSubscriptionDTO.setExpiryDate("expiryDate");
        param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(true);
        expectedResult.setResponse("{}");

        try (MockedStatic<RLockUtil> mockedStatic = mockStatic(RLockUtil.class)) {
            mockedStatic.when(() -> RLockUtil.tryLock(mockLock, 2500, 5000, TimeUnit.MILLISECONDS)).thenReturn(true);
            // Configure RestTemplate.postForEntity(...).
            final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                    HttpStatus.OK);
            when(mockRestTemplate.postForEntity(anyString(), any(),
                    eq(JSONObject.class))).thenReturn(jsonObjectResponseEntity);

            // Run the test
            final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.callTSDPRenewService(param);

            // Verify the results
            assertEquals(expectedResult, result);
        }
    }

    @Test
    public void testCallTSDPRenewService_RestTemplateThrowsRestClientException() {
        // Setup
        final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
        param.setVin("vin");
        param.setUser("user");
        param.setTransactionId("transactionId");
        final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
        tsdpSubscriptionDTO.setExpiryDate("expiryDate");
        param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(false);
        expectedResult.setTooManyRequests(false);
        expectedResult.setErrorMsg("请求异常, 调用TSDP续费获取锁失败");

        // Run the test
        final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.callTSDPRenewService(param);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testConcurrentCallTSDPRenew() {
        // Setup
        final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
        param.setVin("vin");
        param.setUser("user");
        param.setTransactionId("transactionId");
        final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
        tsdpSubscriptionDTO.setExpiryDate("expiryDate");
        param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(false);
        expectedResult.setTooManyRequests(false);
        expectedResult.setErrorMsg("请求异常, 调用TSDP续费获取锁失败");

        when(mockLock.tryLock()).thenReturn(true);

        // Configure RestTemplate.postForEntity(...).
        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                HttpStatus.OK);

        // Run the test
        final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.concurrentCallTSDPRenew(param);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testConcurrentCallTSDPRenew_RestTemplateThrowsRestClientException() {
        // Setup
        final OrdFufilmentBusiDTO param = new OrdFufilmentBusiDTO();
        param.setVin("vin");
        param.setUser("user");
        param.setTransactionId("transactionId");
        final TSDPSubscriptionDTO tsdpSubscriptionDTO = new TSDPSubscriptionDTO();
        tsdpSubscriptionDTO.setExpiryDate("expiryDate");
        param.setNewOrModifiedSubscriptions(List.of(tsdpSubscriptionDTO));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(false);
        expectedResult.setErrorMsg("已有在途订单，请稍后再试");

        when(mockLock.tryLock()).thenReturn(false);

        // Run the test
        final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.concurrentCallTSDPRenew(param);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testListenerCallTSDPRenew() {
        // Setup
        final List<SubscriptionServiceDO> services = List.of(SubscriptionServiceDO.builder()
                .serviceName("serviceName")
                .servicePackage("servicePackage")
                .build());
        final BaseMessage baseMessage = new BaseMessage();
        baseMessage.setMessageId("messageId");
        baseMessage.setTenantId(0L);
        baseMessage.setOrderCode("orderCode");
        baseMessage.setVin("vin");
        baseMessage.setServiceEndDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));

        final RemoteModifyRespDTO expectedResult = new RemoteModifyRespDTO();
        expectedResult.setSuccess(false);
        expectedResult.setErrorMsg("已有在途订单，请稍后再试");

        // Run the test
        final RemoteModifyRespDTO result = remoteCallServiceImplUnderTest.listenerCallTSDPRenew(services,
                "fulfilmentId", baseMessage, "inControlId");

        // Verify the results
        assertEquals(expectedResult, result);
        verify(mockCallMapper).insert(any(VcsOrderFufilmentCall.class));
    }

    @Test
    public void testGetByVin_Success() {
        // Setup
        String vin = "VIN1234567890";
        RemoteQueryByVinRespDTO expectedResponse = new RemoteQueryByVinRespDTO();

        ResponseEntity<RemoteQueryByVinRespDTO> responseEntity = new ResponseEntity<>(expectedResponse, HttpStatus.OK);
        when(mockRestTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class)))
                .thenReturn(responseEntity);

        // Run the test
        RemoteQueryByVinRespDTO result = remoteCallServiceImplUnderTest.getByVin(vin);

        // Verify the results
        assertNotNull(result);
        verify(mockRestTemplate).exchange(eq("http://test.com/vin/" + vin), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class));
    }

    @Test
    public void testGetByVin_NonOKStatusCode() {
        // Setup
        String vin = "VIN1234567890";

        ResponseEntity<RemoteQueryByVinRespDTO> responseEntity = new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        when(mockRestTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class)))
                .thenReturn(responseEntity);

        // Run the test and verify exception
        Exception exception = assertThrows(RuntimeException.class, () -> {
            remoteCallServiceImplUnderTest.getByVin(vin);
        });

        // Verify the results
        assertEquals(ErrorCodeConstants.GET_EXPIRE_DATE_ERROR.getCode(), ((com.jlr.ecp.framework.common.exception.ServiceException) exception).getCode());
        verify(mockRestTemplate).exchange(eq("http://test.com/vin/" + vin), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class));
    }

    @Test
    public void testGetByVin_HttpClientErrorException_404() {
        // Setup
        String vin = "VIN1234567890";

        HttpClientErrorException exception = HttpClientErrorException.create(
                HttpStatus.NOT_FOUND,
                "404 Not Found",
                new HttpHeaders(),
                "404 Not Found".getBytes(),
                null
        );
        when(mockRestTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class)))
                .thenThrow(exception);

        // Run the test and verify exception
        Exception thrownException = assertThrows(RuntimeException.class, () -> {
            remoteCallServiceImplUnderTest.getByVin(vin);
        });

        // Verify the results
        assertEquals(ErrorCodeConstants.VIN_NOT_FOUND_ERROR.getCode(), ((com.jlr.ecp.framework.common.exception.ServiceException) thrownException).getCode());
        verify(mockRestTemplate).exchange(eq("http://test.com/vin/" + vin), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class));
    }

    @Test
    public void testGetByVin_HttpClientErrorException_409() {
        // Setup
        String vin = "VIN1234567890";

        HttpClientErrorException exception = HttpClientErrorException.create(
                HttpStatus.CONFLICT,
                "409 Too Many Requests",
                new HttpHeaders(),
                "409 Too Many Requests".getBytes(),
                null
        );
        when(mockRestTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class)))
                .thenThrow(exception);

        // Run the test and verify exception
        assertThrows(ServiceException.class, () -> {
            remoteCallServiceImplUnderTest.getByVin(vin);
        });

        verify(mockRestTemplate).exchange(eq("http://test.com/vin/" + vin), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class));
    }

    @Test
    public void testGetByVin_HttpClientErrorException_Other() {
        // Setup
        String vin = "VIN1234567890";

        HttpClientErrorException exception = HttpClientErrorException.create(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "500 Internal Server Error",
                new HttpHeaders(),
                "500 Internal Server Error".getBytes(),
                null
        );
        when(mockRestTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class)))
                .thenThrow(exception);

        // Run the test and verify exception
        Exception thrownException = assertThrows(RuntimeException.class, () -> {
            remoteCallServiceImplUnderTest.getByVin(vin);
        });

        // Verify the results
        assertEquals(ErrorCodeConstants.GET_EXPIRE_DATE_ERROR.getCode(), ((com.jlr.ecp.framework.common.exception.ServiceException) thrownException).getCode());
        verify(mockRestTemplate).exchange(eq("http://test.com/vin/" + vin), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class));
    }

    @Test
    public void testGetByVin_RestClientException() {
        // Setup
        String vin = "VIN1234567890";

        RestClientException exception = new RestClientException("Connection failed");
        when(mockRestTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class)))
                .thenThrow(exception);

        // Run the test and verify exception
        Exception thrownException = assertThrows(RuntimeException.class, () -> {
            remoteCallServiceImplUnderTest.getByVin(vin);
        });

        // Verify the results
        assertEquals(ErrorCodeConstants.GET_EXPIRE_DATE_ERROR.getCode(), ((com.jlr.ecp.framework.common.exception.ServiceException) thrownException).getCode());
        verify(mockRestTemplate).exchange(eq("http://test.com/vin/" + vin), eq(HttpMethod.GET), any(HttpEntity.class), eq(RemoteQueryByVinRespDTO.class));
    }

    @Test
    public void testGetByICR_Success() {
        // Setup
        String inControlId = "<EMAIL>";
        JSONObject responseBody = new JSONObject();
        responseBody.put("subscriptions", new JSONArray());

        ResponseEntity<JSONObject> responseEntity = new ResponseEntity<>(responseBody, HttpStatus.OK);
        when(mockRestTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(JSONObject.class)))
                .thenReturn(responseEntity);

        // Run the test
        List<VinsAndServiceDTO> result = remoteCallServiceImplUnderTest.getByICR(inControlId);

        // Verify the results
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mockRestTemplate).exchange(eq("http://test.com/icr/" + inControlId), eq(HttpMethod.GET), any(HttpEntity.class), eq(JSONObject.class));
    }

    @Test
    public void testGetByICR_NonOKStatusCode() {
        // Setup
        String inControlId = "<EMAIL>";

        ResponseEntity<JSONObject> responseEntity = new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        when(mockRestTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(JSONObject.class)))
                .thenReturn(responseEntity);

        // Run the test and verify exception
        Exception exception = assertThrows(RuntimeException.class, () -> {
            remoteCallServiceImplUnderTest.getByICR(inControlId);
        });

        // Verify the results
        assertEquals(ErrorCodeConstants.GET_EXPIRE_DATE_ERROR.getCode(), ((com.jlr.ecp.framework.common.exception.ServiceException) exception).getCode());
        verify(mockRestTemplate).exchange(eq("http://test.com/icr/" + inControlId), eq(HttpMethod.GET), any(HttpEntity.class), eq(JSONObject.class));
    }

    @Test
    public void testGetByICR_RestClientException_Other() {
        // Setup
        String inControlId = "<EMAIL>";

        RestClientException exception = new RestClientException("Connection failed");
        when(mockRestTemplate.exchange(anyString(), eq(HttpMethod.GET), any(HttpEntity.class), eq(JSONObject.class)))
                .thenThrow(exception);

        // Run the test and verify exception is rethrown
        assertThrows(RestClientException.class, () -> {
            remoteCallServiceImplUnderTest.getByICR(inControlId);
        });

        verify(mockRestTemplate).exchange(eq("http://test.com/icr/" + inControlId), eq(HttpMethod.GET), any(HttpEntity.class), eq(JSONObject.class));
    }
}
