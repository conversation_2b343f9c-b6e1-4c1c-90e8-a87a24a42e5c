package com.jlr.ecp.subscription.service.vehicle;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Snowflake;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.google.common.collect.Lists;
import com.jlr.ecp.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleByCarDTO;
import com.jlr.ecp.subscription.api.incontrol.dto.IncontrolVehicleDTO;
import com.jlr.ecp.subscription.api.model.vo.UserCarServiceListVO;
import com.jlr.ecp.subscription.api.model.vo.UserDPResultVO;
import com.jlr.ecp.subscription.api.subscripiton.dto.ServicePackageDTO;
import com.jlr.ecp.subscription.api.subscripiton.dto.VinsAndServiceDTO;
import com.jlr.ecp.subscription.config.RedisService;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.IncontrolVehicleDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.SeriesBrandMappingDataDO;
import com.jlr.ecp.subscription.dal.dataobject.icrorder.VehicleDmsDO;
import com.jlr.ecp.subscription.dal.dataobject.remotepackage.PIVIPackageDO;
import com.jlr.ecp.subscription.dal.dataobject.subscribeservice.SubscriptionServiceDO;
import com.jlr.ecp.subscription.dal.mysql.appd.AppDCuRenewRecordsMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.IncontrolVehicleDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.SeriesBrandMappingDataDOMapper;
import com.jlr.ecp.subscription.dal.mysql.icroder.VehicleDmsDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.PIVIPackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.remotepackage.RemotePackageDOMapper;
import com.jlr.ecp.subscription.dal.mysql.subscribeservice.SubscriptionServiceMapper;
import com.jlr.ecp.subscription.dal.repository.ConsumerVehicleRepository;
import com.jlr.ecp.subscription.dal.repository.IncontrolVehicleRepository;
import com.jlr.ecp.subscription.service.icrorder.SeriesBrandMappingDataDOService;
import com.jlr.ecp.subscription.service.incontrol.IncontrolCustomerService;
import com.jlr.ecp.subscription.service.model.VehicleModelMasterDataService;
import com.jlr.ecp.subscription.service.remote.RemoteCallService;
import com.jlr.ecp.subscription.service.remotepackage.RemotePackageDOService;
import com.jlr.ecp.subscription.util.redis.RedisDelayQueueUtil;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class IncontrolVehicleServiceImplTest {

    @Mock
    private IncontrolVehicleDOMapper mockIncontrolVehicleMapper;
    @Mock
    private VehicleModelMasterDataService mockVehicleModelMasterDataService;
    @Mock
    private IncontrolCustomerService mockIncontrolCustomerService;
    @Mock
    private RestTemplate mockRestTemplate;
    @Mock
    private SubscriptionServiceMapper mockSubscriptionServiceMapper;
    @Mock
    private RemotePackageDOMapper mockRemotePackageDOMapper;
    @Mock
    private RemotePackageDOService mockRemotePackageDOService;
    @Mock
    private VehicleDmsDOMapper mockVehicleDmsDOMapper;
    @Mock
    private SeriesBrandMappingDataDOMapper mockMappingDataMapper;
    @Mock
    private RedisService mockRedisService;
    @Mock
    private RedisDelayQueueUtil mockRedisDelayQueueUtil;

    @Mock
    private PIVIPackageDOMapper piviPackageDOMapper;

    @Mock
    private AppDCuRenewRecordsMapper mockAppDCuRenewRecordsMapper;
    @Mock
    private RemoteCallService mockRemoteCallService;
    private IncontrolVehicleServiceImpl incontrolVehicleServiceImplUnderTest;

    @Mock
    private Snowflake mockEcpIdUtil;

    @Mock
    private SeriesBrandMappingDataDOService mockSeriesBrandMappingDataDOService;

    @Mock
    private VinInitializeService mockVinInitializeService;

    @Mock
    private ConsumerVehicleRepository mockConsumerVehicleRepository;

    @Mock
    private IncontrolVehicleRepository incontrolVehicleRepositoryTest;

    @Before
    public void setUp() {
        incontrolVehicleServiceImplUnderTest = new IncontrolVehicleServiceImpl(mockIncontrolVehicleMapper,
                mockVehicleModelMasterDataService, mockIncontrolCustomerService, mockRestTemplate,
                mockSubscriptionServiceMapper, mockRemotePackageDOMapper, mockRemotePackageDOService, piviPackageDOMapper, mockVehicleDmsDOMapper, mockAppDCuRenewRecordsMapper, mockRemoteCallService);
        ReflectionTestUtils.setField(incontrolVehicleServiceImplUnderTest, "mappingDataMapper", mockMappingDataMapper);
        ReflectionTestUtils.setField(incontrolVehicleServiceImplUnderTest, "redisDelayQueueUtil",
                mockRedisDelayQueueUtil);
        ReflectionTestUtils.setField(incontrolVehicleServiceImplUnderTest, "icrSubscriptionUrl", "icrSubscriptionUrl");
        ReflectionTestUtils.setField(incontrolVehicleServiceImplUnderTest, "ifEcomToken", "ifEcomToken");
        ReflectionTestUtils.setField(incontrolVehicleServiceImplUnderTest, "seriesBrandMappingDataDOService",
                mockSeriesBrandMappingDataDOService);
        ReflectionTestUtils.setField(incontrolVehicleServiceImplUnderTest, "vinInitializeService",
                mockVinInitializeService);
        ReflectionTestUtils.setField(incontrolVehicleServiceImplUnderTest, "consumerVehicleRepository",
                mockConsumerVehicleRepository);
        incontrolVehicleServiceImplUnderTest.redisService = mockRedisService;
        incontrolVehicleServiceImplUnderTest.busyErrorLimit = 0;
        incontrolVehicleServiceImplUnderTest.busyDelaySecond = 0;
        incontrolVehicleServiceImplUnderTest.ecpIdUtil = mockEcpIdUtil;
        TableInfoHelper.initTableInfo(new MapperBuilderAssistant(new MybatisConfiguration(), ""), IncontrolVehicleDO.class);
    }

    @Test
    public void testGetIncontrolVehicleByConsumerCode() {
        // Setup
        final IncontrolVehicleDTO incontrolVehicleDTO = new IncontrolVehicleDTO();
        incontrolVehicleDTO.setId(0L);
        incontrolVehicleDTO.setCarNo("carNo");
        incontrolVehicleDTO.setIncontrolId("incontrolId");
        incontrolVehicleDTO.setCarVin("carVin");
        incontrolVehicleDTO.setCarSystemModel("carSystemModel");
        final List<IncontrolVehicleDTO> expectedResult = Lists.newArrayList(incontrolVehicleDTO);

        // Configure IncontrolVehicleDOMapper.getBindIncontrolVehicle(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = Lists.newArrayList(IncontrolVehicleDO.builder()
                .carVin("carVin")
                .incontrolId("inControlId")
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .brandCode("brandCode")
                .brandName("brandName")
                .hobEn("hobEn")
                .productionEn("productionEn")
                .configCode("configCode")
                .configName("configName")
                .modelYear("modelYear")
                .carSystemModel("carSystemModel")
                .bindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockIncontrolVehicleMapper.getBindIncontrolVehicle("consumerCode", "brandCode"))
                .thenReturn(incontrolVehicleDOS);

        // Run the test
        final List<IncontrolVehicleDTO> result = incontrolVehicleRepositoryTest.getIncontrolVehicleByConsumerCode(
                "consumerCode", "brandCode");

        // Verify the results
        assertNotNull(result);
        // assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetIncontrolVehicleByConsumerCode_IncontrolVehicleDOMapperReturnsNoItems() {
        // Setup
        when(mockIncontrolVehicleMapper.getBindIncontrolVehicle("consumerCode", "brandCode"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<IncontrolVehicleDTO> result = incontrolVehicleRepositoryTest.getIncontrolVehicleByConsumerCode(
                "consumerCode", "brandCode");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetIncontrolVehicleByCarVinList() {
        // Setup
        final IncontrolVehicleByCarDTO incontrolVehicleByCarDTO = new IncontrolVehicleByCarDTO();
        incontrolVehicleByCarDTO.setCarVin("carVin");
        incontrolVehicleByCarDTO.setIncontrolId("incontrolId");
        incontrolVehicleByCarDTO.setConsumerCode("consumerCode");
        incontrolVehicleByCarDTO.setSeriesCode("seriesCode");
        incontrolVehicleByCarDTO.setSeriesName("seriesName");
        final List<IncontrolVehicleByCarDTO> expectedResult = Lists.newArrayList(incontrolVehicleByCarDTO);

        // Configure IncontrolVehicleDOMapper.selectIncontrolVehicleByCarVinList(...).
        final IncontrolVehicleByCarDTO incontrolVehicleByCarDTO1 = new IncontrolVehicleByCarDTO();
        incontrolVehicleByCarDTO1.setCarVin("carVin");
        incontrolVehicleByCarDTO1.setIncontrolId("incontrolId");
        incontrolVehicleByCarDTO1.setConsumerCode("consumerCode");
        incontrolVehicleByCarDTO1.setSeriesCode("seriesCode");
        incontrolVehicleByCarDTO1.setSeriesName("seriesName");
        final List<IncontrolVehicleByCarDTO> incontrolVehicleByCarDTOS = Lists.newArrayList(incontrolVehicleByCarDTO1);
        when(mockIncontrolVehicleMapper.selectIncontrolVehicleByCarVinList(Lists.newArrayList("value")))
                .thenReturn(incontrolVehicleByCarDTOS);

        // Run the test
        final List<IncontrolVehicleByCarDTO> result = incontrolVehicleServiceImplUnderTest.getIncontrolVehicleByCarVinList(
                Lists.newArrayList("value"));

        // Verify the results
        assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testGetIncontrolVehicleByCarVinList_IncontrolVehicleDOMapperReturnsNoItems() {
        // Setup
        when(mockIncontrolVehicleMapper.selectIncontrolVehicleByCarVinList(Lists.newArrayList("value")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<IncontrolVehicleByCarDTO> result = incontrolVehicleServiceImplUnderTest.getIncontrolVehicleByCarVinList(
                Lists.newArrayList("value"));

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testQueryAndStoreVinsAndServiceInfo() {
        // Setup
        final VinsAndServiceDTO vinsAndServiceDTO = new VinsAndServiceDTO();
        vinsAndServiceDTO.setVin("carVin");
        final ServicePackageDTO servicePackageDTO = new ServicePackageDTO();
        servicePackageDTO.setServicePackageName("servicePackage");
        servicePackageDTO.setServiceName("serviceName");
        servicePackageDTO.setExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        vinsAndServiceDTO.setServicePackage(Lists.newArrayList(servicePackageDTO));
        final List<VinsAndServiceDTO> expectedResult = Lists.newArrayList(vinsAndServiceDTO);

        // Configure RestTemplate.exchange(...).
        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                HttpStatus.OK);
        when(mockRestTemplate.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class),
                any(Class.class))).thenReturn(jsonObjectResponseEntity);

        // when(mockRedisService.getCacheObject("key")).thenReturn(0);
        // when(mockRemotePackageDOService.getExistPackageCode(Lists.newArrayList("value"))).thenReturn(Lists.newArrayList("value"));

        // Run the test
        final List<VinsAndServiceDTO> result = incontrolVehicleServiceImplUnderTest.queryAndStoreVinsAndServiceInfo(
                "inControlId");

        // Verify the results
        assertNull(result);
        // assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisService, atLeast(0)).increment("counterKey");
        verify(mockRedisDelayQueueUtil, atLeast(0)).remove("inControlId", "code");
        verify(mockRedisDelayQueueUtil, atLeast(0)).addDelayQueue("inControlId", 0L, TimeUnit.SECONDS, "code");
        verify(mockSubscriptionServiceMapper, atLeast(0)).delete(any(LambdaQueryWrapperX.class));
        verify(mockSubscriptionServiceMapper, atLeast(0)).insertBatch(new ArrayList<>(Lists.newArrayList(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .serviceName("serviceName")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build())));
    }

    @Test
    public void testQueryAndStoreVinsAndServiceInfo_RestTemplateThrowsRestClientException() {
        // Setup
        // when(mockRestTemplate.exchange("url", HttpMethod.GET, new HttpEntity<>("value", new HttpHeaders()),
        //         JSONObject.class)).thenThrow(RestClientException.class);
        // when(mockRedisService.getCacheObject("key")).thenReturn(0);

        // Run the test
        assertThatThrownBy(
                () -> incontrolVehicleServiceImplUnderTest.queryAndStoreVinsAndServiceInfo("inControlId"))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    public void testQueryAndStoreVinsAndServiceInfo_RedisServiceGetCacheObjectReturnsNull() {
        // Setup
        // when(mockRestTemplate.exchange("url", HttpMethod.GET, new HttpEntity<>("value", new HttpHeaders()),
        //         JSONObject.class)).thenThrow(RestClientException.class);
        // when(mockRedisService.getCacheObject("key")).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> incontrolVehicleServiceImplUnderTest.queryAndStoreVinsAndServiceInfo("inControlId"))
                .isInstanceOf(NullPointerException.class);
        verify(mockRedisService, atLeast(0)).increment("counterKey");
        verify(mockRedisDelayQueueUtil, atLeast(0)).remove("inControlId", "code");
        verify(mockRedisDelayQueueUtil, atLeast(0)).addDelayQueue("inControlId", 0L, TimeUnit.SECONDS, "code");
    }

    @Test
    public void testQueryAndStoreVinsAndServiceInfo_RemotePackageDOServiceReturnsNoItems() {
        // Setup
        // Configure RestTemplate.exchange(...).
        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(new JSONObject(0, false),
                HttpStatus.OK);
        when(mockRestTemplate.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class),
                any(Class.class))).thenReturn(jsonObjectResponseEntity);

        // when(mockRedisService.getCacheObject("key")).thenReturn(0);
        // when(mockRemotePackageDOService.getExistPackageCode(Lists.newArrayList("value"))).thenReturn(Collections.emptyList());

        // Run the test
        final List<VinsAndServiceDTO> result = incontrolVehicleServiceImplUnderTest.queryAndStoreVinsAndServiceInfo(
                "inControlId");

        // Verify the results
        assertNull(result);
        // assertThat(result).isEqualTo(Collections.emptyList());
        verify(mockRedisService, atLeast(0)).increment("counterKey");
        verify(mockSubscriptionServiceMapper, atLeast(0)).delete(any(LambdaQueryWrapperX.class));
        verify(mockSubscriptionServiceMapper, atLeast(0)).insertBatch(new ArrayList<>(Lists.newArrayList(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .serviceName("serviceName")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build())));
    }

    @Test
    public void testSaveSubscriptionServiceInfo() {
        // Setup
        final VinsAndServiceDTO vinsAndServiceDTO1 = new VinsAndServiceDTO();
        vinsAndServiceDTO1.setVin("carVin");
        final ServicePackageDTO servicePackageDTO = new ServicePackageDTO();
        servicePackageDTO.setServicePackageName("servicePackage");
        servicePackageDTO.setServiceName("serviceName");
        servicePackageDTO.setExpireDateUTC0(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        vinsAndServiceDTO1.setServicePackage(Lists.newArrayList(servicePackageDTO));
        final List<VinsAndServiceDTO> vinsAndServiceDTO = Lists.newArrayList(vinsAndServiceDTO1);
        when(mockEcpIdUtil.nextIdStr()).thenReturn("123");

        // Run the test
        incontrolVehicleServiceImplUnderTest.saveSubscriptionServiceInfo("inControlId", vinsAndServiceDTO);

        // Verify the results
        verify(mockSubscriptionServiceMapper, atLeast(0)).delete(any(LambdaQueryWrapperX.class));
        verify(mockSubscriptionServiceMapper, atLeast(0)).insertBatch(new ArrayList<>(Lists.newArrayList(SubscriptionServiceDO.builder()
                .subscriptionId("123")
                .carVin("carVin")
                .serviceName("serviceName")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build())));
    }

    @Test
    public void testSaveSubscriptionServiceInfo_RemotePackageDOServiceReturnsNoItems() {
        // Setup
        final VinsAndServiceDTO vinsAndServiceDTO1 = new VinsAndServiceDTO();
        vinsAndServiceDTO1.setVin("carVin");
        final ServicePackageDTO servicePackageDTO = new ServicePackageDTO();
        servicePackageDTO.setServicePackageName("servicePackage");
        servicePackageDTO.setServiceName("serviceName");
        servicePackageDTO.setExpireDateUTC0(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        vinsAndServiceDTO1.setServicePackage(Lists.newArrayList(servicePackageDTO));
        final List<VinsAndServiceDTO> vinsAndServiceDTO = Lists.newArrayList(vinsAndServiceDTO1);
        // when(mockRemotePackageDOService.getExistPackageCode(Lists.newArrayList("value"))).thenReturn(Collections.emptyList());
        // when(mockRedisService.increment("counterKey")).thenReturn(0L);

        // Run the test
        incontrolVehicleServiceImplUnderTest.saveSubscriptionServiceInfo("inControlId", vinsAndServiceDTO);

        // Verify the results
        verify(mockSubscriptionServiceMapper, atLeast(0)).delete(any(LambdaQueryWrapperX.class));
        verify(mockSubscriptionServiceMapper, atLeast(0)).insertBatch(new ArrayList<>(Lists.newArrayList(SubscriptionServiceDO.builder()
                .carVin("carVin")
                .serviceName("serviceName")
                .servicePackage("servicePackage")
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .serviceType(0)
                .build())));
    }

    @Test
    public void testGetVinsAndServiceByIncontronId() {
        // Setup
        final VinsAndServiceDTO vinsAndServiceDTO = new VinsAndServiceDTO();
        vinsAndServiceDTO.setVin("carVin");
        final ServicePackageDTO servicePackageDTO = new ServicePackageDTO();
        servicePackageDTO.setServicePackageName("servicePackage");
        servicePackageDTO.setServiceName("serviceName");
        servicePackageDTO.setExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0));
        vinsAndServiceDTO.setServicePackage(Lists.newArrayList(servicePackageDTO));
        final List<VinsAndServiceDTO> expectedResult = Lists.newArrayList(vinsAndServiceDTO);

        // Configure RestTemplate.exchange(...).
        String json = FileUtil.readString("classpath:ICR名下订阅查询.json", "UTF-8");
        final ResponseEntity<JSONObject> jsonObjectResponseEntity = new ResponseEntity<>(JSON.parseObject(json),
                HttpStatus.OK);
        when(mockRestTemplate.exchange(anyString(), any(HttpMethod.class), any(HttpEntity.class),
                any(Class.class))).thenReturn(jsonObjectResponseEntity);

        // when(mockRedisService.getCacheObject("key")).thenReturn(0);

        // Run the test
        final List<VinsAndServiceDTO> result = incontrolVehicleServiceImplUnderTest.getVinsAndServiceByIncontronId(
                "inControlId");

        // Verify the results
        assertNotNull(result);
        // assertThat(result).isEqualTo(expectedResult);
        verify(mockRedisService, atLeast(0)).increment("counterKey");
        verify(mockRedisDelayQueueUtil, atLeast(0)).remove("inControlId", "code");
        verify(mockRedisDelayQueueUtil, atLeast(0)).addDelayQueue("inControlId", 0L, TimeUnit.SECONDS, "code");
    }

    @Test
    public void testGetVinsAndServiceByIncontronId_RestTemplateThrowsRestClientException() {
        // Setup
        // when(mockRestTemplate.exchange("url", HttpMethod.GET, new HttpEntity<>("value", new HttpHeaders()),
        //         JSONObject.class)).thenThrow(RestClientException.class);
        // when(mockRedisService.getCacheObject("key")).thenReturn(0);

        // Run the test
        assertThatThrownBy(
                () -> incontrolVehicleServiceImplUnderTest.getVinsAndServiceByIncontronId("inControlId"))
                .isInstanceOf(NullPointerException.class);
    }

    @Test
    public void testGetVinsAndServiceByIncontronId_RedisServiceGetCacheObjectReturnsNull() {
        // Setup
        // when(mockRestTemplate.exchange("url", HttpMethod.GET, new HttpEntity<>("value", new HttpHeaders()),
        //         JSONObject.class)).thenThrow(RestClientException.class);
        // when(mockRedisService.getCacheObject("key")).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> incontrolVehicleServiceImplUnderTest.getVinsAndServiceByIncontronId("inControlId"))
                .isInstanceOf(NullPointerException.class);
        verify(mockRedisService, atLeast(0)).increment("counterKey");
        verify(mockRedisDelayQueueUtil, atLeast(0)).remove("inControlId", "code");
        verify(mockRedisDelayQueueUtil, atLeast(0)).addDelayQueue("inControlId", 0L, TimeUnit.SECONDS, "code");
    }

    @Test
    public void testGetUserCarServiceList_NormalCase() {
        // Setup - 正常情况测试
        final List<IncontrolVehicleDO> incontrolVehicleDOS = Lists.newArrayList(IncontrolVehicleDO.builder()
                .carVin("VIN001")
                .incontrolId("inControlId")
                .seriesCode("SERIES001")
                .seriesName("seriesName")
                .brandCode("brandCode")
                .brandName("brandName")
                .hobEn("hobEn")
                .productionEn("productionEn")
                .configCode("configCode")
                .configName("configName")
                .modelYear("modelYear")
                .carSystemModel("PIVI")
                .bindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());

        when(mockIncontrolVehicleMapper.getBindIncontrolVehicle("consumerCode", "clientId"))
                .thenReturn(incontrolVehicleDOS);

        // 模拟消费者绑定车辆
        // 注意：这里需要ConsumerVehicleRepository的mock，但原始测试中未注入

        // 模拟根据VIN列表和品牌代码查询返回数据
        final List<IncontrolVehicleDTO> carInfoList = Lists.newArrayList(new IncontrolVehicleDTO());
        carInfoList.get(0).setCarVin("VIN001");
        carInfoList.get(0).setCarSystemModel("PIVI");
        carInfoList.get(0).setSeriesCode("SERIES001");
        when(mockIncontrolVehicleMapper.selectCarInfoByCarVinListAndBrandCode(anyList(), eq("clientId")))
                .thenReturn(carInfoList);

        // 模拟品牌映射数据
        final List<SeriesBrandMappingDataDO> seriesBrandMappingDataDOS = Lists.newArrayList(SeriesBrandMappingDataDO.builder()
                .seriesCode("SERIES001")
                .seriesName("Range Rover")
                .brandNameView("Land Rover")
                .build());
        when(mockMappingDataMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(seriesBrandMappingDataDOS);

        // 模拟订阅服务数据
        final List<SubscriptionServiceDO> subscriptionServiceDOS = Lists.newArrayList(SubscriptionServiceDO.builder()
                        .carVin("VIN001")
                        .serviceName("Online Pack")
                        .servicePackage("Online Pack")
                        .expiryDate(LocalDateTime.now().plusYears(1))
                        .serviceType(1) // PIVI服务类型
                        .build(),
                SubscriptionServiceDO.builder()
                        .carVin("VIN001")
                        .serviceName("Online Pack")
                        .servicePackage("ONLINE-PACK")
                        .expiryDate(LocalDateTime.now().plusYears(1))
                        .serviceType(3) // PIVI服务类型
                        .build(),
                SubscriptionServiceDO.builder()
                        .carVin("VIN001")
                        .serviceName("Online Pack")
                        .servicePackage("CONNECTED-NAVIGATION")
                        .expiryDate(LocalDateTime.now().plusYears(1))
                        .serviceType(3) // PIVI服务类型
                        .build(),
                SubscriptionServiceDO.builder()
                        .carVin("VIN001")
                        .serviceName("Online Pack")
                        .servicePackage("DATA-PLAN")
                        .expiryDate(LocalDateTime.now().plusYears(1))
                        .serviceType(3) // PIVI服务类型
                        .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapperX.class)))
                .thenReturn(subscriptionServiceDOS);

        // Run the test
        final List<UserCarServiceListVO> result = incontrolVehicleServiceImplUnderTest.getUserCarServiceList(
                "consumerCode", "brandCode", "clientId");

        // Verify the results
        assertNotNull(result);
        verify(mockIncontrolVehicleMapper).getBindIncontrolVehicle("consumerCode", "clientId");
        verify(mockIncontrolVehicleMapper).selectCarInfoByCarVinListAndBrandCode(anyList(), eq("clientId"));
        verify(mockMappingDataMapper).selectList(any(LambdaQueryWrapperX.class));
    }

    @Test
    public void testGetUserCarServiceList_IncontrolVehicleDOMapperSelectListReturnsNoItems() {
        // Setup - 模拟selectCarInfoByCarVinListAndBrandCode返回空列表
        when(mockIncontrolVehicleMapper.getBindIncontrolVehicle("consumerCode", "brandCode"))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<UserCarServiceListVO> result = incontrolVehicleServiceImplUnderTest.getUserCarServiceList(
                "consumerCode", "brandCode", "brandCode");

        // Verify the results
        assertNotNull(result);
        verify(mockIncontrolVehicleMapper).getBindIncontrolVehicle("consumerCode", "brandCode");
    }

    @Test
    public void testGetUserCarServiceList_SelectCarInfoReturnsNoItems() {
        // Setup - 模拟有车辆VIN但selectCarInfoByCarVinListAndBrandCode返回空
        final List<IncontrolVehicleDO> incontrolVehicleDOS = Lists.newArrayList(IncontrolVehicleDO.builder()
                .carVin("carVin")
                .incontrolId("inControlId")
                .seriesCode("seriesCode")
                .seriesName("seriesName")
                .brandCode("brandCode")
                .brandName("brandName")
                .hobEn("hobEn")
                .productionEn("productionEn")
                .configCode("configCode")
                .configName("configName")
                .modelYear("modelYear")
                .carSystemModel("carSystemModel")
                .bindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockIncontrolVehicleMapper.getBindIncontrolVehicle("consumerCode", "brandCode"))
                .thenReturn(incontrolVehicleDOS);

        // 模拟根据VIN列表和品牌代码查询返回空列表
        when(mockIncontrolVehicleMapper.selectCarInfoByCarVinListAndBrandCode(anyList(), eq("brandCode")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<UserCarServiceListVO> result = incontrolVehicleServiceImplUnderTest.getUserCarServiceList(
                "consumerCode", "brandCode", "brandCode");

        // Verify the results
        assertNotNull(result);
        verify(mockIncontrolVehicleMapper).getBindIncontrolVehicle("consumerCode", "brandCode");
        verify(mockIncontrolVehicleMapper).selectCarInfoByCarVinListAndBrandCode(anyList(), eq("brandCode"));
    }

    @Test
    public void testBuildAndSaveVehicle() {
        // Setup
        final Map<String, PIVIPackageDO> piviPackageDOMap = Map.ofEntries(Map.entry("value", PIVIPackageDO.builder()
                .packageCode("servicePackage")
                .jlrSubscriptionId(0L)
                .iccid("iccid")
                .vin("carVin")
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build()));

        // Configure VehicleDmsDOMapper.selectVehicleDmsDOByCarVinList(...).
        final List<VehicleDmsDO> vehicleDmsDOS = List.of(VehicleDmsDO.builder()
                .carVin("carVin")
                .invoiceDate("invoiceDate")
                .build());
        when(mockVehicleDmsDOMapper.selectVehicleDmsDOByCarVinList(List.of("value"))).thenReturn(vehicleDmsDOS);

        // Configure IncontrolVehicleDOMapper.selectList(...).
        final List<IncontrolVehicleDO> incontrolVehicleDOS = List.of(IncontrolVehicleDO.builder()
                .carVin("carVin")
                .incontrolId("inControlId")
                .brandCode("brandCode")
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .bindTime(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build());
        when(mockIncontrolVehicleMapper.selectList(any(LambdaQueryWrapperX.class))).thenReturn(incontrolVehicleDOS);

        // Configure VehicleModelMasterDataService.findDpList(...).
        final UserDPResultVO userDPResultVO = new UserDPResultVO();
        userDPResultVO.setVin("value");
        userDPResultVO.setBrandCode("brandCode");
        userDPResultVO.setBrandName("brandName");
        userDPResultVO.setModelYear("modelYear");
        userDPResultVO.setConfigCode("configCode");
        final List<UserDPResultVO> userDPResultVOS = List.of(userDPResultVO);
        when(mockVehicleModelMasterDataService.findDpList(List.of("value"))).thenReturn(userDPResultVOS);

        // Run the test
        incontrolVehicleServiceImplUnderTest.buildAndSaveVehicle("inControlId", List.of("value"), piviPackageDOMap);

        // Verify the results
        // Confirm SeriesBrandMappingDataDOService.createSeriesBrandMappingData(...).
        verify(mockIncontrolVehicleMapper).deleteVehicleByIncontrol("inControlId");
        verify(mockIncontrolVehicleMapper).deleteVehicleByCarVinList(List.of("value"));
        verify(mockIncontrolVehicleMapper).insertBatch(anyList());
    }

    @Test
    public void testServiceAddPIVIByCarVin() {
        // Setup
        final Map<String, PIVIPackageDO> piviPackageDOMap = Map.ofEntries(Map.entry("value", PIVIPackageDO.builder()
                .packageCode("servicePackage")
                .jlrSubscriptionId(0L)
                .iccid("iccid")
                .vin("carVin")
                .amaPExpireDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .dmsInvoiceDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                .build()));

        // Configure SubscriptionServiceMapper.selectList(...).
        final List<SubscriptionServiceDO> subscriptionServiceDOS = List.of(SubscriptionServiceDO.builder()
                        .subscriptionId("subscriptionId")
                        .carVin("carVin")
                        .serviceName("serviceName")
                        .servicePackage("servicePackage")
                        .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceType(3)
                        .jlrSubscriptionId(0L)
                        .iccid("iccid")
                        .build(),
                SubscriptionServiceDO.builder()
                        .subscriptionId("subscriptionId")
                        .carVin("value2")
                        .serviceName("serviceName")
                        .servicePackage("servicePackage")
                        .expiryDate(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .expireDateUtc0(LocalDateTime.of(2020, 1, 1, 0, 0, 0))
                        .serviceType(3)
                        .jlrSubscriptionId(0L)
                        .iccid("iccid")
                        .build());
        when(mockSubscriptionServiceMapper.selectList(any(LambdaQueryWrapper.class)))
                .thenReturn(subscriptionServiceDOS);

        when(mockEcpIdUtil.nextIdStr()).thenReturn("subscriptionId");

        // Run the test
        incontrolVehicleServiceImplUnderTest.serviceAddPIVIByCarVin(List.of("value", "value2"), "inControlId", piviPackageDOMap);

        // Verify the results
        verify(mockSubscriptionServiceMapper).insertBatch(anyList());
    }
}
